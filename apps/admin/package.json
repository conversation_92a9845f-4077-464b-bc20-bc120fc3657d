{"name": "admin", "version": "1.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite --port 8082", "dev:local": "vite --mode localapi --port 8082", "dev:staging": "vite --mode staging --port 8082", "build": "vite build", "build:local": "vite build --mode localapi", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest watch"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fontsource/roboto": "^4.5.8", "@hookform/resolvers": "^3.1.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.129", "@mui/material": "^5.12.2", "@mui/x-data-grid": "^6.16.0", "@mui/x-date-pickers": "^6.11.2", "@sentry/browser": "^8.20.0", "@sentry/react": "^7.74.1", "@sentry/vite-plugin": "^2.8.0", "dayjs": "^1.11.9", "lib": "*", "react": "^18.2.0", "react-arborist": "^3.0.2", "react-dom": "^18.2.0", "react-hook-form": "^7.43.9", "react-router-dom": "^6.8.1", "react-use": "^17.4.0", "yup": "^1.1.1"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "jsdom": "^21.1.0", "msw": "^1.1.0", "nanoid": "^5.0.1", "tsconfig": "*", "typescript": "^4.9.5", "vite": "^4.1.4", "viteconfig": "*", "vitest": "^0.29.2"}, "msw": {"workerDirectory": "public"}}