import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Button, Box, Typography, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { EcoisDialog } from '../Dialog';
import { FilterForm } from '../ProjectList/FilterForm';
import { ProjectListDialogTable } from '../ProjectList/ProjectListDialogTable';
import { useFilteredProjects } from '../ProjectList/useFilteredProjects';
import { useProjects } from '../ProjectList/useProjects';
import { useSearchControl } from '../ProjectList/useSearchControl';
import { DatalistLayout } from '@/layout';
import { ClientUserProject, Project } from '@/types';

function useProjectListDialog() {
  const { data, isLoading } = useProjects();
  const { search, dispatch, debouncedSearch, searchStatus, initialState } = useSearchControl();
  const { filteredProjects, tableState } = useFilteredProjects(
    data?.projects ?? [],
    isLoading,
    searchStatus,
    debouncedSearch,
  );

  return {
    projects: data?.projects ?? [],
    filteredProjects,
    tableState,
    search,
    initialState,
    dispatch,
  };
}

type ProjectListDialogProps = {
  clientUserProjects: ClientUserProject[];
  openState: boolean;
  close: () => void;
  onCancel?: () => void;
  onSubmit?: (selectedProjectIdSet: Project['id'][]) => Promise<void>;
};
function ProjectListDialog({
  clientUserProjects,
  openState,
  close,
  onCancel,
  onSubmit,
}: // clientUserId,
// onSuccess,
ProjectListDialogProps) {
  const [isSubmitLoading, setIsSubmitLoading] = useState(false);
  function startLoading() {
    setIsSubmitLoading(true);
  }

  function fulfilled() {
    setIsSubmitLoading(false);
  }

  // フォーム送信時の処理
  async function handleOnFormSubmit() {
    startLoading();
    await onSubmit?.(Array.from(selectedProjectIdSet));
    fulfilled();
  }

  // キャンセル時の処理
  function handleCancel() {
    onCancel?.();
    close();
  }

  const { projects, filteredProjects, search, initialState, dispatch, tableState } =
    useProjectListDialog();
  const projectIdSet = new Set(projects.map((project) => project.id));
  const filteredProjectIdSet = new Set(filteredProjects.map((project) => project.id));

  // チェックボックス制御
  const [selectedProjectIdSet, setSelectedProjectIdSet] = useState<Set<Project['id']>>(new Set());

  // ダイアログが閉じるときのリセット処理
  function reset() {
    setSelectedProjectIdSet(new Set());
    fulfilled();
  }

  // ダイアログが閉じるときのフック
  useEffect(() => {
    openState && reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openState]);

  return (
    <EcoisDialog
      open={openState}
      close={close}
      fullWidth
      maxWidth='xl'
      sx={(theme) => ({
        '.MuiDialog-paper': { height: '100%', minWidth: theme.breakpoints.values.sm + 'px' },
      })}
    >
      <DialogTitle
        component={Stack}
        direction={'row'}
        justifyContent={'space-between'}
        flexWrap={'wrap'}
      >
        <DialogTitle sx={{ padding: 0 }}>権限を付与する案件を選択してください</DialogTitle>
        <Stack
          direction={'row'}
          spacing={1}
          alignItems={'center'}
          justifyContent={'end'}
          flexGrow={1}
          py={(theme) => (theme.breakpoints.down('sm') ? 1 : 0)}
        >
          <Box px={2}>
            <Typography>{selectedProjectIdSet.size}件選択中</Typography>
          </Box>
          <Button variant='outlined' onClick={handleCancel}>
            キャンセル
          </Button>
          <LoadingButton
            loading={isSubmitLoading}
            variant='contained'
            disabled={selectedProjectIdSet.size === 0}
            disableElevation
            onClick={handleOnFormSubmit}
          >
            追加
          </LoadingButton>
        </Stack>
      </DialogTitle>
      <DialogContent sx={{ display: 'flex', overflow: 'visible' }}>
        <DatalistLayout
          sx={(theme) => ({ [theme.breakpoints.down('lg')]: { alignContent: 'baseline' } })}
        >
          {tableState.isEmptyData ? (
            <Typography>登録されている案件は0件です。</Typography>
          ) : (
            <DatalistLayout>
              <DatalistLayout.ControlIsland>
                <FilterForm search={search} dispatch={dispatch} initialState={initialState} />
              </DatalistLayout.ControlIsland>

              <DatalistLayout.DataIsland>
                <ProjectListDialogTable
                  clientUserProjects={clientUserProjects}
                  selectedProjectIdSet={selectedProjectIdSet}
                  setSelectedProjectIdSet={setSelectedProjectIdSet}
                  projectIdSet={projectIdSet}
                  filteredProjectIdSet={filteredProjectIdSet}
                  filteredProjects={filteredProjects}
                  tableState={tableState}
                />
              </DatalistLayout.DataIsland>
            </DatalistLayout>
          )}
        </DatalistLayout>
      </DialogContent>
    </EcoisDialog>
  );
}

export { useProjectListDialog, ProjectListDialog };
export type { ProjectListDialogProps };
