import { useEffectOnce } from 'react-use';
import { useListProjects } from '@/api';
import { ListProjectsQueryParameter } from '@/api/types';

type Props = {
  query?: ListProjectsQueryParameter;
};

function useProjects({ query = {} }: Props = {}) {
  const { data, trigger, isLoading, error } = useListProjects();
  useEffectOnce(() => {
    fetch(query);
  });

  async function fetch(query: ListProjectsQueryParameter = {}) {
    return await trigger({
      queryParameter: query,
    });
  }

  return {
    data,
    fetch,
    isLoading,
    error,
  };
}

export { useProjects };
