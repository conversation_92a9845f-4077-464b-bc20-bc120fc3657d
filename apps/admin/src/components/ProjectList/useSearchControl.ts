import { useReducer, useState, useEffect } from 'react';
import { useDebounce } from 'react-use';

// 初期状態
const initialState: SearchState = {
  id: '',
  name: '',
  locationId: '',
  locationAddress: '',
  locationNull: false,
  isKenesIntegrationAvailable: 'all',
};
export type SearchState = {
  id: string;
  name: string;
  locationId: string;
  locationAddress: string;
  locationNull: boolean;
  isKenesIntegrationAvailable: 'all' | 'available' | 'unavailable';
};

type SearchAction =
  | { type: 'SET_SEARCH'; field: keyof SearchState; value: string | boolean }
  | { type: 'CLEAR_SEARCH' };

// Reducerの実装
function searchReducer(state: SearchState, action: SearchAction): SearchState {
  switch (action.type) {
    case 'SET_SEARCH':
      return {
        ...state,
        [action.field]: action.value,
      };
    case 'CLEAR_SEARCH':
      return initialState; // 初期状態にリセット
    default:
      return state;
  }
}

function useSearchControl() {
  // For searching
  const [search, dispatch] = useReducer(searchReducer, initialState);
  const [isPristine, setIsPristine] = useState(true);
  const [searchStatus, setSearchStatus] = useState<'pending' | 'ready'>('ready');
  const [debouncedSearch, setDebouncedSearch] = useState<SearchState>(search);
  const [ready] = useDebounce(
    () => {
      setDebouncedSearch(search);
      setIsPristine(false);
    },
    500,
    [search],
  );

  const isFinished = ready();

  useEffect(() => {
    if (isFinished) {
      setSearchStatus('ready');
    } else {
      !isPristine && setSearchStatus('pending');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFinished]);

  return {
    search,
    dispatch,
    searchStatus,
    debouncedSearch,
    initialState,
  };
}

export { useSearchControl };
