import { Box, Button, Stack, Typography } from '@mui/material';
import { FormControl, FormControlLabel, Checkbox } from '@mui/material';
import { OutlinedInput, Select, MenuItem, TextField, InputLabel } from '@mui/material';
import { isDeepEqual } from '@mui/x-data-grid/internals';
import { SearchState } from './useSearchControl';

type FilterFormProps = {
  search: SearchState;
  dispatch: React.Dispatch<any>;
  initialState: SearchState;
};

const FilterForm = ({ search, dispatch, initialState }: FilterFormProps) => {
  // Clear location text search
  function handleClearLocationTextSearch() {
    dispatch({ type: 'SET_SEARCH', field: 'locationAddress', value: '' });
    dispatch({ type: 'SET_SEARCH', field: 'locationId', value: '' });
  }
  return (
    <>
      <Stack
        mb={1}
        direction={'row'}
        justifyContent={{ xs: 'flex-start', lg: 'space-between' }}
        alignItems={'center'}
      >
        <Box>
          <Typography variant='body1'>絞り込み検索</Typography>
        </Box>
        <Box>
          <Button
            disabled={isDeepEqual(search, initialState)}
            onClick={() => {
              dispatch({ type: 'CLEAR_SEARCH' });
            }}
          >
            条件をクリア
          </Button>
        </Box>
      </Stack>
      <Stack
        spacing={{
          xs: 1,
          lg: 3,
        }}
        direction={{ lg: 'column', xs: 'row' }}
      >
        <TextField
          size='small'
          label='案件ID'
          placeholder='1 2 3 4 ...'
          name='id'
          value={search.id}
          onChange={(e) => dispatch({ type: 'SET_SEARCH', field: 'id', value: e.target.value })}
        />
        <TextField
          label='案件名'
          size='small'
          name='name'
          placeholder='案件名A 案件名B ...'
          value={search.name}
          onChange={(e) => dispatch({ type: 'SET_SEARCH', field: 'name', value: e.target.value })}
        />
        <FormControl size='small'>
          <InputLabel htmlFor='locationId'>ロケーションID</InputLabel>
          <OutlinedInput
            label='ロケーションID'
            name='locationId'
            placeholder='1 2 3 4 ...'
            value={search.locationId}
            onChange={(e) => {
              dispatch({
                type: 'SET_SEARCH',
                field: 'locationNull',
                value: false,
              }); // ロケーションなしのチェックを外す
              dispatch({
                type: 'SET_SEARCH',
                field: 'locationId',
                value: e.target.value,
              });
            }}
          />
        </FormControl>
        <FormControl size='small'>
          <InputLabel htmlFor='locationAddress'>ロケーションアドレス</InputLabel>
          <OutlinedInput
            label='ロケーションアドレス'
            name='locationAddress'
            placeholder='ABCD1234 XYZ 6789...'
            value={search.locationAddress}
            onChange={(e) => {
              dispatch({
                type: 'SET_SEARCH',
                field: 'locationNull',
                value: false,
              }); // ロケーションなしのチェックを外す
              dispatch({
                type: 'SET_SEARCH',
                field: 'locationAddress',
                value: e.target.value,
              });
            }}
          />
        </FormControl>
        <FormControl size='small'>
          <InputLabel id='isKenesIntegrationAvailable'>KENES連携</InputLabel>
          <Select
            label='isKenesIntegrationAvailable'
            size='small'
            value={search.isKenesIntegrationAvailable}
            defaultValue={'all'}
            onChange={(e) =>
              dispatch({
                type: 'SET_SEARCH',
                field: 'isKenesIntegrationAvailable',
                value: e.target.value,
              })
            }
          >
            <MenuItem value='all'>すべて</MenuItem>
            <MenuItem value='available'>連携中</MenuItem>
            <MenuItem value='unavailable'>未連携</MenuItem>
          </Select>
        </FormControl>
      </Stack>
      <Box
        pl={{
          xs: 0.5,
        }}
        pt={{
          xs: 1,
          lg: 2,
        }}
        mb={{ xs: 2 }}
      >
        <FormControlLabel
          componentsProps={{
            typography: {
              variant: 'body2',
            },
          }}
          control={
            <Checkbox
              size='small'
              checked={search.locationNull}
              onChange={(e) => {
                handleClearLocationTextSearch();
                dispatch({
                  type: 'SET_SEARCH',
                  field: 'locationNull',
                  value: e.target.checked,
                });
              }}
            />
          }
          label='ロケーションなしの案件のみ表示'
        />
      </Box>
    </>
  );
};
export { FilterForm };
