import { TableCell, Typography, Stack, useTheme, Theme } from '@mui/material';
import { ChangeEvent } from 'react';
import { CLCheckbox } from '../ColumnList/ColumnList';
import { EcoisSimpleDataTable } from '../Table';
import type { TableState } from './useFilteredProjects';
import { StatusMarkDot } from '@/components/Datapoint/StatusMark';
import { ClientUserProject, Project } from '@/types';

type ProjectListTableProps = {
  clientUserProjects: ClientUserProject[];
  selectedProjectIdSet: Set<Project['id']>;
  setSelectedProjectIdSet: (projectIdSet: Set<Project['id']>) => void;
  projectIdSet: Set<Project['id']>;
  filteredProjectIdSet: Set<Project['id']>;
  tableState: TableState;
  filteredProjects: Project[];
};

function ProjectListDialogTable({
  clientUserProjects,
  selectedProjectIdSet,
  setSelectedProjectIdSet,
  projectIdSet,
  filteredProjectIdSet,
  tableState,
  filteredProjects,
}: ProjectListTableProps) {
  // Theme
  const theme = useTheme() as Theme;

  function addId(id: Project['id']) {
    setSelectedProjectIdSet(selectedProjectIdSet.union(new Set([id])));
  }
  function removeId(id: Project['id']) {
    setSelectedProjectIdSet(selectedProjectIdSet.difference(new Set([id])));
  }

  function handleCheckOnChange(project: Project) {
    return (event: ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        addId(project.id);
      } else {
        removeId(project.id);
      }
    };
  }

  function handleFullCheckOnChange() {
    if (isAllChecked) {
      setSelectedProjectIdSet(new Set());
    } else {
      setSelectedProjectIdSet(projectIdSet.difference(grantedProjectIdSet));
    }
  }

  function handleListItemOnClick(project: Project) {
    return () => {
      if (selectedProjectIdSet.has(project.id)) {
        removeId(project.id);
      } else {
        addId(project.id);
      }
    };
  }

  //付与済みの案件
  const grantedProjectIdSet = new Set(clientUserProjects?.map((project) => project.id) ?? []);

  const isAllChecked = selectedProjectIdSet.isSupersetOf(
    filteredProjectIdSet.difference(grantedProjectIdSet),
  );

  const isIndeterminate = selectedProjectIdSet.size > 0 && !isAllChecked;

  return (
    <EcoisSimpleDataTable
      heightAdjuster={{
        value: -parseInt(theme.spacing(5).replace('px', ''), 10),
        unit: 'px',
      }}
      data={filteredProjects}
      tableHeadRow={
        <>
          <TableCell sx={{ width: 0 }}>
            <CLCheckbox
              checked={isAllChecked}
              onChange={handleFullCheckOnChange}
              indeterminate={isIndeterminate}
            />
          </TableCell>
          <TableCell sx={{ width: 0, whiteSpace: 'nowrap' }}>案件ID</TableCell>
          <TableCell>案件名</TableCell>
          <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>ロケーションID</TableCell>
          <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>ロケーションアドレス</TableCell>
          <TableCell sx={{ width: '5%', whiteSpace: 'nowrap' }}>KENES連携</TableCell>
        </>
      }
      rowProps={(project) => {
        const granted = grantedProjectIdSet.has(project.id);

        return {
          onClick: granted ? undefined : handleListItemOnClick(project),
          sx: granted ? { opacity: 0.45, pointerEvents: 'none' } : undefined,
        };
      }}
      tableBodyRow={(project) => {
        const granted = grantedProjectIdSet.has(project.id);
        return (
          <>
            <TableCell sx={{ width: 0 }}>
              <CLCheckbox
                onChange={handleCheckOnChange(project)}
                checked={selectedProjectIdSet.has(project.id) || granted}
                disabled={granted}
              />
            </TableCell>
            <TableCell sx={{ width: 0 }}>{project.id}</TableCell>
            <TableCell>{project.name}</TableCell>
            <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>
              {project.locations.length > 0
                ? project.locations.map((location) => location.id).join(',')
                : 'なし'}
            </TableCell>
            <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>
              {project.locations.length > 0
                ? project.locations.map((location) => location.address).join(',')
                : 'なし'}
            </TableCell>
            <TableCell sx={{ width: '5%', whiteSpace: 'nowrap' }}>
              <Stack direction={'row'} alignItems={'center'} spacing={1}>
                <StatusMarkDot isAvailable={project.isKenesIntegrationAvailable || false} />
                <Typography variant={'body2'}>
                  {project.isKenesIntegrationAvailable ? '連携中' : '未連携'}
                </Typography>
              </Stack>
            </TableCell>
          </>
        );
      }}
      isEmpty={tableState.isSearchResultEmpty}
      isProgress={tableState.isTableLoading}
    />
  );
}

export { ProjectListDialogTable };
