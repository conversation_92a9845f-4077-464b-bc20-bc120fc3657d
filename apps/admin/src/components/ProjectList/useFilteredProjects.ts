import { useMemo } from 'react';
import { SearchState } from './useSearchControl';
import { Project } from '@/types';

export type TableState = {
  isEmptyData: boolean;
  isTableLoading: boolean;
  isSearchResultEmpty: boolean;
};

const useFilteredProjects = (
  projects: Project[],
  isLoading: boolean,
  searchStatus: 'pending' | 'ready',
  search: SearchState,
) => {
  const filteredProjects = useMemo(() => {
    const { id, name, locationId, locationAddress, locationNull, isKenesIntegrationAvailable } =
      search;
    return projects.filter((project) => {
      const wordId = id.split(' ');
      const wordName = name.split(' ');
      const wordLocationId = locationId.split(' ');
      const wordLocationAddress = locationAddress.split(' ');
      /* 
        id : 完全一致検索
        name : 部分一致検索
        locationId : 完全一致検索
        locationAddress : 部分一致検索 
        locationNull : ロケーションがないかどうか
      */
      const conditions = {
        id: id === '' || wordId.some((word) => project.id.toString() === word),
        name: name === '' || wordName.every((word) => project.name.includes(word)),
        locationId:
          locationId === '' ||
          wordLocationId.every((word) =>
            project.locations.some((location) => location.id.toString() === word),
          ),
        locationAddress:
          locationAddress === '' ||
          wordLocationAddress.every((word) =>
            project.locations.some((location) => location.address.includes(word)),
          ),
        locationNull: locationNull ? project.locations.length === 0 : true,
        isKenesIntegrationAvailable:
          isKenesIntegrationAvailable === 'all' ||
          (isKenesIntegrationAvailable === 'available' && project.isKenesIntegrationAvailable) ||
          (isKenesIntegrationAvailable === 'unavailable' && !project.isKenesIntegrationAvailable),
      };

      return (
        conditions.id &&
        conditions.name &&
        conditions.locationId &&
        conditions.locationAddress &&
        conditions.locationNull &&
        conditions.isKenesIntegrationAvailable
      );
    });
  }, [projects, search]);

  // テーブルの状態を管理
  const tableState: TableState = useMemo(
    () => ({
      //プロジェクトがない場合
      isEmptyData: projects.length === 0,
      //読み込み中または検索中か
      isTableLoading: isLoading || searchStatus === 'pending',
      //検索結果が0件か
      isSearchResultEmpty: !!projects && filteredProjects.length === 0 && searchStatus === 'ready',
    }),
    [projects, filteredProjects.length, isLoading, searchStatus],
  );

  return {
    tableState,
    filteredProjects,
  };
};

export { useFilteredProjects };
