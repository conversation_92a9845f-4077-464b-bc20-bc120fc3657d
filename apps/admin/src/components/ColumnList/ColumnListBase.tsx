import { ArrowBack, Search } from '@mui/icons-material';
import {
  Box,
  Icon,
  InputAdornment,
  ListItemIcon,
  ListItemText,
  TextField,
  Typography,
} from '@mui/material';
import { ChangeEvent, ReactNode, useEffect, useRef, useState } from 'react';
import {
  ColumnList,
  CLDetailPane,
  CLDetailPaneContent,
  CLDetailPaneTitle,
  ColumnListItem,
  ColumnListItems,
  ColumnListTitle,
  ColumnListTitleLabel,
  CLCheckbox,
} from '../../components/ColumnList/ColumnList';
import { StatusMarkDot } from '../Datapoint/StatusMark';
import { Datapoint, Location, Unit } from '@/types';
type DetailPaneDisplayState = 'collapsed' | 'expanded';

type ColumListProps<T extends Location | Unit | Datapoint> = {
  title: string;
  data: T[] | undefined;
  isLoading: boolean;
  error: Error;
  checkboxSelection?: boolean;
  detailContents?: ReactNode;
  normalStateButton?: ReactNode;
  checkedStateButton?: ReactNode;
  optionMenuButtons?: ReactNode;
  updateSelectedItem?: (item: T) => void;
  onListItemClick?: (item: T) => void;
  onCheckedItemsChange?: (items: number[]) => void;
  topOfListItem?: ReactNode;
  showDatapointStatus?: boolean;
  showID?: boolean;
  useAdvancedSearch?: boolean;
  advancedSearchComponent?: (initial: T[], updateFn: (prev: T[]) => void) => ReactNode;
  listControlButtons?: (item: T) => ReactNode;
};

function ColumnListBase<T extends Location | Unit | Datapoint>({
  title,
  data,
  isLoading,
  checkboxSelection,
  onListItemClick,
  detailContents,
  normalStateButton,
  checkedStateButton,
  optionMenuButtons,
  topOfListItem,
  showID = false,
  showDatapointStatus = false,
  onCheckedItemsChange,
  selectedId,
  onUpdateSelectedId,
  useAdvancedSearch = false,
  advancedSearchComponent,
  listControlButtons,
}: ColumListProps<T> & {
  selectedId: number | null;
  onUpdateSelectedId: (id: number) => void;
}) {
  const [checkedItems, setCheckedItems] = useState<number[]>([]);

  const isItemSelected = selectedId !== null && data?.length !== 0;
  const selectedItem = data?.find(({ id }) => id === selectedId);

  const [detailPaneDisplayState, setDetailPaneDisplayState] =
    useState<DetailPaneDisplayState>('collapsed');

  const handleCheckboxChange = (target: T) => (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newCheckedItems = [...checkedItems, target.id];
      setCheckedItems(newCheckedItems);
      onCheckedItemsChange && onCheckedItemsChange(newCheckedItems);
    } else {
      const newCheckedItems = checkedItems.filter((id) => id !== target.id);
      setCheckedItems(newCheckedItems);
      onCheckedItemsChange && onCheckedItemsChange(newCheckedItems);
    }
  };

  // データが変更されたら、チェックボックスのチェックを外す
  useEffect(() => {
    setCheckedItems([]);
  }, [data]);

  const handleCheckAllChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      data && setCheckedItems(data.map((item) => item.id));
      data && onCheckedItemsChange && onCheckedItemsChange(data.map((item) => item.id));
    } else {
      setCheckedItems([]);
      onCheckedItemsChange && onCheckedItemsChange([]);
    }
  };

  const isChecked = checkedItems.length > 0;
  const isAllChecked = checkedItems.length === (data ? data.length : null) && data?.length !== 0;
  const isIndeterminate = checkedItems.length > 0 && checkedItems.length < (data ? data.length : 0);

  const [filteredData, setFilteredData] = useState(data ?? []);

  const timeoutRef = useRef<NodeJS.Timeout>();

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const searchText = event.target.value;
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      const filtered = data?.filter((item) => {
        return item.id.toString().includes(searchText) || item.name.includes(searchText);
      });
      setFilteredData(filtered ?? []);
    }, 500);
  };

  useEffect(() => {
    setFilteredData(data ?? []);
  }, [data]);

  return (
    <>
      {
        <ColumnList>
          <ColumnListTitle>
            <ColumnListTitleLabel>
              {checkboxSelection && (
                <CLCheckbox
                  checked={isAllChecked}
                  onChange={handleCheckAllChange}
                  indeterminate={isIndeterminate}
                />
              )}
              {!isChecked
                ? `${title}(${data?.length ?? 0})件`
                : `選択済み(${checkedItems.length})件`}
            </ColumnListTitleLabel>
            {!(checkedItems.length > 0) ? <>{normalStateButton}</> : <>{checkedStateButton}</>}
          </ColumnListTitle>
          <Box p={1}>
            {useAdvancedSearch === false ? (
              <TextField
                size='small'
                sx={{ width: '100%' }}
                variant='outlined'
                placeholder='検索'
                onChange={handleInputChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            ) : (
              advancedSearchComponent && data && advancedSearchComponent(data, setFilteredData)
            )}
          </Box>
          <ColumnListItems isLoading={isLoading}>
            <>
              {filteredData && filteredData.length > 0 && topOfListItem}
              {filteredData ? (
                filteredData.length === 0 ? (
                  <ColumnListItem>{title}のデータがありません</ColumnListItem>
                ) : (
                  filteredData &&
                  filteredData.map((item) => (
                    <ColumnListItem
                      selected={selectedId === item.id}
                      key={item.id}
                      secondaryAction={listControlButtons && listControlButtons(item)}
                      onClick={() => {
                        onUpdateSelectedId(item.id);
                        setDetailPaneDisplayState('expanded');
                        onListItemClick && onListItemClick(item);
                      }}
                    >
                      {checkboxSelection && (
                        <ListItemIcon
                          style={{ display: 'flex', alignItems: 'center' }}
                          sx={{
                            minWidth: '46px',
                          }}
                        >
                          <CLCheckbox
                            checked={checkedItems.includes(item.id)} //  TODO ここでチェックされているかどうかを判定
                            onChange={handleCheckboxChange(item)}
                            sx={(theme) => ({
                              paddingRight: theme.spacing(0.7),
                            })}
                          />
                          {showDatapointStatus && 'isAvailable' in item && (
                            <StatusMarkDot isAvailable={item.isAvailable} />
                          )}
                        </ListItemIcon>
                      )}
                      <ListItemText
                        sx={{ margin: 0 }}
                        secondary={
                          showDatapointStatus &&
                          'address' in item &&
                          'terminalId' in item &&
                          'deviceNo' in item && (
                            <Typography sx={{ fontSize: '11px' }}>
                              {`${item.address} / p${item.terminalId} ${
                                item.deviceNo ? '/ U' + item.deviceNo : ''
                              }`}
                            </Typography>
                          )
                        }
                      >
                        {showID && 'no' in item && item.no.toString().padStart(2, '0')} {item.name}
                      </ListItemText>
                    </ColumnListItem>
                  ))
                )
              ) : (
                <ColumnListItem>
                  <Icon>
                    <ArrowBack />
                  </Icon>
                  選択してください
                </ColumnListItem>
              )}
            </>
          </ColumnListItems>
          <CLDetailPane isExpanded={isItemSelected && detailPaneDisplayState === 'expanded'}>
            <CLDetailPaneTitle
              isControllerShown={isItemSelected}
              togglePane={() =>
                setDetailPaneDisplayState((prev) =>
                  prev === 'collapsed' ? 'expanded' : 'collapsed',
                )
              }
              isExpanded={detailPaneDisplayState === 'expanded'}
              label={selectedItem?.name || `${title}詳細`}
            >
              {/* OptionMenuButtons */}
              {optionMenuButtons}
            </CLDetailPaneTitle>
            <CLDetailPaneContent
              isExpanded={isItemSelected && detailPaneDisplayState === 'expanded'}
            >
              {detailContents}
            </CLDetailPaneContent>
          </CLDetailPane>
        </ColumnList>
      }
    </>
  );
}

export { ColumnListBase };
