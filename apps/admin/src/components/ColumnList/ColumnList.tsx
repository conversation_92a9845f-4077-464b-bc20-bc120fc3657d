import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

import {
  Box,
  Button,
  ButtonProps,
  Checkbox,
  CheckboxProps,
  CircularProgress,
  Divider,
  ExtendButtonBase,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemButtonProps,
  ListItemIcon,
  ListItemProps,
  ListItemText,
  Menu,
  MenuItem,
  MenuItemProps,
  MenuItemTypeMap,
  MenuList,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import {
  Children,
  MouseEventHandler,
  ReactNode,
  cloneElement,
  isValidElement,
  useState,
} from 'react';
import { PropsWithChildren } from 'react';
import { callAll } from '../../lib';
import { Label } from '../Display';

export type DetailPaneDisplayState = 'collapsed' | 'expanded';

function ColumnList({ children }: { children: ReactNode }) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        // height: '80vh',
      }}
    >
      {children}
    </Box>
  );
}

function ColumnListTitle({ children }: { children: ReactNode }) {
  return (
    <Box>
      <Stack px={2} py={1} direction={'row'} alignItems={'center'} justifyContent={'space-between'}>
        {children}
      </Stack>
      <Divider />
    </Box>
  );
}

function ColumnListTitleLabel({ children }: PropsWithChildren) {
  return <Typography variant='subtitle1'>{children}</Typography>;
}

function ColumnListTitleButton({ children, ...props }: PropsWithChildren<ButtonProps>) {
  return (
    <Box>
      <Button size='small' variant={'contained'} disableElevation {...props}>
        {children}
      </Button>
    </Box>
  );
}

function ColumnListItems({
  children,
  isLoading,
}: PropsWithChildren<{
  isLoading?: boolean;
}>) {
  if (isLoading) {
    return (
      <Box px={2} flex={1}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <List sx={{ flex: 1, overflow: 'auto', position: 'relative', zIndex: 0 }}>{children}</List>
  );
}

function ColumnListItem({
  children,
  secondaryAction,
  onClick = () => void 0,
  ...props
}: PropsWithChildren<ListItemButtonProps & ListItemProps>) {
  return (
    <ListItem
      disableGutters
      disablePadding
      dense
      secondaryAction={secondaryAction}
      sx={{
        '.MuiListItemSecondaryAction-root': {
          visibility: 'hidden',
          paddingRight: 1,
        },
        '&:hover': {
          '.MuiListItemSecondaryAction-root': {
            visibility: 'visible',
          },
        },
      }}
    >
      <ListItemButton onClick={(event) => callAll(onClick)(event)} {...props}>
        {children}
      </ListItemButton>
    </ListItem>
  );
}

function CLDetailPane({ children, isExpanded }: PropsWithChildren<{ isExpanded: boolean }>) {
  return (
    <Paper
      elevation={0}
      square
      sx={[
        isExpanded && {
          flex: 1,
          maxHeight: '50%',
        },
      ]}
    >
      {children}
    </Paper>
  );
}
function CLDetailPaneTitle({
  children,
  label,
  togglePane = () => undefined,
  isExpanded,
  isControllerShown,
}: PropsWithChildren<{
  label: string;
  togglePane: () => void;
  isExpanded: boolean;
  isControllerShown: boolean;
}>) {
  return (
    <Box>
      <Divider />
      <Stack direction={'row'} alignItems='center' justifyContent={'space-between'}>
        {/* 
        複数の子要素を持ったとき -> 子要素配列を検索して該当のコンポーネントを描画
        1つの子要素を持ったとき -> childrenがvalidかを判定したあと、該当のコンポーネントかどうかを判定して描画
        */}
        {/* {Array.isArray(children)
          ? children.find((child) => child.type === ColumnListDetailPaneTitleLabel)
          : isValidElement(children) &&
            children.type === ColumnListDetailPaneTitleLabel &&
            children} */}
        <CLDetailPaneTitleLabel>{label}</CLDetailPaneTitleLabel>
        <Box flex={1} /> {/* Spacer  */}
        {isControllerShown && (
          <>
            {children}
            <Box>
              <IconButton
                sx={{
                  marginRight: 2,
                  marginLeft: 2,
                }}
                onClick={() => {
                  togglePane();
                }}
              >
                {isExpanded ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />}
              </IconButton>
            </Box>
          </>
        )}
      </Stack>
      <Divider />
    </Box>
  );
}

function CLDetailPaneTitleLabel({ children }: PropsWithChildren) {
  return (
    <Typography px={2} py={1} variant={'subtitle1'}>
      {children}
    </Typography>
  );
}

function CLDetailPaneTitleButton({
  children,
  label,
}: PropsWithChildren<{
  label: string;
}>) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box>
      <Button
        variant='outlined'
        size='small'
        disableElevation
        onClick={(e) => {
          handleClick(e);
        }}
      >
        {label}
        <ArrowDropDownIcon />
      </Button>
      <Menu
        id='basic-menu'
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <MenuList
          sx={{
            width: 120,
          }}
          dense
        >
          {/* 
          - MenuItemをChildrenとして持つ
          - MenuItemのonClickをhandleClickに置き換える
          */}
          {Children.map(children, (child) => {
            if (!isValidElement<any>(child)) return null;
            const handleClick = callAll(handleClose, child.props.onClick);
            const props: ExtendButtonBase<MenuItemTypeMap> = {
              ...child.props,
              onClick: handleClick,
            };
            return cloneElement(child, props);
          })}
        </MenuList>
      </Menu>
    </Box>
  );
}

function CLDetailPaneContent({ children, isExpanded }: PropsWithChildren<{ isExpanded: boolean }>) {
  return (
    <>
      {isExpanded && (
        <Box px={2} py={2}>
          <Grid container spacing={2}>
            {children}
          </Grid>
        </Box>
      )}
    </>
  );
}
function CLDetailPaneContentItem({ children }: PropsWithChildren) {
  return (
    <Grid item xs={6}>
      <Stack>{children}</Stack>
    </Grid>
  );
}

function CLDetailPaneContentLabel({ children }: PropsWithChildren) {
  return <Label>{children}</Label>;
}
function CLDetailPaneContentValue({ children }: PropsWithChildren) {
  return (
    <Typography variant='body2' textOverflow={'ellipsis'} overflow={'hidden'}>
      {children}
    </Typography>
  );
}

function CLCheckbox({ ...props }: CheckboxProps) {
  return (
    <Checkbox
      sx={{ paddingTop: 0, paddingBottom: 0 }}
      edge='start'
      tabIndex={-1}
      disableRipple
      {...props}
    />
  );
}

interface CLMenuItemProps extends Omit<MenuItemProps, 'onClick'> {
  onClick: MouseEventHandler<HTMLLIElement>;
  icon: React.ReactElement;
  label: string;
}

function CLMenuItem({ onClick, icon, label, color, ...props }: PropsWithChildren<CLMenuItemProps>) {
  return (
    <MenuItem onClick={(event) => callAll(onClick)(event)} {...props}>
      <ListItemIcon>{cloneElement(icon, { color })}</ListItemIcon>
      <ListItemText primaryTypographyProps={{ color }}>{label}</ListItemText>
    </MenuItem>
  );
}

export {
  ColumnList,
  ColumnListTitle,
  ColumnListTitleLabel,
  ColumnListTitleButton,
  ColumnListItem,
  ColumnListItems,
  CLDetailPane,
  CLDetailPaneTitle,
  CLDetailPaneTitleLabel,
  CLDetailPaneTitleButton,
  CLDetailPaneContent,
  CLDetailPaneContentItem,
  CLDetailPaneContentLabel,
  CLDetailPaneContentValue,
  CLCheckbox,
  CLMenuItem,
};
