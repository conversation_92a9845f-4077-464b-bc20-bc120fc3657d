import { Stack, Divider } from '@mui/material';
import { createContext, PropsWithChildren, useState, useMemo, useContext } from 'react';
import { useListLocation, useListUnits, useListDatapoints } from '@/api';
import { Datapoint, Unit, Location } from '@/types';

// build provider for column components

type fetchFunction = (ids: number[]) => void;

type ContextType<T> = {
  data: T[] | undefined;
  emptyData: () => void;
  isLoading: boolean;
  error: any;
  fetch: fetchFunction;
  selectedId: number | null;
  selectedItem: T | null;
  updateSelectedId: (id: number | null) => void;
  reset: () => void;
};

const ColumnContext = createContext<{
  location: ContextType<Location>;
  unit: ContextType<Unit>;
  datapoint: ContextType<Datapoint>;
} | null>(null);

function MultiColumnView({ children }: PropsWithChildren) {
  const {
    data: locationData,
    isLoading: isLocationLoading,
    error: locationError,
    trigger: locationTrigger,
    reset: locationReset,
  } = useListLocation();

  const {
    data: unitData,
    isLoading: isUnitLoading,
    error: unitError,
    trigger: unitTrigger,
    reset: unitReset,
  } = useListUnits();

  const {
    data: datapointData,
    isLoading: isDatapointLoading,
    error: datapointError,
    trigger: datapointTrigger,
    reset: datapointRest,
  } = useListDatapoints();

  const [selectedLocationItemId, setSelectedLocationItemId] = useState<number | null>(null);
  function updateSelectedLocationItemId(id: number | null) {
    resetUnit();
    resetDatapoint();
    setSelectedLocationItemId(id);
  }

  const [selectedUnitItemId, setSelectedUnitItemId] = useState<number | null>(null);
  function updateSelectedUnitItemId(id: number | null) {
    resetDatapoint();
    setSelectedUnitItemId(id);
  }

  const [selectedDatapointItemId, setSelectedDatapointItemId] = useState<number | null>(null);
  function updateSelectedDatapointItemId(id: number | null) {
    setSelectedDatapointItemId(id);
  }

  const selectedLocationItem = useMemo(
    () => locationData?.locations.find((item) => item.id === selectedLocationItemId) || null,
    [locationData, selectedLocationItemId],
  );

  const selectedUnitItem = useMemo(
    () => unitData?.units.find((item) => item.id === selectedUnitItemId) || null,
    [unitData, selectedUnitItemId],
  );

  const selectedDatapointItem = useMemo(
    () => datapointData?.datapoints.find((item) => item.id === selectedDatapointItemId) || null,
    [datapointData, selectedDatapointItemId],
  );

  function fetchLocationData(locationIds: number[]) {
    locationTrigger(
      {
        queryParameter: { locationIds },
      },
      {
        revalidate: true,
      },
    );
  }

  function fetchUnitByLocationId(locationIds: number[]) {
    unitTrigger({ queryParameter: { locationIds } });
  }

  function fetchDatapointByUnitId(unitIds: number[]) {
    datapointTrigger({ queryParameter: { unitIds } });
  }

  function resetLocation() {
    locationReset();
    updateSelectedLocationItemId(null);
  }

  function resetUnit() {
    unitReset();
    updateSelectedUnitItemId(null);
  }

  function resetDatapoint() {
    datapointRest();
    updateSelectedDatapointItemId(null);
  }

  const value = {
    location: {
      data: locationData?.locations,
      isLoading: isLocationLoading,
      error: locationError,
      fetch: fetchLocationData,
      emptyData: locationReset,
      selectedId: selectedLocationItemId,
      selectedItem: selectedLocationItem,
      updateSelectedId: updateSelectedLocationItemId,
      reset: resetLocation,
    },
    unit: {
      data: unitData?.units,
      isLoading: isUnitLoading,
      error: unitError,
      fetch: fetchUnitByLocationId,
      emptyData: unitReset,
      selectedId: selectedUnitItemId,
      selectedItem: selectedUnitItem,
      updateSelectedId: updateSelectedUnitItemId,
      reset: resetUnit,
    },
    datapoint: {
      data: datapointData?.datapoints,
      isLoading: isDatapointLoading,
      error: datapointError,
      fetch: fetchDatapointByUnitId,
      emptyData: datapointRest,
      selectedId: selectedDatapointItemId,
      selectedItem: selectedDatapointItem,
      updateSelectedId: updateSelectedDatapointItemId,
      reset: resetDatapoint,
    },
  };

  const contextValue = useMemo(
    () => value,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      locationData,
      unitData,
      datapointData,
      selectedLocationItemId,
      selectedUnitItemId,
      selectedDatapointItemId,
    ],
  );

  return (
    <ColumnContext.Provider value={contextValue}>
      <Stack
        direction={'row'}
        divider={<Divider orientation='vertical' flexItem />}
        sx={{
          height: '100%',
        }}
      >
        {children}
      </Stack>
    </ColumnContext.Provider>
  );
}

function useMultiColumnViewContext() {
  const context = useContext(ColumnContext);
  if (!context) {
    throw new Error('useMultiColumnViewContext must be used within a MultiColumnView');
  }

  return context;
}

export { MultiColumnView, useMultiColumnViewContext };
