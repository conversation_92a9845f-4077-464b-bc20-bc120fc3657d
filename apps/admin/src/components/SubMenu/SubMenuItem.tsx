import { ListItem, ListItemButton, ListItemText } from '@mui/material';
import { NavLink } from 'react-router-dom';

interface ListItemLinkProps {
  primary: string;
  to: string;
}

function SubMenuItem({ primary, to }: ListItemLinkProps) {
  return (
    <ListItem
      disablePadding
      disableGutters
      style={{
        borderRadius: '100px 0 0 100px',
        overflow: 'hidden',
      }}
    >
      <NavLink
        to={to}
        style={({ isActive }) => ({
          width: '100%',
          backgroundColor: isActive ? 'rgba(0,0,0,0.08)' : 'transparent',
          textDecoration: 'none',
        })}
      >
        <ListItemButton disableRipple>
          <ListItemText
            primary={primary}
            primaryTypographyProps={{ color: (theme) => theme.palette.text.primary }}
          />
        </ListItemButton>
      </NavLink>
    </ListItem>
  );
}

export { SubMenuItem };
