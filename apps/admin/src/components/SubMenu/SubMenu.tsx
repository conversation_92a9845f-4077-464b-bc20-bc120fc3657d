import { List } from '@mui/material';
import { PropsWithChildren } from 'react';
import { useLayoutConstants } from '@/layout';

function SubMenu({ children }: PropsWithChildren) {
  const { component } = useLayoutConstants();

  return (
    <List
      dense
      disablePadding
      sx={{
        width: component.subMenu.width,
      }}
    >
      {children}
    </List>
  );
}

export { SubMenu };
