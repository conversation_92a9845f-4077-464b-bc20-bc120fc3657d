import { ArrowOutward } from '@mui/icons-material';
import { Paper, Box, Typography, Button, Link } from '@mui/material';
import { Project } from '@/types';

type UtilityRegistrationProps = { projectId: Project['id'] };
function UtilityRegistration({ projectId }: UtilityRegistrationProps) {
  return (
    <Paper variant={'outlined'}>
      <Box p={2}>
        <Typography variant={'body1'} mb={1}>
          速報値の設定を行うには、まずユーティリティを登録してください。
        </Typography>
        <Button
          variant={'contained'}
          disableElevation
          LinkComponent={Link}
          href={`/projects/${projectId}/utility`}
        >
          ユーティリティを登録 <ArrowOutward fontSize={'small'} />
        </Button>
      </Box>
    </Paper>
  );
}
export { UtilityRegistration };
