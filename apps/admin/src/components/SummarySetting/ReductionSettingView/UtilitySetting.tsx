import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Equation, HeaderHidden, HeaderVisible } from '../components';
import { Utility } from '@/types';

type UtilitySettingProps = { utilities: Utility[] };
function UtilitySetting({ utilities }: UtilitySettingProps) {
  const visibleUtilities = utilities
    .filter((utility) => utility.isEnableSummary === true)
    .sort((a, b) => a.order - b.order);
  const hiddenUtilities = utilities
    .filter((utility) => utility.isEnableSummary === false)
    .sort((a, b) => a.order - b.order);

  return (
    <Box p={2}>
      <HeaderVisible />
      <TableContainer
        component={Paper}
        variant={'outlined'}
        sx={(theme) => ({
          maxWidth: theme.breakpoints.values.sm,
        })}
      >
        {visibleUtilities.length !== 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ユーティリティ名</TableCell>
                <TableCell>削減量の値</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {visibleUtilities.map((utility) => (
                <TableRow key={utility.id}>
                  <TableCell width={'40%'}>{utility.name}</TableCell>
                  <TableCell width={'60%'}>
                    <Equation nodeId={utility.nodeId} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Typography variant={'body1'} p={2}>
            アイテムはありません
          </Typography>
        )}
      </TableContainer>
      <HeaderHidden mt={3} />
      <TableContainer
        component={Paper}
        variant={'outlined'}
        sx={(theme) => ({
          maxWidth: theme.breakpoints.values.sm,
        })}
      >
        {hiddenUtilities.length !== 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ユーティリティ名</TableCell>
                <TableCell>削減量の値</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {hiddenUtilities.map((utility) => (
                <TableRow key={utility.id}>
                  <TableCell>{utility.name}</TableCell>
                  <TableCell>
                    <Equation nodeId={utility.nodeId} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Typography variant={'body1'} p={2}>
            アイテムはありません
          </Typography>
        )}
      </TableContainer>
    </Box>
  );
}
export { UtilitySetting };
