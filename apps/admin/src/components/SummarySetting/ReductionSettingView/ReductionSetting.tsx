import { Button, Box, Typography, LinearProgress, Link } from '@mui/material';
import { useEffectOnce } from 'react-use';
import { UtilityRegistration } from './UtilityRegistration';
import { UtilitySetting } from './UtilitySetting';
import { useListUtilities } from '@/api';
import { InfoCard, InfoCardHeader, InfoCardContent } from '@/components/InfoCard';
import { Project } from '@/types';

function ReductionSetting({ projectId }: { projectId: Project['id'] }) {
  const { data, isLoading, trigger } = useListUtilities();

  useEffectOnce(() => {
    trigger({
      queryParameter: {
        projectIds: [projectId],
      },
    });
  });

  return (
    <>
      <InfoCard>
        {isLoading && (
          <LinearProgress
            style={{
              position: 'absolute',
              height: '4px',
              width: '100%',
            }}
          />
        )}
        <InfoCardHeader
          actions={
            <Button variant={'contained'} disableElevation LinkComponent={Link} href='./edit'>
              編集
            </Button>
          }
        >
          速報値(削減量)の設定
        </InfoCardHeader>
        <InfoCardContent>
          <Box mb={2}>
            <Typography variant='body2'>
              各ユーティリティの速報値の表示設定や、削減量の計算方法を定義します。
            </Typography>
          </Box>
          {data?.utilities?.length === 0 ? (
            <UtilityRegistration projectId={projectId} />
          ) : (
            data?.utilities && <UtilitySetting utilities={data?.utilities} />
          )}
        </InfoCardContent>
      </InfoCard>
    </>
  );
}
export { ReductionSetting };
