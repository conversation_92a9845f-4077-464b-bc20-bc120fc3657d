import { Chip, CircularProgress, Stack, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useReadNode } from '@/api';
import { Node } from '@/types';

type EquationProps = { nodeId: Node['id'] | null; nodeName?: string };
function Equation({ nodeId, nodeName }: EquationProps) {
  const { data, trigger, error, isLoading } = useReadNode();

  useEffect(() => {
    if (nodeId === null || nodeId === undefined) return;
    trigger({
      urlParameter: {
        nodeId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodeId]);

  if (nodeId === null) return <StandardChip />;
  if (error) return <ErrorChip />;

  const name = data?.node?.name ?? nodeName;

  return (
    <>
      {isLoading && <CircularProgress />}
      {data?.node && (
        <Stack direction={'row'} alignItems={'center'} spacing={1}>
          <StandardChip />
          <Minus /> <NodeNameChip label={`"${name}"の計測値`} />
        </Stack>
      )}
    </>
  );
}

function StandardChip() {
  return <Chip variant={'outlined'} label='基準値' color={'success'} size='small' />;
}

function NodeNameChip({ label }: { label: string }) {
  return <Chip variant={'outlined'} label={label} color={'info'} size='small' />;
}

function ErrorChip() {
  return (
    <Chip variant={'outlined'} label='ノードの取得に失敗しました' color={'error'} size='small' />
  );
}

function Minus() {
  return <Typography variant='body2'>-</Typography>;
}

export { Equation };
