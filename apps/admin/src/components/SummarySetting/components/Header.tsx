import { Visibility, VisibilityOff } from '@mui/icons-material';
import { Stack, StackProps, Typography, TypographyProps } from '@mui/material';
import { PropsWithChildren } from 'react';

function Base({ children, ...props }: PropsWithChildren<StackProps>) {
  return (
    <Stack direction={'row'} spacing={1} mb={1} alignItems={'center'} pl={0.5} {...props}>
      {children}
    </Stack>
  );
}

function TextBase({ children, ...props }: PropsWithChildren<TypographyProps>) {
  return (
    <Typography variant={'body1'} mb={1} {...props}>
      {children}
    </Typography>
  );
}

type HeaderProps = {
  textProps?: TypographyProps;
} & StackProps;

function HeaderVisible({ textProps, ...props }: HeaderProps) {
  return (
    <Base {...props}>
      <Visibility fontSize='small' />
      <TextBase {...textProps}>表示(上から順)</TextBase>
    </Base>
  );
}

function HeaderHidden({ textProps, ...props }: HeaderProps) {
  return (
    <Base {...props}>
      <VisibilityOff fontSize='small' />
      <TextBase {...textProps}>非表示</TextBase>
    </Base>
  );
}

export { HeaderVisible, HeaderHidden };
