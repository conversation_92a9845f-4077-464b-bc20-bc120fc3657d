import { HighlightOff } from '@mui/icons-material';
import { Box, Button, CircularProgress, IconButton, Link, Stack, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useReadNode } from '@/api';
import { Node } from '@/types';

type NodeCellItemProps = {
  nodeId: Node['id'] | null;
  onSelectClick?: () => void;
  onDeleteClick?: () => void;
};
function NodeCellItem({ nodeId, onSelectClick, onDeleteClick }: NodeCellItemProps) {
  const { data, trigger, error, isLoading } = useReadNode();

  useEffect(() => {
    if (nodeId === null || nodeId === undefined) return;
    trigger({
      urlParameter: {
        nodeId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodeId]);

  function handleNodeSelectClick() {
    onSelectClick?.();
  }

  function handleNodeDeleteClick() {
    onDeleteClick?.();
  }

  if (isLoading) {
    return <CircularProgress />;
  }

  if (nodeId === null) {
    return (
      <Box>
        {error && (
          <Typography variant={'caption'} color={'error'} display={'block'}>
            ノードの取得に失敗(nodeId:{nodeId})
          </Typography>
        )}
        <Button onClick={handleNodeSelectClick}>ノード選択</Button>
      </Box>
    );
  }

  return (
    <>
      <Stack direction={'row'} spacing={1} alignItems={'center'}>
        <Link
          variant={'body2'}
          onClick={handleNodeSelectClick}
          color={'inherit'}
          sx={{
            '&:hover': {
              opacity: 0.6,
            },
          }}
        >
          {data?.node?.name}
        </Link>
        <IconButton color='error' size='small' onClick={handleNodeDeleteClick}>
          <HighlightOff />
        </IconButton>
      </Stack>
    </>
  );
}
export { NodeCellItem };
