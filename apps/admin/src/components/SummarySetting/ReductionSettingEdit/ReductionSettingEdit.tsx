import {
  useSensor,
  useSensors,
  PointerSensor,
  DragEndEvent,
  DndContext,
  closestCenter,
  DragOverlay,
  DragStartEvent,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { SortableContext, arrayMove, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { VisibilityOff } from '@mui/icons-material';
import {
  Paper,
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
} from '@mui/material';
import { useCallback, useEffect, useState } from 'react';

import { Equation, HeaderHidden, HeaderVisible } from '../components';
import { DisplayToggleCellItem } from './DisplayToggleCellItem';
import { NodeCellItem } from './NodeCellItem';
import { SortableRow } from './SortableRow';
import { useProjectNodeDialog } from '@/components/ProjectNodeDialog';
import { Node, Project, Utility } from '@/types';

type ReductionSettingEditProps = {
  utilities: Utility[];
  projectId: Project['id'];
  onChange?: (utilities: Utility[]) => void;
};
function ReductionSettingEdit({ utilities, projectId, onChange }: ReductionSettingEditProps) {
  const [sortableUtilities, setSortableUtilities] = useState(
    enumerate(utilities), // orderを1から始まるようにする
  );

  useEffect(() => {
    onChange?.(sortableUtilities);
  }, [onChange, sortableUtilities]);

  function enumerate(utilities: Utility[]): Utility[] {
    return [...utilities].map((utility, i) => ({ ...utility, order: i + 1 }));
  }

  const sensors = useSensors(useSensor(PointerSensor));

  const [activeId, setActiveId] = useState<UniqueIdentifier>();

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    //ドラッグしたリソースのid
    const id = active.id;
    setActiveId(id);
  };

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      if (over) {
        const oldIndex = sortableUtilities.findIndex((utility) => utility.id === active.id);
        const newIndex = sortableUtilities.findIndex((utility) => utility.id === over.id);
        const movedUtility = arrayMove(sortableUtilities, oldIndex, newIndex);
        setSortableUtilities(enumerate(movedUtility));
      }
    },
    [sortableUtilities],
  );

  function OverlayRow({ utility }: { utility: Utility }) {
    return (
      <SortableRow overlay utility={utility}>
        <TableCell width='1px' component={Box}>
          {utility.order}
        </TableCell>
        <TableCell
          width='1px'
          component={Box}
          sx={{
            whiteSpace: 'nowrap',
          }}
        >
          {utility.name}
        </TableCell>
      </SortableRow>
    );
  }

  /**
   * 表示状態切替時に、表示順を振りなおす
   * - isEnableSummaryがtrueを先頭から並べる。順番はorder順
   * - isEnableSummaryがfalseを、その後に並べる。順番はorder順
   * - 先頭から1ずつ順番を振りなおす
   */
  function reOrderUtilities(utilities: Utility[]): Utility[] {
    const sortedUtilitiesWithOrderAndEnableSummary = utilities.filter(
      ({ isEnableSummary }) => isEnableSummary,
    );
    const sortedUtilitiesWithOrderAndDisableSummary = utilities.filter(
      ({ isEnableSummary }) => !isEnableSummary,
    );

    const reorderedUtilities = enumerate([
      ...sortedUtilitiesWithOrderAndEnableSummary,
      ...sortedUtilitiesWithOrderAndDisableSummary,
    ]);

    return reorderedUtilities;
  }

  const visibleUtilities = sortableUtilities.filter(({ isEnableSummary }) => isEnableSummary);
  const hiddenUtilities = sortableUtilities.filter(({ isEnableSummary }) => !isEnableSummary);

  function toggleVisibility(utility: Utility, visibility: boolean) {
    setSortableUtilities((prev) => {
      // get rid of the utility from the array
      const filteredUtilities = prev.filter((prevUtility) => prevUtility.id !== utility.id);
      // add the utility to the end of the array
      const reorderedUtilities = [
        ...filteredUtilities,
        { ...utility, isEnableSummary: !visibility },
      ];
      return reOrderUtilities(reorderedUtilities);
    });
  }

  function EmptyRow() {
    return (
      <TableRow>
        <TableCell colSpan={5} align='center' height={'100px'}>
          アイテムがありません
        </TableCell>
      </TableRow>
    );
  }

  const {
    ProjectNodeDialog,
    open,
    props: projectNodeDialogProps,
  } = useProjectNodeDialog({
    projectId,
  });

  function handleNodeSelectClick(utility: Utility) {
    open(utility);
  }

  function handleNodeSelectSubmit(node: Node, utility: Utility) {
    //ノードを選択した時に、ノードのidを更新する
    setSortableUtilities((prev) => {
      return prev.map((prevUtility) => {
        if (prevUtility.id === utility.id) {
          return { ...prevUtility, nodeId: node.id };
        }
        return prevUtility;
      });
    });
  }

  function handleNodeDeleteClick(utility: Utility) {
    setSortableUtilities((prev) => {
      return prev.map((prevUtility) => {
        if (prevUtility.id === utility.id) {
          return { ...prevUtility, nodeId: null };
        }
        return prevUtility;
      });
    });
  }

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis, restrictToWindowEdges, restrictToParentElement]}
        autoScroll={false}
      >
        <Paper variant='outlined'>
          <Box p={2} pb={4}>
            <HeaderVisible />
            <SortableContext
              items={visibleUtilities.map((utility) => utility.id)}
              strategy={verticalListSortingStrategy}
            >
              <Paper
                variant='outlined'
                sx={{
                  overflowX: 'auto',
                }}
              >
                <Table>
                  <SummaryTableHead />
                  <TableBody>
                    {visibleUtilities.length === 0 && <EmptyRow />}
                    {visibleUtilities.map((utility) => (
                      <SortableRow key={utility.id} utility={utility}>
                        <TableCell width='80px'>{utility.order}</TableCell>
                        <TableCell width='22%'>{utility.name}</TableCell>
                        <TableCell width='22%'>
                          <NodeCellItem
                            nodeId={utility.nodeId}
                            onSelectClick={() => handleNodeSelectClick(utility)}
                            onDeleteClick={() => handleNodeDeleteClick(utility)}
                          />
                        </TableCell>
                        <TableCell width='22%'>
                          <DisplayToggleCellItem
                            visible={utility.isEnableSummary}
                            onClick={(visibility) => {
                              toggleVisibility(utility, visibility);
                            }}
                          />
                        </TableCell>
                        <TableCell width='22%'>
                          <Equation nodeId={utility.nodeId} />
                        </TableCell>
                      </SortableRow>
                    ))}
                  </TableBody>
                </Table>
              </Paper>
            </SortableContext>
            <HeaderHidden mt={3} />
            <SortableContext
              items={hiddenUtilities.map((utility) => utility.id)}
              strategy={verticalListSortingStrategy}
            >
              <Paper variant='outlined' sx={{ mt: 2, overflowX: 'auto' }}>
                <Table>
                  <SummaryTableHead />
                  <TableBody>
                    {hiddenUtilities.length === 0 && <EmptyRow />}
                    {hiddenUtilities.map((utility) => (
                      <SortableRow key={utility.id} utility={utility}>
                        <TableCell width='80px'>
                          <IconButton disableFocusRipple disableRipple size='small'>
                            <VisibilityOff />
                          </IconButton>
                        </TableCell>
                        <TableCell width='22%'>{utility.name}</TableCell>
                        <TableCell width='22%'>
                          <NodeCellItem
                            nodeId={utility.nodeId}
                            onSelectClick={() => handleNodeSelectClick(utility)}
                            onDeleteClick={() => handleNodeDeleteClick(utility)}
                          />
                        </TableCell>
                        <TableCell width='22%'>
                          <DisplayToggleCellItem
                            visible={utility.isEnableSummary}
                            onClick={(visibility) => {
                              toggleVisibility(utility, visibility);
                            }}
                          />
                        </TableCell>
                        <TableCell width='22%'>
                          <Equation nodeId={utility.nodeId} />
                        </TableCell>
                      </SortableRow>
                    ))}
                  </TableBody>
                </Table>
              </Paper>
            </SortableContext>
          </Box>
        </Paper>
        <DragOverlay>
          {activeId && (
            <OverlayRow
              utility={sortableUtilities.find((utility) => utility.id === activeId) as Utility}
            />
          )}
        </DragOverlay>
      </DndContext>
      <ProjectNodeDialog {...projectNodeDialogProps} onSubmit={handleNodeSelectSubmit} />
    </>
  );
}

function SummaryTableHead() {
  const tableHeadContents = [
    {
      // For drag icon
      label: '',
    },
    {
      label: '表示順',
    },
    {
      label: 'ユーティリティ名',
    },
    {
      label: 'ノード',
    },
    {
      label: '表示状態切り替え',
    },
    {
      label: '削減量計算式',
    },
  ];
  return (
    <TableHead>
      <TableRow>
        {tableHeadContents.map((tableHeadContent) => (
          <TableCell sx={{ whiteSpace: 'nowrap' }} key={tableHeadContent.label}>
            {tableHeadContent.label}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

export { ReductionSettingEdit };
