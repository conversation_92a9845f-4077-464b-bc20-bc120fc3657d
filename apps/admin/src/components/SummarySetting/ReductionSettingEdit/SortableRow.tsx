import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DragIndicator } from '@mui/icons-material';
import { IconButton, TableCell, TableRow, TableRowProps, Box, TableCellProps } from '@mui/material';
import { memo, PropsWithChildren } from 'react';
import { Utility } from '@/types';

type SortableRowProps = {
  utility: Utility;
  overlay?: boolean;
} & TableRowProps;
function SortableRow({
  utility,
  overlay,
  children,
  ...props
}: PropsWithChildren<SortableRowProps>) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: utility?.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const SortableTableCell = memo(function SortableTableCell(props: TableCellProps) {
    return <TableCell {...props} {...(overlay && { component: Box })} />;
  });

  return (
    <>
      {utility && (
        <TableRow
          ref={setNodeRef}
          style={style}
          {...attributes}
          sx={(theme) => ({
            //TODO: Fix this
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
            backgroundColor: 'white',
            borderBottom: `1px solid ${theme.palette.divider}`,
            '& > *': {
              borderBottom: 'unset',
            },
            ...(overlay && {
              position: 'relative',
              display: 'block',
              width: '100%',
              zIndex: 999999,
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.25)',
              },
            }),
          })}
          {...props}
          {...(overlay && { component: Box })}
        >
          <SortableTableCell width='80px'>
            {utility.isEnableSummary && (
              <IconButton
                size='small'
                disableFocusRipple
                disableRipple
                sx={{
                  cursor: 'ns-resize',
                }}
                {...listeners}
              >
                <DragIndicator />
              </IconButton>
            )}
          </SortableTableCell>
          {children}
        </TableRow>
      )}
    </>
  );
}
export { SortableRow };
