import { Visibility, VisibilityOff } from '@mui/icons-material';
import { Button } from '@mui/material';

type DisplayToggleCellItemProps = { visible: boolean; onClick?: (visibility: boolean) => void };
function DisplayToggleCellItem({ visible, onClick }: DisplayToggleCellItemProps) {
  function handleOnClick() {
    onClick?.(visible);
  }

  if (visible) {
    return (
      <Button
        onClick={() => {
          handleOnClick();
        }}
        startIcon={<VisibilityOff />}
      >
        非表示にする
      </Button>
    );
  }

  return (
    <Button onClick={handleOnClick} startIcon={<Visibility />}>
      表示する
    </Button>
  );
}
export { DisplayToggleCellItem };
