import { ExpandMore, Visibility, VisibilityOff } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Button,
  Typography,
  Stack,
  Accordion as MuiAccordion,
  AccordionDetails,
  AccordionSummary,
  Chip,
  styled,
  AccordionProps,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  Paper,
  AccordionActions,
} from '@mui/material';
import { ChangeEvent, useEffect, useState } from 'react';
import { useUpdateProject } from '@/api';
import { InfoCard, InfoCardHeader, InfoCardContent } from '@/components/InfoCard';
import { useProject } from '@/providers/ProjectProvider';
import { Project } from '@/types';

type UpdateTarget = keyof Pick<
  Project,
  'isEnableSummaryDetail' | 'isEnableSummaryScore' | 'isEnableSummaryTotal'
>;
function SummaryContentsSetting() {
  const { project, fetch } = useProject();

  const { trigger: updateProjectRequest, isLoading: isUpdateLoading } = useUpdateProject();

  function updateProject(target: UpdateTarget) {
    return async (visibility: boolean) => {
      const updateSrc: Partial<Project> = {
        ...project,
        [target]: visibility,
      };

      try {
        if (project) {
          const response = await updateProjectRequest({
            urlParameter: {
              projectId: project?.id,
            },
            body: updateSrc,
          });

          if (response) {
            fetch(project?.id);
          }
        }
      } catch (error) {
        console.error(error);
      }
    };
  }

  return (
    <>
      <InfoCard>
        <InfoCardHeader py={2}>コンテンツ表示設定</InfoCardHeader>
        <InfoCardContent>
          <Paper variant={'outlined'}>
            {project && (
              <>
                <SummaryContentController
                  label='速報値'
                  visible={project?.isEnableSummaryDetail}
                  onSubmit={updateProject('isEnableSummaryDetail')}
                  isLoading={isUpdateLoading}
                />
                <SummaryContentController
                  label='集計サマリ'
                  visible={project?.isEnableSummaryScore}
                  onSubmit={updateProject('isEnableSummaryScore')}
                  isLoading={isUpdateLoading}
                />
                <SummaryContentController
                  label='累計サマリ'
                  visible={project?.isEnableSummaryTotal}
                  onSubmit={updateProject('isEnableSummaryTotal')}
                  isLoading={isUpdateLoading}
                />
              </>
            )}
          </Paper>
        </InfoCardContent>
      </InfoCard>
    </>
  );
}

type SummaryContentControllerProps = {
  label: string;
  visible: boolean;
  onChange?: (visible: boolean) => void;
  onSubmit: (visible: boolean) => void;
  isLoading: boolean;
};

function SummaryContentController({
  label,
  visible,
  onChange,
  onSubmit,
  isLoading,
}: SummaryContentControllerProps) {
  type RadioValue = 'visible' | 'hidden';

  const defaultRadioValue: RadioValue = visible
    ? 'visible'
    : visible === false
    ? 'hidden'
    : 'visible';

  const [radioValue, setRadioValue] = useState<RadioValue>(defaultRadioValue);

  function handleOnChange(event: ChangeEvent<HTMLInputElement>) {
    const value = event.target.value as RadioValue;
    const visible = value === 'visible' ? true : value === 'hidden' ? false : false;
    setRadioValue(value);
    onChange?.(visible);
  }

  function handleOnSubmit() {
    const result = transformRadioValueToVisible(radioValue);
    onSubmit(result);
  }

  function transformRadioValueToVisible(radioValue: RadioValue) {
    return radioValue === 'visible' ? true : radioValue === 'hidden' ? false : false;
  }

  const [expanded, setExpanded] = useState(false);

  function toggleExpanded() {
    setExpanded(!expanded);
  }

  // reset expanded state when visible prop is changed
  useEffect(() => {
    setExpanded(false);
  }, [visible]);

  function handleCancel() {
    setRadioValue(defaultRadioValue);
    toggleExpanded();
  }

  return (
    <Accordion expanded={expanded} onChange={toggleExpanded}>
      <AccordionSummary
        expandIcon={
          <ExpandMore
            sx={{
              cursor: 'pointer',
            }}
          />
        }
        sx={{
          '&:hover': {
            '.actionLabel': {
              visibility: 'visible',
            },
          },
        }}
      >
        <Stack
          direction={'row'}
          justifyContent={'space-between'}
          width={'100%'}
          alignItems={'center'}
        >
          <Stack direction={'row'} alignItems={'center'} spacing={2}>
            <Typography variant='body1'>{label}</Typography>
            <Stack direction={'row'} alignItems={'center'}>
              <VisibilityChip visible={visible} />
            </Stack>
          </Stack>
          <Typography
            className='actionLabel'
            sx={{ visibility: 'hidden' }}
            variant='button'
            color={'GrayText'}
          >
            操作
          </Typography>
        </Stack>
      </AccordionSummary>
      <AccordionDetails>
        <RadioGroup
          value={radioValue}
          name='controlled-radio-buttons-group'
          onChange={handleOnChange}
          sx={{
            width: 'fit-content',
          }}
        >
          <FormLabel id='demo-controlled-radio-buttons-group'>表示状態の切り替え</FormLabel>
          <FormControlLabel value={'visible'} control={<Radio />} label='表示する' />
          <FormControlLabel value={'hidden'} control={<Radio />} label='非表示にする' />
        </RadioGroup>
      </AccordionDetails>
      <AccordionActions>
        <Stack mt={1} direction={'row'} spacing={2} justifyContent={'flex-start'} width={'100%'}>
          <LoadingButton
            loading={isLoading}
            variant={'contained'}
            disableElevation
            size='small'
            onClick={handleOnSubmit}
            disabled={radioValue === defaultRadioValue}
          >
            変更
          </LoadingButton>
          <Button onClick={handleCancel} size='small'>
            キャンセル
          </Button>
        </Stack>
      </AccordionActions>
    </Accordion>
  );
}

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} {...props} />
))(() => ({
  '&.Mui-expanded:before': {
    opacity: 1,
  },
}));

function VisibilityChip({ visible }: { visible: boolean }) {
  if (visible) {
    return (
      <Chip
        color={'success'}
        size='small'
        icon={<Visibility fontSize='small' />}
        variant='outlined'
        label='表示中'
      />
    );
  } else {
    return (
      <Chip
        label=' 非表示中'
        size='small'
        icon={<VisibilityOff fontSize='small' />}
        variant='outlined'
      />
    );
  }
}

export { SummaryContentsSetting };
