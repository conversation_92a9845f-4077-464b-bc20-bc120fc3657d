import { Paper, PaperProps, TableContainer } from '@mui/material';
import { PropsWithChildren } from 'react';
import { useDOMRect } from '@/hooks';

export type EcoisTableContainerProps = PropsWithChildren<PaperProps> & {
  heightAdjuster?: {
    value: number;
    unit: string;
  };
};

function EcoisTableContainer({ children, sx, heightAdjuster, ...props }: EcoisTableContainerProps) {
  const [ref, { top }] = useDOMRect<HTMLDivElement>();

  const adjusterString = heightAdjuster ? buildAdjusterString(heightAdjuster) : '';

  return (
    <TableContainer
      ref={ref}
      component={Paper}
      sx={{
        position: 'relative',
        overflow: 'auto',
        height: (theme) => `calc(100vh - ${top}px - ${theme.spacing(2)}${adjusterString})`,
        ...sx,
      }}
      {...props}
    >
      {children}
    </TableContainer>
  );
}

function buildAdjusterString({ value, unit }: { value: number; unit: string }) {
  if (value === 0) return '';
  const operator = value > 0 ? '+' : '-';

  return ` ${operator} ${Math.abs(value)}${unit}`;
}

export { EcoisTableContainer };
