import { Stack, StackProps } from '@mui/material';
import { PropsWithChildren } from 'react';

type ControlCellContainerProps = PropsWithChildren<StackProps>;
function ControlCellContainer({ children, ...props }: ControlCellContainerProps) {
  return (
    <Stack
      direction='row'
      pr={1}
      display={'inline-flex'}
      height={'100%'}
      alignItems={'center'}
      {...props}
    >
      {children}
    </Stack>
  );
}
export { ControlCellContainer };
