import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
  GlobalStyles,
  Theme,
  TableRowProps,
} from '@mui/material';
import { useTheme } from '@mui/material';
import { PropsWithChildren, ReactElement } from 'react';
import { EcoisTableContainer, EcoisTableContainerProps } from './EcoisTableContainer';
import { TableProgress } from './TableProgress';

type Props<T, K extends keyof T> = {
  data: T[] | undefined;
  keyProp?: K;
  tableBodyRow: (data: T) => ReactElement;
  tableHeadRow?: ReactElement;
  controlCell?: (data: T) => ReactElement;
  isEmpty: boolean;
  isProgress: boolean;
  rowProps?: (data: T) => TableRowProps;
};

function EcoisSimpleDataTable<T, K extends keyof T>({
  data,
  keyProp = 'id' as K,
  tableHeadRow,
  controlCell,
  tableBodyRow,
  isEmpty,
  isProgress,
  rowProps,
  ...props
}: PropsWithChildren<Props<T, K>> & EcoisTableContainerProps) {
  return (
    <EcoisTableContainer {...props}>
      {isProgress && <TableProgress />}
      <Table stickyHeader>
        <TableHead>
          {isEmpty && <EmptyMessageRow />}
          <TableRow {...(isEmpty && { style: { display: 'none' } })}>
            {tableHeadRow}
            <TableCell width={'20px'}></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!isEmpty &&
            !!data &&
            data.map((item) => (
              <TableRow
                hover
                key={`${item[keyProp]}`}
                sx={{
                  textDecoration: 'none',
                  position: 'relative',
                  cursor: 'pointer',
                  ...rowProps?.(item).sx,
                }}
                {...rowProps?.(item)}
              >
                {tableBodyRow(item)}
                <ControlTableCell>{controlCell && controlCell(item)}</ControlTableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </EcoisTableContainer>
  );
}

function ControlTableCell({ children }: PropsWithChildren) {
  const theme = useTheme() as Theme;

  const hoverBackgroundColor = theme.palette.action.hover;
  const leftPadding = theme.spacing(3);

  const targetClassName = 'controlButtons';
  const visibilityControlStyle = {
    [`.MuiTableRow-root .${targetClassName}`]: {
      visibility: 'hidden',
    },
    [`.MuiTableRow-root:hover .${targetClassName}`]: {
      visibility: 'visible',
    },
  };

  return (
    <TableCell>
      <GlobalStyles styles={visibilityControlStyle} />
      <Box
        pl={leftPadding}
        className={targetClassName}
        sx={{
          height: `calc(100% - 1px)`,
          position: 'absolute',
          right: '0',
          top: '0',
          backgroundImage: `
          linear-gradient(
            to right,
          rgba(0,0,0,0) 0px, 
            ${hoverBackgroundColor} ${leftPadding}, 
            ${hoverBackgroundColor}
          ), 
          linear-gradient(
            to right,
          rgba(0,0,0,0) 0%,
            #fff ${leftPadding},
            #fff
          )`,
        }}
      >
        {children}
      </Box>
    </TableCell>
  );
}

function EmptyMessageRow() {
  return (
    <TableRow>
      <TableCell colSpan={2}>データが見つかりませんでした</TableCell>
    </TableRow>
  );
}

export { EcoisSimpleDataTable };
