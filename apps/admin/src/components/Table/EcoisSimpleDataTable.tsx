import NorthIcon from '@mui/icons-material/North';
import SouthIcon from '@mui/icons-material/South';
import SwapVertIcon from '@mui/icons-material/SwapVert';
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
  GlobalStyles,
  Theme,
  TableRowProps,
  TableSortLabel,
} from '@mui/material';
import { useTheme } from '@mui/material';
import { PropsWithChildren, ReactElement } from 'react';
import { EcoisTableContainer, EcoisTableContainerProps } from './EcoisTableContainer';
import { TableProgress } from './TableProgress';

export type Column<T> = {
  key: keyof T;
  label: string;
  minWidth?: number;
  maxWidth?: number;
  align?: 'left' | 'right' | 'center';

  sortable?: boolean;
};

type Props<T, K extends keyof T> = {
  data: T[] | undefined;
  keyProp?: K;
  tableBodyRow: (data: T) => ReactElement;
  tableHeadRow?: ReactElement;
  controlCell?: (data: T) => ReactElement;
  isEmpty: boolean;
  isProgress: boolean;
  rowProps?: (data: T) => TableRowProps;
  columns?: Column<T>[];
  order?: 'asc' | 'desc' | 'default';
  orderBy?: keyof T;
  onRequestSort?: (property: keyof T) => void;
};

function EcoisSimpleDataTable<T, K extends keyof T>({
  data,
  keyProp = 'id' as K,
  controlCell,
  tableBodyRow,
  tableHeadRow,
  isEmpty,
  isProgress,
  rowProps,
  columns,
  order,
  orderBy,
  onRequestSort,
  ...props
}: PropsWithChildren<Props<T, K>> & EcoisTableContainerProps) {
  type SortDirection = 'asc' | 'desc' | 'default';

  function SortIcon({ direction }: { direction: SortDirection }) {
    switch (direction) {
      case 'asc':
        return <NorthIcon fontSize='small' sx={{ fontSize: '14px' }} />;
      case 'desc':
        return <SouthIcon fontSize='small' sx={{ fontSize: '14px' }} />;
      default:
        return <SwapVertIcon fontSize='small' sx={{ opacity: 0.3 }} />;
    }
  }

  return (
    <EcoisTableContainer {...props}>
      {isProgress && <TableProgress />}
      <Table stickyHeader>
        <TableHead>
          {isEmpty && <EmptyMessageRow />}
          <TableRow {...(isEmpty && { style: { display: 'none' } })}>
            {columns
              ? columns.map((column) => (
                  <TableCell
                    key={column.key as string}
                    sx={{ minWidth: column.minWidth, maxWidth: column.maxWidth }}
                    align={column.align}
                  >
                    {column.sortable ? (
                      <TableSortLabel
                        active={orderBy === column.key && order !== 'default'}
                        direction={orderBy === column.key && order !== 'default' ? order : 'asc'}
                        onClick={() => onRequestSort?.(column.key)}
                        IconComponent={(props) => (
                          <SortIcon
                            direction={orderBy === column.key ? order ?? 'default' : 'default'}
                            {...props}
                          />
                        )}
                      >
                        {column.label}
                      </TableSortLabel>
                    ) : (
                      column.label
                    )}
                  </TableCell>
                ))
              : tableHeadRow}
            <TableCell width={'20px'}></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!isEmpty &&
            !!data &&
            data.map((item) => (
              <TableRow
                hover
                key={`${item[keyProp]}`}
                sx={{
                  textDecoration: 'none',
                  position: 'relative',
                  cursor: 'pointer',
                  ...rowProps?.(item).sx,
                }}
                {...rowProps?.(item)}
              >
                {tableBodyRow(item)}
                <ControlTableCell>{controlCell && controlCell(item)}</ControlTableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </EcoisTableContainer>
  );
}

function ControlTableCell({ children }: PropsWithChildren) {
  const theme = useTheme() as Theme;

  const hoverBackgroundColor = theme.palette.action.hover;
  const leftPadding = theme.spacing(3);

  const targetClassName = 'controlButtons';
  const visibilityControlStyle = {
    [`.MuiTableRow-root .${targetClassName}`]: {
      visibility: 'hidden',
    },
    [`.MuiTableRow-root:hover .${targetClassName}`]: {
      visibility: 'visible',
    },
  };

  return (
    <TableCell>
      <GlobalStyles styles={visibilityControlStyle} />
      <Box
        pl={leftPadding}
        className={targetClassName}
        sx={{
          height: `calc(100% - 1px)`,
          position: 'absolute',
          right: '0',
          top: '0',
          backgroundImage: `
          linear-gradient(
            to right,
          rgba(0,0,0,0) 0px, 
            ${hoverBackgroundColor} ${leftPadding}, 
            ${hoverBackgroundColor}
          ), 
          linear-gradient(
            to right,
          rgba(0,0,0,0) 0%,
            #fff ${leftPadding},
            #fff
          )`,
        }}
      >
        {children}
      </Box>
    </TableCell>
  );
}

function EmptyMessageRow() {
  return (
    <TableRow>
      <TableCell colSpan={2}>データが見つかりませんでした</TableCell>
    </TableRow>
  );
}

export { EcoisSimpleDataTable };
