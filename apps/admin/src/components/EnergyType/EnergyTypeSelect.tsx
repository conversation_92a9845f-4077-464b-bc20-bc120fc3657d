import { FormControl, InputLabel, MenuItem, Select, SelectProps, Stack, Box } from '@mui/material';
import { EnergyTypeColorIcons, EnergyTypeIcons } from 'lib/Component/Icons/EnergyTypeIcons';
import { forwardRef } from 'react';
import { useEnergyTypes } from '@/providers/EnergyTypesProvider';

type EnergyTypeSelectProps = {
  id?: string | undefined;
  showColor?: boolean;
  isColor?: boolean;
} & SelectProps;

const EnergyTypeSelect = forwardRef<any, EnergyTypeSelectProps>(
  ({ id = 'energyTypeLabel', showColor = true, isColor = false, ...props }, _ref) => {
    const energyTypes = useEnergyTypes();

    return (
      <FormControl size={props.size}>
        <InputLabel id={id}>エネルギータイプ</InputLabel>
        <Select fullWidth {...props} label={id}>
          <MenuItem value='default'>選択してください</MenuItem>
          {energyTypes?.map((energyType) => (
            <MenuItem key={energyType.id} value={energyType.id}>
              <Stack direction={'row'} alignItems={'center'} gap={0.5}>
                {showColor && (
                  <>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      {isColor ? (
                        <EnergyTypeColorIcons energyTypeId={energyType.id} />
                      ) : (
                        <EnergyTypeIcons energyTypeId={energyType.id} sx={{ fontSize: 20 }} />
                      )}
                    </Box>
                  </>
                )}
                {energyType.name}({energyType.unit})
              </Stack>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  },
);

EnergyTypeSelect.displayName = 'EnergyTypeSelect';

export { EnergyTypeSelect };
