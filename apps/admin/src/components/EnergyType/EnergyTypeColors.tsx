import { blue, green, purple, red, yellow } from '@mui/material/colors';

type EnergyTypeColors = 'red' | 'blue' | 'green' | 'yellow' | 'purple';

const energyTypeColor = (color: EnergyTypeColors) => {
  switch (color) {
    case 'red':
      return red[500];
    case 'blue':
      return blue[500];
    case 'green':
      return green[500];
    case 'yellow':
      return yellow[500];
    case 'purple':
      return purple[500];
  }
};

export type { EnergyTypeColors };
export { energyTypeColor };
