import { Typo<PERSON>, <PERSON><PERSON>, TableCell } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { ContentTitle } from '../ContentTitle';
import { EcoisSimpleDataTable, ControlCellContainer } from '../Table';
import {
  useProjectGroupsForPermissionDialog,
  useDeleteProjectGroupPermissionDialog,
} from './Dialog';
import {
  DeleteClientUserPermissionProjectGroupResponse,
  UpsertClientUserPermissionProjectGroupResponse,
} from '@/api/types';
import { ClientUser, ClientUserProjectGroup } from '@/types';

type ClientUserPermissionProjectGroupsProps = {
  clientUserId: ClientUser['id'];
  projectGroups: ClientUserProjectGroup[];
  onAddSuccess: (response: UpsertClientUserPermissionProjectGroupResponse) => void;
  onDeleteSuccess: (response: DeleteClientUserPermissionProjectGroupResponse) => void;
};
function ClientUserPermissionProjectGroups({
  clientUserId,
  projectGroups,
  onAddSuccess,
  onDeleteSuccess,
}: ClientUserPermissionProjectGroupsProps) {
  const {
    ProjectGroupsForPermissionDialog,
    open: openProjectGroupDialog,
    props: projectGroupsDialogProps,
  } = useProjectGroupsForPermissionDialog();

  const {
    DeleteProjectGroupPermissionDialog,
    open: openDeleteDialog,
    props: deleteDialogProps,
  } = useDeleteProjectGroupPermissionDialog();

  //
  //
  //

  const navigate = useNavigate();

  function handleAddButtonClick() {
    openProjectGroupDialog();
  }

  function handleOnAddDialogSuccess(response: UpsertClientUserPermissionProjectGroupResponse) {
    onAddSuccess?.(response);
  }

  function handleProjectGroupDetailClick(projectGroup: ClientUserProjectGroup) {
    return () => {
      navigate(`/projectgroups/${projectGroup.id}/detail`);
    };
  }

  function handleRemoveButton(projectGroup: ClientUserProjectGroup) {
    return () => {
      clientUserId !== undefined &&
        openDeleteDialog({
          projectGroup,
          clientUserId,
        });
    };
  }

  function handleOnDeleteDialogSuccess(response: DeleteClientUserPermissionProjectGroupResponse) {
    onDeleteSuccess?.(response);
  }

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>
          <Typography component='span'>権限付与中の案件グループ</Typography>
        </ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button variant='contained' disableElevation onClick={handleAddButtonClick}>
            案件グループを追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>
      <EcoisSimpleDataTable
        data={projectGroups}
        tableHeadRow={
          <>
            <TableCell sx={{ width: '10%', whiteSpace: 'nowrap' }}>案件グループID</TableCell>
            <TableCell>案件グループ名</TableCell>
          </>
        }
        controlCell={(projectGroup) => (
          <ControlCellContainer>
            <Button size='small' onClick={handleProjectGroupDetailClick(projectGroup)}>
              詳細
            </Button>
            <Button size='small' onClick={handleRemoveButton(projectGroup)}>
              解除する
            </Button>
          </ControlCellContainer>
        )}
        tableBodyRow={(projectGroup) => (
          <>
            <TableCell sx={{ width: '10%' }}>{projectGroup.id}</TableCell>
            <TableCell sx={{ width: '100%' }}>{projectGroup.name}</TableCell>
          </>
        )}
        isEmpty={projectGroups.length === 0}
        isProgress={!projectGroups}
      />
      {clientUserId !== undefined && (
        <ProjectGroupsForPermissionDialog
          clientUserId={clientUserId}
          {...projectGroupsDialogProps}
          onSuccess={handleOnAddDialogSuccess}
        />
      )}
      <DeleteProjectGroupPermissionDialog
        onSuccess={handleOnDeleteDialogSuccess}
        {...deleteDialogProps}
      />
    </>
  );
}
export { ClientUserPermissionProjectGroups };
