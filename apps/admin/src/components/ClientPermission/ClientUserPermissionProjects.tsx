import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableCell, Box, Paper } from '@mui/material';
import { useState, SyntheticEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { ContentTitle } from '../ContentTitle';
import { useDeleteDialog } from '../Dialog';
import { ControlCellContainer, EcoisSimpleDataTable } from '../Table';
import { useProjectListForPermissionDialog } from './Dialog';
import { useDeleteClientUserPermissionProject } from '@/api';
import { ClientUserProject } from '@/types';

type Props = {
  clientUserId: number;
  clientUserProjects: ClientUserProject[] | undefined;
  onRemoveSuccess: () => void;
  onAddSuccess: () => void;
};

function ClientUserPermissionProjects({
  clientUserId,
  clientUserProjects,
  onAddSuccess,
  onRemoveSuccess,
}: Props) {
  const { ProjectListForPermissionDialog, props, open } = useProjectListForPermissionDialog();

  function handleAddButtonClick() {
    open();
  }

  const navigate = useNavigate();
  function handleProjectDetailClick(project: ClientUserProject) {
    return () => {
      navigate(`/projects/${project.id}/detail`);
    };
  }

  // Delete Dialog
  const { DeleteDialog, open: openDeleteDialog, props: deleteDialogProps } = useDeleteDialog();

  const { trigger: deletePermission, isLoading: deleteLoading } =
    useDeleteClientUserPermissionProject();

  const [deleteTarget, setDeleteTarget] = useState<ClientUserProject | undefined>(undefined);

  function handleRemoveButton(project: ClientUserProject) {
    return (event: SyntheticEvent) => {
      event.stopPropagation();
      setDeleteTarget(project);
      openDeleteDialog();
    };
  }

  async function handleDeleteDialogSubmit() {
    try {
      if (!deleteTarget || !clientUserId) return;
      await deletePermission({
        urlParameter: { clientUserId: clientUserId, projectId: deleteTarget?.id },
      });
      onRemoveSuccess?.();
      // fetchPermission(clientUserId);
    } catch (e) {
      return Promise.reject(e);
    }
  }

  function handleAddDialogSuccess() {
    onAddSuccess?.();
  }

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>
          <Typography component='span'>権限付与中の案件</Typography>
        </ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button variant='contained' disableElevation onClick={handleAddButtonClick}>
            案件を追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>

      <EcoisSimpleDataTable
        data={clientUserProjects}
        tableHeadRow={
          <>
            <TableCell sx={{ width: '10%', whiteSpace: 'nowrap' }}>案件ID</TableCell>
            <TableCell>案件名</TableCell>
          </>
        }
        tableBodyRow={(project) => (
          <>
            <TableCell sx={{ width: '10%' }}>{project.id}</TableCell>
            <TableCell sx={{ width: '100%' }}>{project.name}</TableCell>
          </>
        )}
        controlCell={(project) => (
          <ControlCellContainer>
            <Button size='small' onClick={handleProjectDetailClick(project)}>
              詳細
            </Button>
            <Button size='small' onClick={handleRemoveButton(project)}>
              解除する
            </Button>
          </ControlCellContainer>
        )}
        isEmpty={clientUserProjects?.length === 0}
        isProgress={!clientUserProjects}
      />

      {clientUserProjects && (
        <ProjectListForPermissionDialog
          clientUserId={clientUserId}
          clientUserProjects={clientUserProjects}
          {...props}
          onSuccess={handleAddDialogSuccess}
        />
      )}

      <DeleteDialog
        {...deleteDialogProps}
        title={`閲覧権限を解除する`}
        confirmText='案件の権限を解除するとユーザーは案件を閲覧することができなくなります'
        isLoading={deleteLoading}
        labelText='解除する'
        buttonText='解除'
        onSubmit={handleDeleteDialogSubmit}
      >
        <Box my={2}>
          <Paper variant={'outlined'}>
            <Box p={1}>
              <Typography variant='body2'>対象の案件</Typography>
              <Typography variant='body1'>{deleteTarget?.name}</Typography>
            </Box>
          </Paper>
        </Box>
      </DeleteDialog>
    </>
  );
}

export { ClientUserPermissionProjects };
