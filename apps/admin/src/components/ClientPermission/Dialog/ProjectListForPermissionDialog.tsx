import { useUpsertClientUserPermissionProject } from '@/api';
import { UpsertClientUserPermissionProjectResponse } from '@/api/types';
import { ProjectListDialog, ProjectListDialogProps } from '@/components/ProjectListDialog';
import { useDialogState } from '@/hooks';
import { ClientUser, ClientUserProject } from '@/types';

type ProjectListForPermissionDialogProps = {
  onSuccess?: (response: UpsertClientUserPermissionProjectResponse) => void;
  clientUserId: ClientUser['id'];
  clientUserProjects?: ClientUserProject[];
};

function useProjectListForPermissionDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ProjectListForPermissionDialog, open, props };
}

function ProjectListForPermissionDialog({
  clientUserId,
  clientUserProjects,
  onSuccess,
  close,
  ...props
}: ProjectListDialogProps & ProjectListForPermissionDialogProps) {
  const { trigger: upsertProjectPermission } = useUpsertClientUserPermissionProject();

  async function updateProjectPermission(selectedProjectIds: number[]) {
    try {
      const response = await upsertProjectPermission({
        body: {
          projectIds: selectedProjectIds,
        },
        urlParameter: {
          clientUserId,
        },
      });
      if (response) {
        onSuccess?.(response);
        close();
      }
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <ProjectListDialog
      {...props}
      close={close}
      clientUserProjects={clientUserProjects}
      onSubmit={updateProjectPermission}
    />
  );
}

export { useProjectListForPermissionDialog };
