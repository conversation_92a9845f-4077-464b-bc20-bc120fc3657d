import { Box, Paper, Typography } from '@mui/material';
import { useState } from 'react';
import { useDeleteClientUserPermissionProjectGroup } from '@/api';
import { DeleteClientUserPermissionProjectGroupResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ClientUser, ClientUserProjectGroup } from '@/types';

type DialogContext = {
  projectGroup: ClientUserProjectGroup;
  clientUserId: ClientUser['id'];
};

function useDeleteProjectGroupPermissionDialog() {
  const { openState, open: openDialog, close: closeDialog } = useDialogState();

  const [context, setContext] = useState<DialogContext>({} as DialogContext);

  function open(context: DialogContext) {
    setContext(context);
    openDialog();
  }

  function close() {
    setContext({} as DialogContext);
    closeDialog();
  }

  const props = {
    context,
    openState,
    close,
  };

  return { DeleteProjectGroupPermissionDialog, open, props };
}

type DeleteProjectGroupPermissionDialogProps = {
  context: DialogContext;
  onSuccess?: (response: DeleteClientUserPermissionProjectGroupResponse) => void;
} & HooksDialogProps;
function DeleteProjectGroupPermissionDialog({
  context: { projectGroup, clientUserId },
  onSuccess,
  ...props
}: DeleteProjectGroupPermissionDialogProps) {
  const { trigger, isLoading } = useDeleteClientUserPermissionProjectGroup();

  async function handleDeleteDialogSubmit() {
    try {
      const response = await trigger({
        urlParameter: {
          clientUserId,
          projectGroupId: projectGroup?.id,
        },
      });
      if (response) {
        props.close();
        onSuccess?.(response);
      }
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <DeleteDialog
      {...props}
      title={`閲覧権限を解除する`}
      confirmText='権限を解除するとユーザーは案件グループに属する案件をすべて閲覧できなくなります'
      isLoading={isLoading}
      labelText='解除する'
      buttonText='解除'
      onSubmit={handleDeleteDialogSubmit}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象の案件グループ</Typography>
            <Typography variant='body1'>{projectGroup?.name}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteProjectGroupPermissionDialog };
