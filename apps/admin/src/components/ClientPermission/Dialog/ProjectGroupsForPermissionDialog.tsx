import { Loading<PERSON>utton } from '@mui/lab';
import {
  DialogTitle,
  Stack,
  Box,
  Typography,
  DialogContent,
  TableCell,
  useTheme,
  Theme,
  Button,
} from '@mui/material';
import { useState, ChangeEvent, useEffect } from 'react';
import { useEffectOnce } from 'react-use';
import { useListProjectGroups, useUpsertClientUserPermissionProjectGroup } from '@/api';
import { UpsertClientUserPermissionProjectGroupResponse } from '@/api/types';
import { CLCheckbox } from '@/components/ColumnList/ColumnList';
import { EcoisDialog } from '@/components/Dialog';
import { EcoisSimpleDataTable } from '@/components/Table';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ClientUser, ProjectGroup } from '@/types';

function useProjectGroupsForPermissionDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    close,
    openState,
  };

  return { ProjectGroupsForPermissionDialog, open, props };
}

type ProjectGroupsForPermissionDialogProps = {
  clientUserId: ClientUser['id'];
  onSuccess?: (response: UpsertClientUserPermissionProjectGroupResponse) => void;
} & HooksDialogProps;

function ProjectGroupsForPermissionDialog({
  openState,
  clientUserId,
  close,
  onSuccess,
}: ProjectGroupsForPermissionDialogProps) {
  const {
    trigger: requestProjectGroups,
    data: projectGroupsRes,
    isLoading: isLoadingProjectGroups,
  } = useListProjectGroups();

  const { trigger: upsertPermission, isLoading: isLoadingUpsertPermission } =
    useUpsertClientUserPermissionProjectGroup();

  function fetchProjectGroups() {
    requestProjectGroups({ queryParameter: {} });
  }

  useEffectOnce(() => {
    fetchProjectGroups();
  });

  const theme = useTheme() as Theme;

  const [selectedProjectIds, setSelectedProjectIds] = useState<ProjectGroup['id'][]>([]);
  function addId(id: ProjectGroup['id']) {
    setSelectedProjectIds([...selectedProjectIds, id]);
  }
  function removeId(id: ProjectGroup['id']) {
    setSelectedProjectIds(selectedProjectIds.filter((selectedId) => selectedId !== id));
  }

  function handleCheckOnChange(project: ProjectGroup) {
    return (event: ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        addId(project.id);
      } else {
        removeId(project.id);
      }
    };
  }

  function handleFullCheckOnChange(event: ChangeEvent<HTMLInputElement>) {
    if (event.target.checked) {
      setSelectedProjectIds(projectGroupsRes?.projectGroups.map(({ id }) => id) ?? []);
    } else {
      setSelectedProjectIds([]);
    }
  }

  const isIndeterminate =
    projectGroupsRes &&
    selectedProjectIds.length > 0 &&
    selectedProjectIds.length < projectGroupsRes?.projectGroups.length;

  function handleListItemOnClick(project: ProjectGroup) {
    return () => {
      if (selectedProjectIds.includes(project.id)) {
        removeId(project.id);
      } else {
        addId(project.id);
      }
    };
  }

  // ダイアログが閉じるときのリセット処理
  function reset() {
    setSelectedProjectIds([]);
  }

  // ダイアログが閉じるときのフック
  useEffect(() => {
    openState && reset();
  }, [openState]);

  function onCancelClick() {
    close();
  }

  async function handleSubmit() {
    try {
      const response = await upsertPermission({
        urlParameter: {
          clientUserId: clientUserId,
        },
        body: {
          projectGroupIds: selectedProjectIds,
        },
      });
      if (response) {
        close();
        onSuccess?.(response);
      }
    } catch (e) {
      console.error(e);
    }
  }

  return (
    <EcoisDialog
      open={openState}
      close={close}
      fullWidth
      maxWidth='xl'
      sx={(theme) => ({
        '.MuiDialog-paper': { height: '100%', minWidth: theme.breakpoints.values.sm + 'px' },
      })}
    >
      <DialogTitle
        component={Stack}
        direction={'row'}
        justifyContent={'space-between'}
        flexWrap={'wrap'}
      >
        <DialogTitle sx={{ padding: 0 }}>権限を付与する案件グループを選択してください</DialogTitle>
        <Stack
          direction={'row'}
          spacing={1}
          alignItems={'center'}
          justifyContent={'end'}
          flexGrow={1}
          py={(theme) => (theme.breakpoints.down('sm') ? 1 : 0)}
        >
          <Box px={2}>
            <Typography>{selectedProjectIds.length}件選択中</Typography>
          </Box>
          <Button variant='outlined' onClick={onCancelClick}>
            キャンセル
          </Button>
          <LoadingButton
            loading={isLoadingUpsertPermission}
            variant='contained'
            disabled={selectedProjectIds.length === 0}
            disableElevation
            onClick={handleSubmit}
          >
            追加
          </LoadingButton>
        </Stack>
      </DialogTitle>
      <DialogContent sx={{ display: 'flex', overflow: 'visible' }}>
        <EcoisSimpleDataTable
          heightAdjuster={{
            value: -parseInt(theme.spacing(5).replace('px', ''), 10),
            unit: 'px',
          }}
          data={projectGroupsRes?.projectGroups}
          tableHeadRow={
            <>
              <TableCell sx={{ width: 0 }}>
                <CLCheckbox
                  checked={selectedProjectIds.length === projectGroupsRes?.projectGroups.length}
                  onChange={handleFullCheckOnChange}
                  indeterminate={isIndeterminate}
                />
              </TableCell>
              <TableCell sx={{ width: 0, whiteSpace: 'nowrap' }}>案件ID</TableCell>
              <TableCell>案件名</TableCell>
            </>
          }
          rowProps={(project) => ({
            onClick: handleListItemOnClick(project),
          })}
          tableBodyRow={(project) => (
            <>
              <TableCell sx={{ width: 0 }}>
                <CLCheckbox
                  onChange={handleCheckOnChange(project)}
                  checked={selectedProjectIds.includes(project.id)}
                />
              </TableCell>
              <TableCell sx={{ width: 0 }}>{project.id}</TableCell>
              <TableCell>{project.name}</TableCell>
            </>
          )}
          isEmpty={projectGroupsRes?.projectGroups.length === 0}
          isProgress={isLoadingProjectGroups}
        />
      </DialogContent>
    </EcoisDialog>
  );
}
export { useProjectGroupsForPermissionDialog };
