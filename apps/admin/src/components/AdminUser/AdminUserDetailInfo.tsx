import { Box, CircularProgress, Alert, Chip, Stack } from '@mui/material';
import { AddText, Label, Value } from '../Display';
import { AdminUser } from '@/types';

function AdminUserDetailInfo({
  adminUser,
  isLoading,
  error,
  onAddTextButtonClick,
}: {
  adminUser: AdminUser | undefined;
  isLoading: boolean;
  error: Error;
  onAddTextButtonClick?: () => void;
}) {
  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity='error'>アドミンユーザーの読み込みに失敗しました</Alert>;
  }

  function handleAddButtonClick() {
    onAddTextButtonClick?.();
  }

  return (
    <>
      {adminUser && (
        <Stack spacing={2}>
          <Box>
            <Label>権限</Label>
            <Value>{adminUser.role}</Value>
          </Box>
          <Box>
            <Label>有効状態</Label>
            <Value>
              {adminUser.isAvailable ? (
                <Chip component={'span'} color={'primary'} label='有効' />
              ) : (
                <Chip component={'span'} color={'error'} label='無効' />
              )}
            </Value>
          </Box>
          <Box>
            <Label>ユーザーネーム</Label>
            <Value>{adminUser.username}</Value>
          </Box>
          <Box>
            <Label>Email</Label>
            <Value>
              {adminUser.email ?? <AddText onClick={handleAddButtonClick} label='Email' />}
            </Value>
          </Box>
          <Box>
            <Label>氏名</Label>
            <Value>
              {adminUser.name ?? <AddText onClick={handleAddButtonClick} label='氏名' />}
            </Value>
          </Box>
          <Box>
            <Label>会社</Label>
            <Value>
              {adminUser.company ?? <AddText onClick={handleAddButtonClick} label='会社' />}
            </Value>
          </Box>
          <Box>
            <Label>部署</Label>
            <Value>
              {adminUser.department ?? <AddText onClick={handleAddButtonClick} label='部署' />}
            </Value>
          </Box>
          <Box>
            <Label>役職</Label>
            <Value>
              {adminUser.position ?? <AddText onClick={handleAddButtonClick} label='役職' />}
            </Value>
          </Box>{' '}
        </Stack>
      )}
    </>
  );
}

export { AdminUserDetailInfo };
