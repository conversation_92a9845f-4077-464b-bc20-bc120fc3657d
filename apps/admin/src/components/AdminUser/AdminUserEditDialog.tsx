import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button, Alert } from '@mui/material';
import { useEffect } from 'react';
import { EcoisDialog } from '../Dialog';
import { AdminUserForm } from './AdminUserForm';
import { defaultAdminUserValues } from './defaultAdminUserValues';
import { useAdminUserEditForm } from './useAdminUserForm';
import { useUpdateAdminUser } from '@/api';
import { useAuth, useDialogState } from '@/hooks';
import { AdminUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useAdminUserEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { AdminUserEditDialog, props, open };
}

function AdminUserEditDialog({
  openState,
  close,
  onSubmit,
  adminUser,
}: {
  openState: boolean;
  close: () => void;
  adminUser: AdminUser;
  onSubmit?: (adminUser: AdminUser) => void;
}) {
  const { trigger, isLoading } = useUpdateAdminUser();

  const { control, formState, handleSubmit, reset, watch } = useAdminUserEditForm(adminUser);

  const { profile: myProfile, logout } = useAuth();

  function handleClose() {
    reset();
    close();
  }

  useEffect(() => {
    reset({
      ...defaultAdminUserValues(adminUser),
    });
  }, [adminUser, reset]);

  const roleValue = watch('role');
  const isRoleDirty = roleValue !== adminUser.role;
  const isMe = myProfile?.id === adminUser.id;
  const shouldLogout = isRoleDirty && isMe;

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>アドミンユーザーを編集</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <AdminUserForm mode='edit' control={control} errors={formState.errors} />
        </Stack>
        {isRoleDirty && (
          <Alert severity='warning'>
            権限を変更する場合､対象のユーザーが強制的にログアウトされる場合があります｡
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!formState.isValid || !formState.isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            // try {
            const response = await trigger({
              urlParameter: {
                adminUserId: adminUser.id,
              },
              body: {
                username: data.username,
                name: data.name === '' ? null : data.name,
                email: data.email === '' ? null : data.email,
                company: data.company === '' ? null : data.company,
                department: data.department === '' ? null : data.department,
                position: data.position === '' ? null : data.position,
                role: data.role,
              },
            });
            reset();
            if (response) {
              onSubmit && onSubmit(response?.adminUser);
            }
            close();
            if (shouldLogout) {
              logout();
            }
            // } catch (error) {
            //   console.error(error);
            // }
          })}
        >
          変更
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useAdminUserEditDialog };
