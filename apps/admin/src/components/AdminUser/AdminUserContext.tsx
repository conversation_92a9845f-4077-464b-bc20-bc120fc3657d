import { PropsWithChildren, createContext, useContext, useMemo } from 'react';
import { RedirectToNotFound } from '../Routing';
import { AdminUser } from '@/types';

const AdminUserContext = createContext<{
  adminUser: AdminUser | undefined;
  isLoading: boolean;
  error?: any;
  fetch: (adminUserId: number) => void;
} | null>(null);

export { AdminUserContext };

function AdminUserProvider({
  children,
  adminUser,
  isLoading,
  error,
  fetchFn,
}: PropsWithChildren<{
  adminUser?: AdminUser;
  isLoading: boolean;
  error?: any;
  fetchFn: (adminUserId: number) => void;
}>) {
  const value = useMemo(() => {
    return {
      adminUser,
      isLoading,
      error,
      fetch: fetchFn,
    };
  }, [adminUser, isLoading, error, fetchFn]);

  if (error) {
    return <RedirectToNotFound error={error} />;
  }

  return <AdminUserContext.Provider value={value}>{children}</AdminUserContext.Provider>;
}

function useAdminUser() {
  const context = useContext(AdminUserContext);

  if (!context) {
    throw new Error('useAdminUser must be used within a AdminUserProvider');
  }
  return context;
}

export { AdminUserProvider, useAdminUser };
