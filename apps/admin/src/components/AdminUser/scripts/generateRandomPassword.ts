import { PASSWORD_SYMBOLS } from './CONSTANTS';

function generateRandomPassword() {
  const symbols = PASSWORD_SYMBOLS;
  const numbers = '0123456789';
  const lowercaseLetters = 'abcdefghijklmnopqrstuvwxyz';
  const uppercaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

  let str = '';
  str += symbols[Math.floor(Math.random() * symbols.length)];
  str += numbers[Math.floor(Math.random() * numbers.length)];
  str += lowercaseLetters[Math.floor(Math.random() * lowercaseLetters.length)];
  str += uppercaseLetters[Math.floor(Math.random() * uppercaseLetters.length)];

  const allChars = symbols + numbers + lowercaseLetters + uppercaseLetters;
  for (let i = str.length; i < 10; i++) {
    str += allChars[Math.floor(Math.random() * allChars.length)];
  }

  return str;
}

export { generateRandomPassword };
