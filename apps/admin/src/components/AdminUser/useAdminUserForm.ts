import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { adminUserAddSchema, adminUserEditSchema } from './adminUserSchema';
import { defaultAdminUserValues } from './defaultAdminUserValues';
import { AdminUser, Role } from '@/types';

type AdminUserAddFormValues = {
  username: string;
  password: string;
  name: string;
  email: string;
  company: string;
  department: string;
  position: string;
  role: Role;
};

type AdminUserEditFormValues = Omit<AdminUserAddFormValues, 'password'>;

function useAdminUserAddForm() {
  return useForm({
    mode: 'onChange',
    defaultValues: {
      username: '',
      password: '',
      name: '',
      email: '',
      company: '',
      department: '',
      position: '',
      role: 'general',
    },
    resolver: yupResolver(adminUserAddSchema),
  });
}

function useAdminUserEditForm(adminUser: AdminUser) {
  return useForm({
    mode: 'onChange',
    defaultValues: defaultAdminUserValues(adminUser),
    resolver: yupResolver(adminUserEditSchema),
  });
}

export { useAdminUserAddForm, useAdminUserEditForm };
export type { AdminUserAddFormValues, AdminUserEditFormValues };
