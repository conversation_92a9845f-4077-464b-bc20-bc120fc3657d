import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, Button, DialogActions } from '@mui/material';
import { PropsWithChildren, useState } from 'react';
import { CopyInfoContent, EcoisDialog } from '../Dialog';
import { Label, Value } from '../Display';
import { AdminUserForm } from './AdminUserForm';
import { AdminUserAddFormValues, useAdminUserAddForm } from './useAdminUserForm';
import { useCreateAdminUser } from '@/api';
import { useDialogState } from '@/hooks';
import { AdminUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useAdminUserAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { AdminUserAddDialog, props, open };
}

type pageState = 'add' | 'info';

function AdminUserAddDialog({
  openState,
  close,
  onSuccess,
  onClose,
  onCancel,
}: PropsWithChildren<{
  openState: boolean;
  close: () => void;
  onSuccess?: (adminUser: AdminUser) => void;
  onClose?: (adminUser: AdminUser) => void;
  onCancel?: () => void;
}>) {
  const [pageState, setPageState] = useState<pageState>('add');
  function changePageState(state: pageState) {
    setPageState(state);
  }

  const [createdUserInfo, setCreatedUserInfo] = useState<AdminUser | undefined>({} as AdminUser);
  function updateUserInfo(adminUser: AdminUser) {
    setCreatedUserInfo(adminUser);
  }

  const [formValues, setFormValues] = useState<AdminUserAddFormValues | undefined>(
    {} as AdminUserAddFormValues,
  );
  function updateFormValues(adminUser: AdminUserAddFormValues) {
    setFormValues(adminUser);
  }

  function handleOnClose() {
    close();

    // 遷移先のページに移動する前に、stateを初期化する
    // これをしないと、次回ダイアログを開いた時に、前回のstateが残っている
    // 閉じるを押した瞬間に初期化すると、ダイアログが閉じる前にstateが初期化されてしまう
    setTimeout(() => {
      setPageState('add');
    }, 500);

    if (createdUserInfo) onClose && onClose(createdUserInfo);
  }

  function handleOnSubmit(adminUserAddFormValues: AdminUserAddFormValues) {
    changePageState('info');
    updateFormValues(adminUserAddFormValues);
  }

  function handleOnSuccess(adminUser: AdminUser) {
    updateUserInfo(adminUser);
    onSuccess && onSuccess(adminUser);
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      {pageState === 'add' && (
        <AdminUserAddFrom
          close={close}
          onSubmit={handleOnSubmit}
          onSuccess={handleOnSuccess}
          onCancel={onCancel}
        />
      )}
      {pageState === 'info' && (
        <AdminUserInfoCopy close={close} formValues={formValues} onClose={handleOnClose} />
      )}
    </EcoisDialog>
  );
}

function AdminUserInfoCopy({
  formValues,
  close,
  onClose,
}: {
  formValues: AdminUserAddFormValues | undefined;
  close: () => void;
  onClose?: () => void;
}) {
  function handleCloseClick() {
    close();
    onClose && onClose();
  }

  const supportText = formValues?.email
    ? 'ユーザー名またはEmailとパスワードの組み合わせでログインできます。'
    : 'ユーザー名とパスワードの組み合わせでログインできます。';

  const usernameText = `ユーザー名\n${formValues?.username}`;
  const emailText = `Email\n${formValues?.email}`;
  const passwordText = `パスワード\n${formValues?.password}`;
  const brake = '\n\n';

  function generateCopyText(formValues: AdminUserAddFormValues) {
    return (
      supportText +
      '\n' +
      usernameText +
      brake +
      (formValues?.email && emailText + brake) +
      passwordText
    );
  }

  const clipboardText = formValues ? generateCopyText(formValues) : '';

  return (
    <CopyInfoContent
      titleText='アドミンユーザーが作成されました'
      bodyText={
        '以下のログイン情報をコピーして、アドミンユーザーに共有してください。\n※パスワードは一度しか表示されません。'
      }
      onClose={handleCloseClick}
      clipboardText={clipboardText}
      contentElements={
        <>
          <Label>ユーザー名</Label>
          <Value>{formValues?.username}</Value>
          {formValues?.email && (
            <>
              <Label>Email</Label>
              <Value>{formValues.email}</Value>
            </>
          )}
          <Label>パスワード</Label>
          <Value>{formValues?.password}</Value>
        </>
      }
    />
  );
}

function AdminUserAddFrom({
  close,
  onSubmit,
  onCancel,
  onSuccess,
}: {
  close: () => void;
  onSubmit?: (adminUser: AdminUserAddFormValues) => void;
  onSuccess?: (adminUser: AdminUser) => void;
  onCancel?: () => void;
}) {
  const { trigger, isLoading, reset: swrReset } = useCreateAdminUser();

  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset: formReset,
  } = useAdminUserAddForm();

  function resetCache() {
    swrReset();
    formReset();
  }

  function handleClose() {
    resetCache();
    close();
    onCancel && onCancel();
  }

  async function adminUserCreateRequest(data: AdminUserAddFormValues) {
    return await trigger({
      body: {
        username: data.username,
        password: data.password,
        role: data.role,
        ...(data.name !== '' && { name: data.name }),
        ...(data.email !== '' && { email: data.email }),
        ...(data.company !== '' && { company: data.company }),
        ...(data.department !== '' && { department: data.department }),
        ...(data.position !== '' && { position: data.position }),
      },
    });
  }
  const handleFormSubmit = handleSubmit(async (data) => {
    const response = await adminUserCreateRequest(data as AdminUserAddFormValues);
    if (response) {
      onSuccess && onSuccess(response.adminUser);
      onSubmit && onSubmit(data as AdminUserAddFormValues);
      resetCache();
    }
  });

  return (
    <>
      <DialogTitle id='alert-dialog-title'>アドミンユーザーを作成</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <AdminUserForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleFormSubmit}
        >
          作成
        </LoadingButton>
      </DialogActions>
    </>
  );
}
export { useAdminUserAddDialog };
