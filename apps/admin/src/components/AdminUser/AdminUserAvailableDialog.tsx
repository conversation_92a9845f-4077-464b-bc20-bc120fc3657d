import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  Stack,
  DialogActions,
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  FormLabel,
  Typography,
} from '@mui/material';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { EcoisDialog } from '../Dialog';
import { adminUserAvailableSchema } from './adminUserSchema';
import { useUpdateAdminUserAvailable } from '@/api';
import { useDialogState } from '@/hooks';
import { AdminUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useAdminUserAvailableDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { AdminUserAvailableDialog, props, open };
}

function AdminUserAvailableDialog({
  openState,
  close,
  onSubmit,
  adminUser,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (adminUser: AdminUser) => void;
  adminUser: AdminUser;
}) {
  const { trigger, isLoading } = useUpdateAdminUserAvailable();

  const {
    control,
    formState: { isValid, isDirty },
    handleSubmit,
    reset,
  } = useForm({
    defaultValues: {
      isAvailable: adminUser.isAvailable,
    },
    resolver: yupResolver(adminUserAvailableSchema),
  });

  function handleClose() {
    reset();
    close();
  }

  useEffect(() => {
    reset({
      isAvailable: adminUser.isAvailable,
    });
  }, [adminUser, reset]);

  function addCurrentStateLabel(state: boolean) {
    return adminUser.isAvailable === state ? ' (現在の状態)' : '';
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>アドミンユーザーの有効化切替</DialogTitle>
      <DialogContent>
        <Typography variant={'body2'} color={'textSecondary'}>
          有効化すると、アドミンユーザーはログインできるようになります。
          無効化すると、アドミンユーザーに関する情報は保持されますが、ログインできなくなります。
        </Typography>

        <Stack component={'form'} spacing={2} py={2}>
          <Controller
            name={'isAvailable'}
            control={control}
            render={({ field }) => (
              <FormControl>
                <FormLabel id='datapoint-calcMethod-label'>
                  切り替えたい状態を選択してください
                </FormLabel>
                <RadioGroup
                  {...field}
                  aria-labelledby='adminUser-isAvailable-label'
                  name='datapoint-calcMethod'
                >
                  <FormControlLabel
                    value={true}
                    control={<Radio />}
                    label={'有効' + addCurrentStateLabel(true)}
                  />
                  <FormControlLabel
                    value={false}
                    control={<Radio />}
                    label={'無効' + addCurrentStateLabel(false)}
                  />
                </RadioGroup>
              </FormControl>
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            const response = await trigger({
              body: {
                isAvailable: data.isAvailable,
              },
              urlParameter: {
                adminUserId: adminUser.id,
              },
            });
            reset();
            if (response) {
              onSubmit && onSubmit(response?.adminUser);
            }
            close();
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useAdminUserAvailableDialog };
