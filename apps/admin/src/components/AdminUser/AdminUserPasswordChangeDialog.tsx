import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  Stack,
  DialogActions,
  Button,
  Typography,
} from '@mui/material';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { CopyInfoContent, EcoisDialog } from '../Dialog';
import { Label, Value } from '../Display';
import { AdminUserPasswordField } from './AdminUserPasswordField';
import { adminUserPasswordSchema } from './adminUserSchema';
import { useUpdateAdminUserPassword } from '@/api';
import { useDialogState } from '@/hooks';
import { AdminUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useAdminUserPasswordChangeDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { AdminUserPasswordChangeDialog, props, open };
}

type AdminUserId = AdminUser['id'];
type PageState = 'passwordForm' | 'copyForm';
type FormValues = {
  password: string;
};

function AdminUserPasswordChangeDialog({
  openState,
  close,
  onSuccess,
  adminUser,
  onClose,
}: {
  onSuccess?: (adminUserId: AdminUserId) => void;
  onClose?: (adminUserId: AdminUserId) => void;
  adminUser: AdminUser;
  openState: boolean;
  close: () => void;
}) {
  const [pageState, setPageState] = useState<PageState>('passwordForm');
  function changePage(pageState: PageState) {
    setPageState(pageState);
  }

  const [passwordFormValues, setPasswordFormValues] = useState<FormValues>({} as FormValues);
  function updateSubmittedFormValues(formValues: FormValues) {
    setPasswordFormValues(formValues);
  }

  const [adminUserId, setAdminUserId] = useState<AdminUserId | undefined>(undefined);
  function updateAdminUserId(adminUserId: AdminUserId) {
    setAdminUserId(adminUserId);
  }

  function handleFormSubmit(formValues: FormValues) {
    updateSubmittedFormValues(formValues);
    changePage('copyForm');
  }
  function handleFormOnSuccess(adminUserId: AdminUserId) {
    updateAdminUserId(adminUserId);
    onSuccess && onSuccess(adminUserId);
  }

  function handleDialogClose() {
    adminUserId && onClose && onClose(adminUserId);
    close();

    setTimeout(() => {
      changePage('passwordForm');
      cleanUp();
    }, 500);
  }

  function cleanUp() {
    setAdminUserId(undefined);
    setPasswordFormValues({} as FormValues);
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      {pageState === 'passwordForm' && (
        <AdminUserPasswordChangeForm
          close={close}
          onSuccess={handleFormOnSuccess}
          onSubmit={handleFormSubmit}
          adminUser={adminUser}
        />
      )}
      {pageState === 'copyForm' && (
        <AdminUserPasswordCopy
          formValues={passwordFormValues}
          close={close}
          onClose={handleDialogClose}
        />
      )}
    </EcoisDialog>
  );
}

function AdminUserPasswordCopy({
  formValues,
  close,
  onClose,
}: {
  formValues: FormValues | undefined;
  close: () => void;
  onClose?: () => void;
}) {
  function handleCloseClick() {
    close();
    onClose && onClose();
  }

  function generateCopyText(formValues: FormValues) {
    return `パスワード\n${formValues.password}`;
  }

  const clipboardText = formValues ? generateCopyText(formValues) : '';

  return (
    <CopyInfoContent
      onClose={handleCloseClick}
      titleText='パスワードが変更されました'
      bodyText={
        '以下のログイン情報をコピーして、アドミンユーザーに共有してください。\n※パスワードは一度しか表示されません。'
      }
      clipboardText={clipboardText}
      contentElements={
        <>
          <Label>パスワード</Label>
          <Value>{formValues?.password}</Value>
        </>
      }
    />
  );
}

function AdminUserPasswordChangeForm({
  close,
  onSubmit,
  onSuccess,
  adminUser,
}: {
  close: () => void;
  onSubmit?: (formValues: FormValues) => void;
  onSuccess?: (adminUserId: AdminUserId) => void;
  adminUser: AdminUser;
}) {
  const { trigger, isLoading } = useUpdateAdminUserPassword();

  const {
    control,
    formState: { isValid, isDirty, errors },
    handleSubmit,
    reset,
  } = useForm({
    defaultValues: {
      password: '',
    },
    resolver: yupResolver(adminUserPasswordSchema),
  });

  function handleCancel() {
    reset();
    close();
  }

  async function requestChangePassword(password: string) {
    try {
      const response = await trigger({
        body: {
          password,
        },
        urlParameter: {
          adminUserId: adminUser.id,
        },
      });
      return response;
    } catch (error) {
      console.error(error);
    }
  }

  async function handleFormSubmit(formValues: { password: string }) {
    const response = await requestChangePassword(formValues.password);
    if (response) {
      onSuccess && onSuccess(response?.adminUserId);
    }
    onSubmit && onSubmit(formValues);
    reset();
  }

  return (
    <>
      <DialogTitle id='alert-dialog-title'>パスワードの変更</DialogTitle>
      <DialogContent>
        <Typography variant={'body2'} color={'textSecondary'}>
          新しいパスワードを入力してください。
        </Typography>
        <Stack component={'form'} spacing={2} py={2}>
          <AdminUserPasswordField control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit((data) => {
            handleFormSubmit(data);
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </>
  );
}
export { useAdminUserPasswordChangeDialog };
