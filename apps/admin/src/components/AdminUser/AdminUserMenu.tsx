import { Grid, Divider } from '@mui/material';
import { SubMenu, SubMenuItem } from '@/components/SubMenu';

// TODO 共通化コンポートにする
function AdminUserMenu() {
  return (
    <Grid container>
      <Grid item>
        <SubMenu>
          <SubMenuItem to='detail' primary='基本情報' />
          <SubMenuItem to='setting' primary='設定' />
        </SubMenu>
      </Grid>
      <Divider orientation='vertical' flexItem />
    </Grid>
  );
}

export { AdminUserMenu };
