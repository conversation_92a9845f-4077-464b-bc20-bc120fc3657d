import * as yup from 'yup';

import { PASSWORD_SYMBOLS_REGEXP } from './scripts/CONSTANTS';
import { RoleEnum } from '@/types';

const adminUserPasswordCondition = yup
  .string()
  .required('パスワードは必須です')
  .matches(/[a-z]/, '少なくとも1文字以上の半角小英字が必要です')
  .matches(/[A-Z]/, '少なくとも1文字以上の半角大英字が必要です')
  .matches(/[0-9]/, '少なくとも1文字以上の半角数字が必要です')
  .matches(PASSWORD_SYMBOLS_REGEXP, '少なくとも1文字以上の記号が必要です')
  .min(10, 'パスワードは10文字以上でなければなりません');

const adminUserAddSchema = yup.object().shape({
  username: yup
    .string()
    .required('ユーザー名は必須です')
    .matches(/^[0-9a-zA-Z]+$/, '半角英数字のみ利用できます')
    .min(6, '6文字以上必要です'),
  name: yup.string(),
  email: yup.string().email('メールアドレスの形式が正しくありません'),
  company: yup.string(),
  department: yup.string(),
  position: yup.string(),
  password: adminUserPasswordCondition,
  role: yup.mixed().oneOf(Object.values(RoleEnum)),
});

const adminUserEditSchema = adminUserAddSchema.omit(['password']);

const adminUserPasswordSchema = yup.object().shape({
  password: adminUserPasswordCondition,
});

const adminUserAvailableSchema = yup.object().shape({
  isAvailable: yup.boolean().required(),
});

export {
  adminUserAddSchema,
  adminUserEditSchema,
  adminUserPasswordSchema,
  adminUserAvailableSchema,
};
