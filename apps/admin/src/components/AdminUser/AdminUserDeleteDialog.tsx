import { Box, Paper, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useDeleteAdminUser } from '@/api';
import { DeleteDialog, useDeleteDialog } from '@/components/Dialog/DeleteDialog';
import { AdminUser } from '@/types';

function AdminUserDeleteDialog({
  adminUser,
  openState,
  close,
}: {
  adminUser: AdminUser;
  openState: boolean;
  close: () => void;
}) {
  const navigate = useNavigate();

  const { trigger: deleteAdminUser, isLoading: deleteAdminUserIsLoading } = useDeleteAdminUser();

  return (
    <DeleteDialog
      {...{ openState, close }}
      title={`アドミンユーザーを削除`}
      confirmText='削除するとアドミンユーザーのデータは削除されます。注意して作業してください。'
      isLoading={deleteAdminUserIsLoading}
      onSubmit={async () => {
        try {
          await deleteAdminUser({
            urlParameter: { adminUserId: adminUser?.id },
          });
          navigate('/adminusers');
        } catch (e) {
          return Promise.reject(e);
        }
      }}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象のユーザー</Typography>
            <Typography variant='body1'>ユーザーネーム: {adminUser.username}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}

function useAdminUserDeleteDialog() {
  const { props, open } = useDeleteDialog();
  return {
    AdminUserDeleteDialog,
    props,
    open,
  };
}

export { useAdminUserDeleteDialog };
