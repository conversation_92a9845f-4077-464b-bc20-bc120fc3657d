import { Edit, Delete } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON> } from '@mui/material';
import { useEffect, useState } from 'react';
import {
  ColumnListItem,
  ColumnListTitleButton,
  CLMenuItem,
  CLDetailPaneContentItem,
  CLDetailPaneContentLabel,
  CLDetailPaneContentValue,
  CLDetailPaneTitleButton,
} from '../ColumnList/ColumnList';
import { ColumnListBase } from '../ColumnList/ColumnListBase';
import { useMultiColumnViewContext } from '../ColumnList/context/MultiColumViewContext';
import { ColumnProps } from '../ColumnList/type';
import { useDeleteDialog } from '../Dialog/DeleteDialog';
import { useUnitAddDialog } from './Dialog/UnitAddDialog';
import { useUnitEditDialog } from './Dialog/UnitEditDialog';
import { useDeleteUnit } from '@/api';

function UnitColumn({
  mode = 'view',
  checkboxSelection = false,
  onSelectionModelChange,
}: ColumnProps) {
  const {
    location: { selectedId: selectedLocationId },
    unit: { data, selectedId, selectedItem, updateSelectedId, isLoading, error, fetch },
    datapoint: {
      fetch: fetchDatapoint,
      emptyData: emptyDatapoint,
      updateSelectedId: updateDatapointId,
    },
  } = useMultiColumnViewContext();

  const selectedUnitItem = selectedItem;

  const { trigger: deleteUnit, isLoading: isDeleting } = useDeleteUnit();

  const [isAllItemShow, setIsAllItemShow] = useState(false);
  // ロケーションが変更されたら、「すべてのデータポイントを表示」の選択状態を解除
  useEffect(() => {
    setIsAllItemShow(false);
  }, [selectedLocationId]);

  // ダイアログ
  const { UnitAddDialog, open: openAddDialog, props: addDialogProps } = useUnitAddDialog();
  const { UnitEditDialog, open: openEditDialog, props: editDialogProps } = useUnitEditDialog();

  const { DeleteDialog, open: openDeleteDialog, props: deleteDialogProps } = useDeleteDialog();

  return (
    <>
      <ColumnListBase
        title='ユニット'
        data={data}
        showID={true}
        error={error}
        isLoading={isLoading}
        checkboxSelection={checkboxSelection}
        onCheckedItemsChange={onSelectionModelChange}
        onUpdateSelectedId={updateSelectedId}
        onListItemClick={(unit) => {
          setIsAllItemShow(false);
          fetchDatapoint([unit.id]);
        }}
        listControlButtons={(unit) =>
          mode === 'edit' && (
            <Button
              size='small'
              startIcon={<Edit />}
              onClick={() => {
                updateSelectedId(unit.id);
                fetchDatapoint([unit.id]);
                openEditDialog();
              }}
            >
              編集
            </Button>
          )
        }
        selectedId={selectedId}
        topOfListItem={
          data && (
            <ColumnListItem
              selected={isAllItemShow}
              onClick={() => {
                // 複数のunitIdに紐づく、すべてのデータポイントを取得
                updateSelectedId(null);
                fetchDatapoint(data.map((unit) => unit.id));
                setIsAllItemShow(true);

                // fetchDatapoint();
              }}
            >
              すべてのデータポイントを表示
            </ColumnListItem>
          )
        }
        normalStateButton={
          mode === 'edit' && (
            <ColumnListTitleButton
              disabled={!data}
              onClick={() => {
                openAddDialog();
              }}
            >
              追加
            </ColumnListTitleButton>
          )
        }
        optionMenuButtons={
          mode === 'edit' && (
            <CLDetailPaneTitleButton label={'オプション'}>
              <CLMenuItem
                label='編集'
                icon={<Edit />}
                onClick={(event) => {
                  openEditDialog();
                  console.log(event);
                }}
              />
              <CLMenuItem
                label='削除'
                icon={<Delete />}
                color='error'
                onClick={(event) => {
                  openDeleteDialog();
                  console.log(event);
                }}
              />
            </CLDetailPaneTitleButton>
          )
        }
        detailContents={
          <>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>ユニットID</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedUnitItem?.id}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>ユニットNo</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedUnitItem?.no}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>SIM ID</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                <Link
                  href={`https://console.soracom.io/sims?coverage_type=jp&search=%7B%22searchType%22:%22AND%22,%22text%22:%7B%22category%22:%22simId%22,%22value%22:%22${selectedUnitItem?.simId}%22%7D%7D`}
                  target='_blank'
                >
                  {selectedUnitItem?.simId}
                </Link>
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>ソースタイプ</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedUnitItem?.sourceType}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
          </>
        }
      />
      {selectedLocationId && data && (
        <UnitAddDialog
          {...addDialogProps}
          onSubmit={() => {
            fetch([selectedLocationId]);
          }}
          unitNos={data?.map((unit) => unit.no)}
          selectedLocationId={selectedLocationId}
        />
      )}
      {selectedUnitItem && data && (
        <>
          <UnitEditDialog
            {...editDialogProps}
            unit={selectedUnitItem}
            unitNos={data?.map((unit) => unit.no)}
            onSubmit={() => {
              fetch([selectedUnitItem.locationId]);
            }}
          />
          <DeleteDialog
            {...deleteDialogProps}
            title={`${selectedUnitItem.name}の削除`}
            isLoading={isDeleting}
            onSubmit={async () => {
              try {
                await deleteUnit({
                  urlParameter: { unitId: selectedUnitItem.id },
                });
                fetch([selectedUnitItem.locationId]);
                updateSelectedId(null);
                emptyDatapoint();
                updateDatapointId(null);
              } catch (e) {
                return Promise.reject(e);
              }
            }}
          />
        </>
      )}
    </>
  );
}

export { UnitColumn };
