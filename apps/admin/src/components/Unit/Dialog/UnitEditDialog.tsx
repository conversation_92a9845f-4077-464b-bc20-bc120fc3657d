import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { useEffect } from 'react';
import { defaultUnitValues } from './defautUnitValues';
import { UnitForm } from './UnitForm';
import { useUnitForm } from './useUnitForm';
import { useUpdateUnit } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Unit } from '@/types';

/* 

Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useUnitEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { UnitEditDialog, props, open };
}

function UnitEditDialog({
  openState,
  close,
  onSubmit,
  unit,
  unitNos,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (location: Unit) => void;
  unit: Unit;
  unitNos: number[];
}) {
  const { trigger, isLoading, reset: requestReset } = useUpdateUnit();

  const {
    control,
    formState: { errors, isValid, isDirty },
    handleSubmit,
    reset,
  } = useUnitForm({
    defaultValues: unit,
    unitNos,
  });

  useEffect(() => {
    reset({ ...defaultUnitValues(unit) });
  }, [reset, unit]);

  function handleClose() {
    requestReset();
    reset();
    close();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>ユニットを編集</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <UnitForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isDirty || !isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                body: {
                  locationId: unit.locationId,
                  name: data.name,
                  no: Number(data.no),
                  sourceType: data.sourceType,
                  simId: data.simId,
                },
                urlParameter: {
                  unitId: unit.id,
                },
              });

              if (response) {
                const { name, no, sourceType, simId } = response.unit;
                reset({
                  name,
                  no,
                  sourceType,
                  simId,
                });
                onSubmit && onSubmit(response?.unit);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          編集
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useUnitEditDialog };
