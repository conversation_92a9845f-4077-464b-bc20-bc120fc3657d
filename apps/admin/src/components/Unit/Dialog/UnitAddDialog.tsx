import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { UnitForm } from './UnitForm';
import { useUnitForm } from './useUnitForm';
import { useCreateUnit } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Unit } from '@/types';

/* 

Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useUnitAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { UnitAddDialog, props, open };
}

function UnitAddDialog({
  openState,
  close,
  onSubmit,
  selectedLocationId,
  unitNos,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (location: Unit) => void;
  selectedLocationId: number;
  unitNos: number[];
}) {
  const { trigger, isLoading, reset: requestReset } = useCreateUnit();
  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset,
  } = useUnitForm({
    unitNos,
  });

  function handleClose() {
    requestReset();
    reset();
    close();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>ユニットを追加</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <UnitForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                body: {
                  locationId: selectedLocationId,
                  name: data.name,
                  no: Number(data.no),
                  simId: data.simId,
                  sourceType: data.sourceType,
                },
              });
              reset();
              if (response) {
                onSubmit && onSubmit(response?.unit);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useUnitAddDialog };
