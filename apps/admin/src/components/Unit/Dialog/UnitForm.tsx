import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

function UnitForm({ control, errors }: { control: any; errors: any }) {
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='unit-name'
            label='ユニット名(必須)'
            variant='outlined'
            error={'name' in errors}
            helperText={'name' in errors ? errors.name?.message : ''}
          />
        )}
      />
      <Controller
        name='no'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='unit-no'
            type='number'
            label='ユニットNo(必須)'
            variant='outlined'
            error={'no' in errors}
            helperText={'no' in errors ? errors.no?.message : ''}
          />
        )}
      />
      <Controller
        name='sourceType'
        control={control}
        render={({ field }) => (
          <FormControl>
            <InputLabel id='sourceTypeLabel'>ソースタイプ</InputLabel>
            <Select
              {...field}
              labelId='sourceTypeLabel'
              id='unit-sourceType'
              label='ソースタイプ'
              variant='outlined'
              error={'sourceType' in errors}
              defaultValue={'keyence'}
            >
              <MenuItem value={'keyence'}>keyence</MenuItem>
            </Select>
          </FormControl>
        )}
      />
      <Controller
        name='simId'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='unit-simId'
            label='SIM ID'
            variant='outlined'
            error={'simId' in errors}
            helperText={'simId' in errors ? errors.simId?.message : ''}
          />
        )}
      />
    </>
  );
}

export { UnitForm };
