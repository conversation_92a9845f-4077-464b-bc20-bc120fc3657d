import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { defaultUnitValues } from './defautUnitValues';
import { Unit } from '@/types';

const schema = ({
  defaultValues,
  unitNos,
}: {
  unitNos: number[];
  defaultValues: Partial<Unit> | undefined;
}) => {
  const filterDefaultUnitNo = (unitNo: number) => unitNo !== defaultValues?.no;

  return yup.object().shape({
    name: yup.string().required('ユニット名は必須です'),
    no: yup
      .string()
      .required('必須項目です')
      .matches(/^\d+$/, '整数のみ入力可能です')
      .matches(/^[1-9]\d*$/, 'ゼロで始まらない数値が入力可能です')
      .required('ユニットnoは必須です')
      .test(
        'is-unique',
        '重複するユニットNoが存在します',
        (value) => !unitNos.filter(filterDefaultUnitNo).includes(Number(value)),
      ),
    sourceType: yup.string().required('ソースタイプは必須です'),
    simId: yup.string(),
  });
};

function useUnitForm({
  defaultValues,
  unitNos,
}: {
  defaultValues?: Partial<Unit>;
  unitNos: number[];
}) {
  return useForm({
    mode: 'onChange',
    defaultValues: {
      ...defaultUnitValues(defaultValues),
    },
    resolver: yupResolver(schema({ unitNos, defaultValues })),
  });
}

export { schema, useUnitForm };
