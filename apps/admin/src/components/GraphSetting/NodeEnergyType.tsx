import { Box, Stack, Typography } from '@mui/material';
import { EnergyTypeColorIcons } from 'lib/Component/Icons/EnergyTypeIcons';
import { TreeNodeWithHash } from '../../types/Types';

function NodeEnergyType({ node }: { node: TreeNodeWithHash }) {
  return (
    <Box>
      <Typography mb={1} variant={'body1'}>
        エネルギータイプ
      </Typography>
      <Stack direction={'row'} alignItems={'center'} gap={0.5}>
        <EnergyTypeColorIcons energyTypeId={node?.energyType.id} />
        <Typography variant={'body2'}>
          {node?.energyType.name} ({node?.energyType.unit})
        </Typography>
      </Stack>
    </Box>
  );
}

export { NodeEnergyType };
