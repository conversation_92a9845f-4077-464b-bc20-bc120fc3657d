import { Node, TreeNode, TreeNodeWithHash } from '../../../types';

function addChildrenToNodes(nodes: Node[]): TreeNode[] {
  return nodes.map((node) => addChildrenToNode(node));
}

function addChildrenToNode(node: Node): TreeNode {
  return {
    ...node,
    children: [],
  };
}

function toTreeNodeWithHash(node: Node): TreeNodeWithHash {
  return {
    ...node,
    hash: generateHash(node),
    children: [],
  };
}

/* 

利用しているTree表示ライブラリのreact-arboristはString型のidを受け取るようになっているため、
Nodeにハッシュ値を追加する必要がある。

*/

function addHashProperty(nodes: TreeNode[]): TreeNodeWithHash[] {
  return nodes.map((node) => addHashToNode(node));
}

function addHashToNode(node: TreeNode): TreeNodeWithHash {
  const newNode: TreeNodeWithHash = {
    ...node,
    hash: generateHash(node),
    children: addHashProperty(node.children),
  };
  return newNode;
}

/* 

元々Nodeにはユニークなnumber型のidがあるので、
それをstring型に変換しハッシュ値として利用する。

*/
function generateHash(node: Node): string {
  return `${node.id}`;
}

export {
  addHashProperty,
  addHashToNode,
  addChildrenToNodes,
  addChildrenToNode,
  toTreeNodeWithHash,
};
