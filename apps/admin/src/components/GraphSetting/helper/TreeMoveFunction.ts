import { TreeNodeWithHash } from '../../../types';

function moveItem(
  items: TreeNodeWithHash[],
  id: number,
  direction: 'up' | 'down',
): TreeNodeWithHash[] {
  const copiedItems: TreeNodeWithHash[] = JSON.parse(JSON.stringify(items));

  function swap(items: TreeNodeWithHash[], index1: number, index2: number): void {
    const temp = items[index1];
    items[index1] = items[index2];
    items[index2] = temp;
  }

  function findAndMove(nodes: TreeNodeWithHash[]): boolean {
    const index = nodes.findIndex((node) => node.id === id);

    if (index !== -1) {
      if (direction === 'up' && index > 0) {
        swap(nodes, index, index - 1);
        return true;
      } else if (direction === 'down' && index < nodes.length - 1) {
        swap(nodes, index, index + 1);
        return true;
      }
      return true;
    }

    return nodes.some((node) => findAndMove(node.children));
  }

  if (!findAndMove(copiedItems)) {
    throw new Error('Item not found');
  }

  return copiedItems;
}

function countItems(items: TreeNodeWithHash[]): number {
  let count = items.length;

  function countChildren(item: TreeNodeWithHash): void {
    count += item.children.length;
    item.children.forEach((child) => {
      countChildren(child);
    });
  }

  items.forEach((item) => {
    countChildren(item);
  });

  return count;
}

function addItem(
  items: TreeNodeWithHash[],
  newItem: TreeNodeWithHash,
  selectedNodeId: number | null,
): TreeNodeWithHash[] {
  const itemsCopy = JSON.parse(JSON.stringify(items)) as TreeNodeWithHash[];

  function findAndAddItem(parentNodes: TreeNodeWithHash[], node: TreeNodeWithHash): boolean {
    if (node.id === selectedNodeId) {
      if (node.children.length >= 0) {
        node.children.push(newItem);
      } else {
        parentNodes.push(newItem);
      }
      return true;
    }

    return node.children.some((child) => findAndAddItem(node.children, child));
  }

  if (selectedNodeId) {
    itemsCopy.some((item) => findAndAddItem(itemsCopy, item));
  } else {
    itemsCopy.push(newItem);
  }

  // ここでソート条件に従ってソートする

  return itemsCopy;
}

function moveItemById(
  items: TreeNodeWithHash[],
  itemToMove: TreeNodeWithHash,
  parentId: number | null,
  targetIndex: number,
): TreeNodeWithHash[] {
  const itemsCopy = JSON.parse(JSON.stringify(items)) as TreeNodeWithHash[];

  function removeItem(nodes: TreeNodeWithHash[]): boolean {
    const index = nodes.findIndex((node) => node.id === itemToMove.id);

    if (index !== -1) {
      nodes.splice(index, 1);
      return true;
    }
    for (const node of nodes) {
      const found = removeItem(node.children);
      if (found) {
        return true;
      }
    }
    return false;
  }

  function findAndInsertItem(nodes: TreeNodeWithHash[], item: TreeNodeWithHash): boolean {
    if (parentId === null) {
      nodes.splice(targetIndex, 0, item);
      return true;
    } else {
      const index = nodes.findIndex((node) => node.id === parentId);
      if (index !== -1) {
        nodes[index].children.splice(targetIndex, 0, item);
        return true;
      }
      return nodes.some((node) => findAndInsertItem(node.children, item));
    }
  }

  if (!removeItem(itemsCopy)) {
    throw new Error('Item not found');
  }

  if (parentId === null) {
    itemsCopy.splice(targetIndex, 0, itemToMove);
  } else {
    if (!findAndInsertItem(itemsCopy, itemToMove)) {
      throw new Error('Parent not found');
    }
  }

  // ここでソート条件に従ってソートする

  return itemsCopy;
}

function calculateNewOrderByDirection(
  nodeList: TreeNodeWithHash[],
  nodeId: number,
  direction: 'up' | 'down',
): number | undefined {
  for (let i = 0; i < nodeList.length; i++) {
    if (nodeList[i].id === nodeId) {
      if (direction === 'up' && i > 1) {
        // 上から2番めを上に移動した場合、最初のノードのorderの1/2倍とする
        return (nodeList[i - 1].order + nodeList[i - 2].order) / 2;
      } else if (direction === 'down' && i < nodeList.length - 2) {
        // 下から2番めを下に移動した場合、最後のノードのorderの1/2倍とする
        return (nodeList[i + 1].order + nodeList[i + 2].order) / 2;
      } else if (direction === 'up' && i === 1) {
        // 上から2番めを上に移動した場合、最初のノードのorderの1/2倍とする
        return nodeList[i - 1].order / 2;
      } else if (direction === 'down' && i === nodeList.length - 2) {
        // 下から2番めを下に移動した場合、最後のノードのorderの20倍とする
        return nodeList[i].order * 2 * 10;
      }
    }

    if (nodeList[i].children.length > 0) {
      const result = calculateNewOrderByDirection(nodeList[i].children, nodeId, direction);
      if (result) {
        return result;
      }
    }
  }
}

function findNodeById(
  nodeList: TreeNodeWithHash[],
  targetId: number | null,
): TreeNodeWithHash | undefined {
  if (targetId === null) {
    return undefined;
  }
  for (const node of nodeList) {
    if (node.id === targetId) {
      return node;
    }
    const childNode = findNodeById(node.children, targetId);
    if (childNode) {
      return childNode;
    }
  }
}

function calculateNewOrderByIndex(
  nodeList: TreeNodeWithHash[],
  targetIndex: number,
  targetNodeParentId: number | null,
): number | undefined {
  // parentNodeを探すしてChildrenを得る

  function calculateOrder(nodeList: TreeNodeWithHash[], targetIndex: number) {
    if (targetIndex === 0) {
      // console.log('TOP');
      return nodeList[0].order / 2;
    } else if (targetIndex >= nodeList.length) {
      // console.log('BOTTOM');
      return nodeList[nodeList.length - 1].order * 2;
    } else {
      // console.log('MIDDLE');
      return (nodeList[targetIndex - 1].order + nodeList[targetIndex].order) / 2;
    }
  }

  const parent = findNodeById(nodeList, targetNodeParentId);

  if (parent && parent.children) {
    const children = parent.children;
    if (children.length === 0) {
      return undefined;
    }
    return calculateOrder(children, targetIndex);
  } else {
    return calculateOrder(nodeList, targetIndex);
  }
}

export {
  moveItem,
  countItems,
  addItem,
  moveItemById,
  calculateNewOrderByDirection,
  calculateNewOrderByIndex,
};
