import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Typography, Stack, TextField, Button, CircularProgress } from '@mui/material';
import { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { useUpdateNode } from '../../api';
import { TreeNodeWithHash } from '../../types/Types';

const schema = yup.object().shape({
  name: yup.string().required('ノード名称は必須です'),
});

function NodeNameField({
  node,
  onSubmit,
}: {
  node: TreeNodeWithHash | null;
  onSubmit?: (node: TreeNodeWithHash) => void;
}) {
  const { trigger, isLoading } = useUpdateNode();
  const { control, handleSubmit, reset, formState, setValue } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: node ? node.name : '',
    },
  });

  useEffect(() => {
    reset({ name: node ? node.name : '' }, { keepDirty: true });
  }, [node, reset]);

  return (
    <Box>
      <Typography mb={1} variant={'body1'}>
        ノード名称
      </Typography>
      <Stack direction={'row'}>
        <Controller
          name='name'
          control={control}
          render={({ field, fieldState }) => (
            <TextField
              {...field}
              name='name'
              size='small'
              variant='outlined'
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              id='nodeTitle'
              defaultChecked
            />
          )}
        />
        {formState.isDirty && (
          <Stack direction={'row'} alignItems={'flex-start'} spacing={1} ml={2}>
            {isLoading ? (
              <CircularProgress />
            ) : (
              <>
                <Button
                  onClick={() => {
                    reset({ name: node ? node.name : '' });
                  }}
                  variant='outlined'
                  color='error'
                >
                  キャンセル
                </Button>
                <Button
                  type='submit'
                  onClick={() => {
                    handleSubmit(async (data) => {
                      if (node) {
                        try {
                          const response = await trigger({
                            urlParameter: {
                              nodeId: node && node.id,
                            },
                            body: {
                              ...node,
                              name: data.name,
                            },
                          });
                          if (response) {
                            setValue('name', response.node.name);
                          }
                          reset(data);
                          onSubmit && onSubmit({ ...node, name: data.name });
                        } catch (error) {
                          reset();
                          console.error(error);
                        }
                      }
                    })();
                  }}
                  variant='contained'
                  color='primary'
                  disableElevation
                >
                  保存
                </Button>
              </>
            )}
          </Stack>
        )}
      </Stack>
    </Box>
  );
}

export { NodeNameField };
