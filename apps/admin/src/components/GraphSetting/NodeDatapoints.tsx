import { Box, Typography, Paper, Stack, Card, CardContent } from '@mui/material';
import { TreeNodeWithHash } from '../../types';

function NodeDatapoints({ node }: { node: TreeNodeWithHash | null }) {
  return (
    <Box>
      <Typography mb={1} variant={'body1'}>
        データポイント
      </Typography>
      <Typography variant={'body2'} color={'text.secondary'} mb={2}>
        選択しているデータポイントの合計値がグラフに表示されます。
      </Typography>
      <Paper variant={'outlined'}>
        <Box p={2}>
          <Stack spacing={2}>
            {node && node.datapoints.length > 0 ? (
              node.datapoints.map((datapoint) => {
                return (
                  <Card key={datapoint.id} variant={'outlined'}>
                    <CardContent>
                      <Stack
                        direction={'row'}
                        justifyContent={'space-start'}
                        alignItems={'end'}
                        spacing={3}
                      >
                        <Box>
                          <Typography variant={'caption'}>データポイント</Typography>
                          <Typography>{datapoint.name}</Typography>
                        </Box>
                        <Stack direction={'row'} alignItems={'center'}>
                          <Box>
                            <Typography variant={'caption'}>係数</Typography>
                            <Typography variant='body1' color='text.secondary'>
                              {datapoint.coefficient}
                            </Typography>
                          </Box>
                        </Stack>
                      </Stack>
                    </CardContent>
                  </Card>
                );
              })
            ) : (
              <Typography variant={'body2'}>データポイントが選択されていません</Typography>
            )}
          </Stack>
        </Box>
      </Paper>
    </Box>
  );
}

export { NodeDatapoints };
