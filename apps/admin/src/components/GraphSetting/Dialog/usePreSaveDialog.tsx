import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Stack,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';

const schema = yup.object({
  saveAnyway: yup.boolean(),
});

function usePreSaveDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { PreSaveDialog, props, open };
}

function PreSaveDialog({
  openState,
  close,
  onCancel,
  onSubmit,
}: {
  openState: boolean;
  close: () => void;
  onCancel?: () => void;
  onSubmit?: () => void;
}) {
  const limit_depth = 4;
  const {
    control,
    watch,
    handleSubmit,
    reset: formReset,
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      saveAnyway: false,
    },
    resolver: yupResolver(schema),
  });

  function resetCache() {
    formReset();
  }

  function handleCancel() {
    resetCache();
    onCancel?.();
    close();
  }

  const handleOnFormSubmit = handleSubmit(async () => {
    onSubmit?.();
    resetCache();
    close();
  });

  const { saveAnyway } = watch();

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>保存時の注意</DialogTitle>
      <DialogContent>
        <DialogContentText>
          KENES連携中の案件は{limit_depth}階層以上のノードに対応していません｡
          {limit_depth}階層以上のノードはKENESシステムで表示されませんが､保存することができます｡
        </DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            name='saveAnyway'
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={<Checkbox {...field} checked={field.value} />}
                label={'警告を理解した上で保存する'}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton variant='contained' disabled={!saveAnyway} onClick={handleOnFormSubmit}>
          保存
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { usePreSaveDialog };
