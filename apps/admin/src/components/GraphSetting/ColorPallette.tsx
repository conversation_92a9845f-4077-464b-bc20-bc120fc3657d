import { Check, Circle } from '@mui/icons-material';
import { ChipProps, Chip, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { NodeColors } from './type';

type ColorPalletteState = {
  red: boolean;
  yellow: boolean;
  blue: boolean;
  green: boolean;
};

const initialState: ColorPalletteState = {
  red: false,
  yellow: false,
  blue: false,
  green: false,
};

function ColorPallette({
  initialValue,
  onChange,
}: {
  initialValue?: NodeColors;
  onChange?: (color: NodeColors) => void;
}) {
  const [state, setState] = useState(
    initialValue ? { ...initialState, [initialValue]: true } : { ...initialState, blue: true },
  );

  const handleClick = (color: NodeColors) => {
    setState(() => ({
      ...initialState,
      [color]: true,
    }));
    onChange && onChange(color);
  };

  useEffect(() => {
    if (initialValue) {
      setState(() => ({
        ...initialState,
        [initialValue]: true,
      }));
    }
  }, [initialValue]);

  function ColorChip({ label, color, isActive, ...props }: ChipProps & { isActive: boolean }) {
    const variant = isActive ? 'filled' : 'outlined';
    const icon = isActive ? <Check /> : <Circle />;

    return <Chip label={label} variant={variant} color={color} icon={icon} clickable {...props} />;
  }

  return (
    <Stack direction={'row'} spacing={0.5}>
      <ColorChip
        label='青系'
        color={'primary'}
        isActive={state.blue}
        onClick={() => {
          handleClick('blue');
        }}
      />
      <ColorChip
        label='赤系'
        color={'error'}
        isActive={state.red}
        onClick={() => {
          handleClick('red');
        }}
      />
      <ColorChip
        label='黄系'
        color={'attention'}
        isActive={state.yellow}
        onClick={() => {
          handleClick('yellow');
        }}
      />
      <ColorChip
        label='緑系'
        color={'success'}
        isActive={state.green}
        onClick={() => {
          handleClick('green');
        }}
      />
    </Stack>
  );
}

export { ColorPallette };
