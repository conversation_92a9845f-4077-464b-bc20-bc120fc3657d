import { useCallback, useMemo, useState } from 'react';
import { TreeA<PERSON> } from 'react-arborist';
import { TreeNode, TreeNodeWithHash } from '../../../types';
import { addHashProperty, addHashToNode } from '../helper/addHash';
import { addItem, moveItem, moveItemById } from '../helper/TreeMoveFunction';

type UseTreeDataOptions = {
  maxDepth: number;
};

function useTreeData(initialData: TreeNode[] | undefined = [], options?: UseTreeDataOptions) {
  const memoizedInitialData = useMemo(() => initialData, [initialData]);
  const nodeWithHash = addHashProperty(memoizedInitialData);

  const [treeData, setTreeData] = useState<TreeNodeWithHash[]>(nodeWithHash);

  function setValue(nodes: TreeNode[]) {
    setTreeData(addHashProperty(nodes));
  }

  const updateNode = useCallback((updatedNode: TreeNodeWithHash) => {
    setTreeData((prevTreeData) => {
      const updateNodeInTree = (tree: TreeNodeWithHash[]): TreeNodeWithHash[] => {
        return tree.map((node) => {
          if (node.id === updatedNode.id) {
            return updatedNode;
          }
          if (node.children.length > 0) {
            return { ...node, children: updateNodeInTree(node.children) };
          }
          return node;
        });
      };

      return updateNodeInTree(prevTreeData);
    });
  }, []);

  const deleteNode = useCallback((nodeId: number) => {
    setTreeData((prevTreeData) => {
      const removeNodeInTree = (tree: TreeNodeWithHash[]): TreeNodeWithHash[] => {
        return tree.filter((node) => {
          if (node.id === nodeId) {
            return false;
          }
          if (node.children.length > 0) {
            node.children = removeNodeInTree(node.children);
          }
          return true;
        });
      };

      return removeNodeInTree(prevTreeData);
    });
  }, []);

  const moveUpward = useCallback((nodeId: number) => {
    setTreeData((prevTreeData) => {
      const newTreeData = moveItem(prevTreeData, nodeId, 'up');
      return newTreeData;
    });
  }, []);

  const moveDownward = useCallback((nodeId: number) => {
    setTreeData((prevTreeData) => {
      const newTreeData = moveItem(prevTreeData, nodeId, 'down');
      return newTreeData;
    });
  }, []);

  const addNode = useCallback(
    (targetNodeId: number | null, newNode: TreeNodeWithHash, treeApiRef: TreeApi<any>) => {
      const newNodeWithHash = addHashToNode(newNode);

      setTreeData((prevTreeData) => {
        const newTree = addItem(prevTreeData, newNodeWithHash, targetNodeId);
        return newTree;
      });
      if (targetNodeId) {
        treeApiRef.open(targetNodeId.toString());
      }
    },
    [],
  );

  const dndNode = (dragEvent: any, itemToMove: TreeNodeWithHash) => {
    const { parentId, index } = dragEvent;
    const numberedParentId = parentId === null ? null : Number(parentId);
    setTreeData((prev) => moveItemById(prev, itemToMove, numberedParentId, index));
  };

  // TODO Childrenを削除する処理を追加する
  const limitDepth = (nodes: TreeNodeWithHash[], currentDepth = 0): TreeNodeWithHash[] => {
    if (!options?.maxDepth) {
      return nodes;
    }
    if (currentDepth >= options.maxDepth - 1) {
      return nodes.map((node) => ({ ...node, children: [] }));
    }

    return nodes.map((node) => ({
      ...node,
      children: limitDepth(node.children || [], currentDepth + 1),
    }));
  };

  // Optionsがある場合は、各操作の結果を深さ制限する
  const returnTreeData = options?.maxDepth ? limitDepth(treeData) : treeData;

  return {
    treeData: returnTreeData,
    updateNode,
    deleteNode,
    setTreeData,
    moveUpward,
    moveDownward,
    addNode,
    dndNode,
    setValue,
  };
}

export { useTreeData };
