import { yupResolver } from '@hookform/resolvers/yup';
import { Stack, Button, CircularProgress } from '@mui/material';
import { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { useUpdateNode } from '@/api';
import { EnergyTypeSelect } from '@/components/EnergyType/EnergyTypeSelect.js';
import { TreeNodeWithHash } from '@/types/Types';

const schema = yup.object().shape({
  energyTypeId: yup.number().required(),
});

function NodeEnergyTypeField({
  node,
  onSubmit,
}: {
  node: TreeNodeWithHash | null;
  onSubmit?: (node: TreeNodeWithHash) => void;
}) {
  const { trigger: updateNode, isLoading: isUpdatingNode } = useUpdateNode();

  const { control, handleSubmit, reset, formState } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      energyTypeId: node?.energyType.id.toString() || '',
    },
  });

  useEffect(() => {
    reset({ energyTypeId: node?.energyType.id.toString() || '' });
  }, [node, reset]);

  return (
    <Stack direction={'row'}>
      <Controller
        name='energyTypeId'
        control={control}
        render={({ field }) => (
          <EnergyTypeSelect {...field} sx={{ minWidth: '192px' }} size='small' isColor={true} />
        )}
      />
      {formState.isDirty && (
        <Stack direction={'row'} alignItems={'flex-start'} spacing={1} ml={2}>
          {isUpdatingNode ? (
            <CircularProgress />
          ) : (
            <>
              <Button
                onClick={() => {
                  reset({ energyTypeId: node ? node.energyType.id.toString() : '' });
                }}
                variant='outlined'
                color='error'
              >
                キャンセル
              </Button>
              <Button
                type='submit'
                onClick={() => {
                  handleSubmit(async (data) => {
                    if (node) {
                      try {
                        const response = await updateNode({
                          urlParameter: {
                            nodeId: node.id,
                          },
                          body: {
                            ...node,
                            energyTypeId: Number(data.energyTypeId),
                          },
                        });
                        if (response) {
                          reset({ energyTypeId: response.node.energyType.id.toString() });
                          onSubmit && onSubmit({ ...node, energyType: response.node.energyType });
                        }
                      } catch (error) {
                        reset();
                        console.error(error);
                      }
                    }
                  })();
                }}
                variant='contained'
                color='primary'
                disableElevation
              >
                保存
              </Button>
            </>
          )}
        </Stack>
      )}
    </Stack>
  );
}

export { NodeEnergyTypeField };
