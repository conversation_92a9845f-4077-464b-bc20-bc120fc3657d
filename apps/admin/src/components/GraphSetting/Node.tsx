import { ArrowRight, ArrowDropDown } from '@mui/icons-material';
import { ListItemButton, Typography, IconButton, Box, Stack } from '@mui/material';
import { EnergyTypeColorIcons } from 'lib/Component/Icons/EnergyTypeIcons';
import { NodeRendererProps } from 'react-arborist';
import { TreeNodeWithHash } from '../../types';

function TreeNodeComponent({ node, style, dragHandle }: NodeRendererProps<TreeNodeWithHash>) {
  return (
    <ListItemButton
      selected={node.isSelected}
      style={{ ...style }}
      ref={dragHandle}
      dense
      onClick={() => {
        node.isInternal && node.toggle();
      }}
    >
      <Typography pl={2} component={'span'} variant='body1' display={'flex'} whiteSpace={'nowrap'}>
        <Stack direction={'row'} gap={0.5}>
          <IconButton sx={{ padding: 0, position: 'relative' }} disableRipple>
            <Box position={'absolute'} left={-16} top={0}>
              {node.data.children.length !== 0 &&
                (node.isOpen ? <ArrowDropDown /> : <ArrowRight />)}
            </Box>
          </IconButton>

          <Box display={'flex'} alignItems={'center'}>
            <EnergyTypeColorIcons energyTypeId={node?.data.energyType.id} />
          </Box>
          {node.data.name}
        </Stack>
      </Typography>
    </ListItemButton>
  );
}

export { TreeNodeComponent };
