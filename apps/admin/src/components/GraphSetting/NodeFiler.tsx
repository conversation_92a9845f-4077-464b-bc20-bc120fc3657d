import { Box, Divider, Paper, Stack, StackProps, Typography } from '@mui/material';
import { PropsWithChildren } from 'react';

function NodeFiler({
  children,
  variant = 'elevation',
}: PropsWithChildren<{
  variant?: 'elevation' | 'outlined';
}>) {
  return (
    <Paper id='contentBox' sx={{ flex: 1, marginBottom: 2, height: '100%' }} variant={variant}>
      <Stack display={'flex'} direction={'row'} height={'100%'}>
        {children}
      </Stack>
    </Paper>
  );
}

function LeftColumn({ children }: PropsWithChildren) {
  return (
    <Box minWidth={'30%'} overflow={'auto'} height={'100%'}>
      {children}
    </Box>
  );
}

function ColumTitle({ children }: PropsWithChildren) {
  return (
    <Box>
      <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
        {children}
      </Stack>
      <Divider id='titleDivider' />
    </Box>
  );
}

function Label({ children }: PropsWithChildren) {
  return (
    <Typography component={'h3'} variant='body1' p={1}>
      {children}
    </Typography>
  );
}

function Buttons({ children, ...props }: PropsWithChildren<StackProps>) {
  return (
    <Stack direction={'row'} alignItems={'center'} {...props}>
      {children}
    </Stack>
  );
}

function Body({ children }: PropsWithChildren) {
  return <Box p={1}>{children}</Box>;
}

function RightColumn({ children }: PropsWithChildren) {
  return (
    <>
      <Divider orientation={'vertical'} flexItem />
      <Stack flex={1} height={'100%'}>
        {children}
      </Stack>
    </>
  );
}

NodeFiler.Left = LeftColumn;
NodeFiler.Right = RightColumn;
NodeFiler.ColumnTitle = ColumTitle;
NodeFiler.TitleLabel = Label;
NodeFiler.TitleButtons = Buttons;
NodeFiler.ContentBody = Body;

export { NodeFiler };
