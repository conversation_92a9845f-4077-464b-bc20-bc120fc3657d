import { DeleteOutline } from '@mui/icons-material';
import {
  Box,
  Typography,
  Paper,
  Stack,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  OutlinedInput,
  IconButton,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { useEffectOnce } from 'react-use';
import { useReplaceNodeDatapoints } from '../../api/useReplaceNodeDatapoints';
import { MultiColumnView } from '../ColumnList/context/MultiColumViewContext';
import { useMultiColumnViewContext } from '../ColumnList/context/MultiColumViewContext';
import { DatapointColumn } from '../Datapoint/DatapointColumn';
import { EcoisDialog } from '../Dialog';
import { LocationColumn } from '../Location/LocationColumn';
import { UnitColumn } from '../Unit/UnitColumn';
import { useListProjectLocations } from '@/api';
import { useDialogState } from '@/hooks';
import { NodeDatapoint, TreeNodeWithHash, Datapoint, Project, Location } from '@/types';

interface FormValues {
  datapoints: (Pick<NodeDatapoint, 'coefficient'> & Pick<Datapoint, 'id'> & { name: string })[];
}

function NodeDatapointsField({
  node,
  onSubmit,
  projectId,
}: {
  node: TreeNodeWithHash | null;
  onSubmit?: (node: TreeNodeWithHash) => void;
  projectId: Project['id'];
}) {
  const { control, handleSubmit, reset, formState } = useForm<FormValues>({
    defaultValues: {
      datapoints: node ? node.datapoints : [],
    },
  });

  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: 'datapoints',
  });

  useEffect(() => {
    reset({
      datapoints: node ? node.datapoints : [],
    });
  }, [node, reset]);

  function addDatapoints(datapoints: Datapoint[]) {
    if (node) {
      const newDatapoints = datapoints.map((datapoint) => {
        return {
          id: datapoint.id,
          name: datapoint.name,
          coefficient: 1,
        };
      });
      append(newDatapoints);
    }
  }

  const {
    DatapointsSelectDialog,
    props: datapointsSelectDialogProps,
    open: openDatapointsSelectDialog,
  } = useDatapointsSelectDialog();

  const handleClickOpen = () => {
    openDatapointsSelectDialog();
  };

  const { trigger, isLoading } = useReplaceNodeDatapoints();

  function handleDatapointsDialogSubmit(datapoints: Datapoint[]) {
    addDatapoints(datapoints);
  }

  return (
    <Box>
      <Typography mb={1} variant={'body1'}>
        データポイント
      </Typography>
      <Typography variant={'body2'} color={'text.secondary'} mb={2}>
        選択しているデータポイントの合計値がグラフに表示されます。
      </Typography>
      <Paper variant={'outlined'}>
        <Box p={2}>
          <Stack spacing={2}>
            {node && fields.length > 0 ? (
              fields.map((datapoint, index) => {
                return (
                  <Card key={datapoint.id} variant={'outlined'}>
                    <CardContent>
                      <Stack direction={'row'} justifyContent={'space-between'} alignItems={'end'}>
                        <Box>
                          <Typography variant={'caption'}>データポイント</Typography>
                          <Typography>{datapoint.name}</Typography>
                        </Box>
                        <Stack direction={'row'} alignItems={'center'}>
                          <Box>
                            <FormControl>
                              <InputLabel htmlFor={`coefficient-${datapoint.id}`}>係数</InputLabel>
                              <Controller
                                control={control}
                                name={`datapoints.${index}.coefficient`}
                                render={({ field }) => (
                                  <OutlinedInput
                                    type='number'
                                    id={`coefficient-${datapoint.id}`}
                                    label='係数'
                                    value={field.value}
                                    defaultChecked
                                    size='small'
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                    }}
                                  />
                                )}
                              />
                            </FormControl>
                          </Box>
                          <IconButton onClick={() => remove(index)}>
                            <DeleteOutline />
                          </IconButton>
                        </Stack>
                      </Stack>
                    </CardContent>
                  </Card>
                );
              })
            ) : (
              <Typography variant={'body2'}>データポイントが選択されていません</Typography>
            )}
          </Stack>
          <Stack pt={2} direction={'row'} justifyContent={'space-between'}>
            <Box>
              <Button variant={'contained'} disableElevation onClick={handleClickOpen}>
                データポイントを追加
              </Button>
            </Box>
            {formState.isDirty && (
              <Stack direction={'row'} spacing={1}>
                {isLoading ? (
                  <CircularProgress />
                ) : (
                  <>
                    <Button
                      variant={'outlined'}
                      color={'error'}
                      disableElevation
                      onClick={() => {
                        reset({
                          datapoints: node ? node.datapoints : [],
                        });
                      }}
                    >
                      キャンセル
                    </Button>
                    <Button
                      variant={'contained'}
                      disableElevation
                      onClick={handleSubmit(async (data) => {
                        if (node) {
                          try {
                            const response = await trigger({
                              body: {
                                datapoints: data.datapoints.map(({ id, coefficient }) => ({
                                  id,
                                  coefficient,
                                })),
                              },
                              urlParameter: { nodeId: node.id },
                            });
                            if (response) {
                              const replaceDatapoints = response.nodeDatapoints.map(
                                (nodeDatapoint) => ({
                                  id: nodeDatapoint.datapoint.id,
                                  name: nodeDatapoint.datapoint.name,
                                  isAvailable: nodeDatapoint.datapoint.isAvailable,
                                  coefficient: nodeDatapoint.coefficient,
                                }),
                              );
                              replace(replaceDatapoints);
                              reset(data);

                              onSubmit &&
                                onSubmit({
                                  ...node,
                                  datapoints: replaceDatapoints,
                                });
                            }
                          } catch (e) {
                            console.error(e);
                          }
                          reset(data);
                        }
                      })}
                    >
                      保存
                    </Button>
                  </>
                )}
              </Stack>
            )}
          </Stack>
        </Box>
      </Paper>
      <DatapointsSelectDialog
        {...datapointsSelectDialogProps}
        projectId={projectId}
        onSubmit={handleDatapointsDialogSubmit}
      />
    </Box>
  );
}

function useDatapointsSelectDialog() {
  const { openState, open, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { DatapointsSelectDialog, open, props };
}

function DatapointsSelectDialog({
  openState,
  close,
  onSubmit,
  projectId,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (datapoints: Datapoint[]) => void;
  projectId: Project['id'];
}) {
  const [datapoints, setDatapoints] = useState<Datapoint[]>([]);

  const [locationIds, setLocationIds] = useState<Location['id'][]>([]);

  const {
    trigger: fetchProjectLocations,
    data: projectLocationResponse,
    // error: projectLocationError,
  } = useListProjectLocations();

  useEffectOnce(() => {
    fetchProjectLocations({
      urlParameter: {
        projectId: projectId,
      },
    });
  });

  useEffect(() => {
    if (projectLocationResponse) {
      const locationIds = projectLocationResponse?.projectLocations.map(
        ({ locationId }) => locationId,
      );
      setLocationIds(locationIds);
    }
  }, [projectLocationResponse]);

  function updateDatapoints(datapoints: Datapoint[]) {
    setDatapoints(datapoints);
  }

  return (
    <EcoisDialog open={openState} close={close} maxWidth={'xl'}>
      <DialogTitle id='alert-dialog-title'>データポイントを追加</DialogTitle>
      <DialogContent>
        <DialogActions>
          <Button onClick={close}>キャンセル</Button>
          <Button
            variant={'contained'}
            onClick={() => {
              onSubmit?.(datapoints);
              close();
            }}
          >
            追加
          </Button>
        </DialogActions>
        <Paper
          sx={{
            height: '80vh',
            width: '80vw',
          }}
        >
          <MultiColumnView>
            <LocationColumn locationIds={locationIds} />
            <UnitColumn />
            <DatapointsSelectColumns onSelectChange={updateDatapoints} />
          </MultiColumnView>
        </Paper>
      </DialogContent>
    </EcoisDialog>
  );
}

function DatapointsSelectColumns({
  onSelectChange,
}: {
  onSelectChange: (datapoints: Datapoint[]) => void;
}) {
  const {
    datapoint: { data: datapoints },
  } = useMultiColumnViewContext();

  function handleCheckedItemsChange(datapointIds: number[]): void {
    if (datapoints) {
      const selectedDatapoints = datapoints?.filter((datapoint) =>
        datapointIds.includes(datapoint.id),
      );
      onSelectChange?.(selectedDatapoints);
    }
  }

  return (
    <>
      <DatapointColumn checkboxSelection onSelectionModelChange={handleCheckedItemsChange} />
    </>
  );
}

export { NodeDatapointsField };
