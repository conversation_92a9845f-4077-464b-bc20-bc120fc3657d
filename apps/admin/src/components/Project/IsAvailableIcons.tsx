import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import TaskAltIcon from '@mui/icons-material/TaskAlt';
import { Box } from '@mui/material';

export const AvailableIcon = () => (
  <Box
    sx={{
      position: 'relative',
      width: 24,
      height: 24,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <Box
      sx={{
        backgroundColor: '#dcfbe7',
        borderRadius: '50%',
        width: 24,
        height: 24,
        position: 'absolute',
        opacity: 0.5,
      }}
    />
    <TaskAltIcon color={'success'} sx={{ fontSize: '14px', zIndex: 1 }} />
  </Box>
);

export const NotAvailableIcon = () => (
  <Box
    sx={{
      width: 24,
      height: 24,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <CancelOutlinedIcon color='error' sx={{ fontSize: '16px' }} />
  </Box>
);
