import {
  Card,
  LinearProgress,
  Box,
  Typography,
  Button,
  Divider,
  Card<PERSON>ontent,
  Grid,
  Stack,
} from '@mui/material';
import { AvailableIcon, NotAvailableIcon } from './IsAvailableIcons';
import { Value } from '@/components/Display';
import { Project } from '@/types';

function ProjectAvailabilityPanel({
  project,
  isLoading,
  onAvailabilityChangeClick,
}: {
  project?: Project;
  isLoading: boolean;
  onAvailabilityChangeClick?: () => void;
  onEditButtonClick?: () => void;
}) {
  return (
    <>
      <Card>
        {isLoading && <LinearProgress />}
        <Box p={1} px={2} display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
          <Box>
            <Typography variant={'subtitle1'} component={'h3'} mt={0}>
              案件運用状態
            </Typography>
          </Box>
          <Button
            color={project?.isAvailable ? 'error' : 'primary'}
            variant={'contained'}
            disableElevation
            onClick={() => {
              onAvailabilityChangeClick?.();
            }}
          >
            {project?.isAvailable ? '無効にする' : '有効にする'}
          </Button>
        </Box>
        <Divider />
        <CardContent>
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Value>
                  <Stack direction={'row'} alignItems={'center'}>
                    {project?.isAvailable ? <AvailableIcon /> : <NotAvailableIcon />}
                    <Typography>{project?.isAvailable ? '有効' : '無効'}</Typography>
                  </Stack>
                </Value>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </>
  );
}

export { ProjectAvailabilityPanel };
