import {
  Card,
  LinearProgress,
  Box,
  Typo<PERSON>,
  Button,
  Divider,
  CardContent,
  Grid,
  Link,
} from '@mui/material';
import { AddText, Label, Value } from '@/components/Display';
import { Project } from '@/types';

function ProjectOverviewPanel({
  project,
  isLoading,
  onEditButtonClick,
  onAddTextButtonClick,
}: {
  project?: Project;
  isLoading: boolean;
  onAddTextButtonClick?: () => void;
  onEditButtonClick?: () => void;
}) {
  return (
    <>
      <Card>
        {isLoading && <LinearProgress />}
        <Box p={1} px={2} display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
          <Box>
            <Typography variant={'subtitle1'} component={'h3'} mt={0}>
              案件概要
            </Typography>
          </Box>
          <Button disableElevation variant={'contained'} onClick={onEditButtonClick}>
            編集
          </Button>
        </Box>
        <Divider />
        <CardContent>
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Label>案件ID</Label>
                <Value>{project?.id}</Value>
              </Grid>
              <Grid item xs={4}>
                <Label>案件名</Label>
                <Value>{project?.name}</Value>
              </Grid>
              <Grid item xs={4}>
                <Label>案件所在地</Label>
                <Value>
                  {project?.postcode ? (
                    '〒' + project?.postcode
                  ) : (
                    <AddText onClick={onAddTextButtonClick} label='郵便番号' />
                  )}
                  <br />
                  {project?.address ? (
                    project?.address
                  ) : (
                    <AddText onClick={onAddTextButtonClick} label='住所' />
                  )}
                </Value>
              </Grid>
              <Grid item xs={4}>
                <Label>緯度経度</Label>
                <Value
                  sx={(theme) => ({
                    color: theme.palette.text.secondary,
                  })}
                >
                  {project?.latitude && project?.longitude
                    ? `${project.latitude},${project.longitude}`
                    : '所在地より自動的に入力されます'}
                </Value>
              </Grid>
              <Grid item xs={4}>
                <Label>Backlog課題</Label>
                <Value>
                  {project?.backlogIssueKey ? (
                    <Link
                      href={`https://picoada.backlog.jp/view/${project.backlogIssueKey}`}
                      target='_blank'
                      rel='noreferrer noopener'
                    >
                      {project.backlogIssueKey}
                    </Link>
                  ) : (
                    <AddText onClick={onAddTextButtonClick} label='Backlog課題' />
                  )}
                </Value>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </>
  );
}

export { ProjectOverviewPanel };
