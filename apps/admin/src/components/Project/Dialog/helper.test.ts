import { addHyphen } from './helper';

describe('helper', () => {
  it('Return correct value', () => {
    const pattern = [
      {
        postcode: '1234567',
        expected: '123-4567',
      },
      {
        postcode: '123-4567',
        expected: '123-4567',
      },
      {
        postcode: 'abcefgd',
        expected: 'abc-efgd',
      },
    ];

    pattern.forEach((p) => {
      expect(addHyphen(p.postcode)).toBe(p.expected);
    });
  });
});
