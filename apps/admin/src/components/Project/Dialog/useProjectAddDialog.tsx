import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  TextField,
  DialogActions,
  Button,
  Stack,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { addHyphen } from './helper';
import { projectSchema } from './projectSchema';
import { useCreateProject } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Project } from '@/types';

function useProjectAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ProjectAddDialog, props, open };
}

const addProjectSchema = projectSchema.pick([
  'name',
  'postcode',
  'address',
  'isKenesIntegrationAvailable',
]);

function ProjectAddDialog({
  openState,
  close,
  onCancel,
  onSubmit,
}: {
  openState: boolean;
  close: () => void;
  onCancel?: () => void;
  onSubmit?: (project: Project) => void;
}) {
  const { trigger: createProject, isLoading: isCreating, reset: swrReset } = useCreateProject();

  const {
    control,
    formState: { isValid, errors },
    handleSubmit,
    reset: formReset,
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      name: '',
      postcode: '',
      address: '',
      isKenesIntegrationAvailable: true, // KENES案件はデフォルトでtrue
    },
    resolver: yupResolver(addProjectSchema),
  });

  function resetCache() {
    formReset();
    swrReset();
  }

  function handleCancel() {
    resetCache();
    onCancel?.();
    close();
  }

  const handleOnFormSubmit = handleSubmit(async (data) => {
    const postcode = addHyphen(data.postcode);

    const response = await createProject({
      body: {
        name: data.name,
        postcode,
        address: data.address,
        isKenesIntegrationAvailable: data.isKenesIntegrationAvailable,
      },
    });
    if (response) {
      const newProject = response.project;
      onSubmit?.(newProject);
      resetCache();
      close();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>案件作成</DialogTitle>
      <DialogContent>
        <DialogContentText>案件名を入力してください</DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='案件名(必須)'
                error={!!errors.name}
                helperText={errors.name?.message}
              />
            )}
          />
          <Controller
            name='postcode'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='郵便番号(必須)'
                variant='outlined'
                fullWidth
                error={!!errors.postcode}
                helperText={errors.postcode?.message}
              />
            )}
          />
          <Controller
            name='address'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='住所(必須)'
                variant='outlined'
                fullWidth
                error={!!errors.address}
                helperText={errors.address?.message}
              />
            )}
          />
          <Controller
            name='isKenesIntegrationAvailable'
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={<Checkbox {...field} checked={field.value} />}
                label='この案件をKENES案件とする'
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          loading={isCreating}
          variant='contained'
          disabled={!isValid}
          onClick={handleOnFormSubmit}
        >
          作成
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useProjectAddDialog };
