import * as yup from 'yup';

const projectSchema = yup.object({
  name: yup.string().required('案件名を入力してください'),
  postcode: yup
    .string()
    .required('郵便番号を入力してください')
    .matches(
      /^(\d{7}|\d{3}-\d{4})$/,
      '数字だけの場合は7桁、ハイフンありの場合は数字3桁-数字4桁のフォーマットになっている必要があります',
    ),
  address: yup.string().required('住所を入力してください'),
  backlogIssueKey: yup.string(),
  isKenesIntegrationAvailable: yup.boolean(),
});

export { projectSchema };
