import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Stack,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useUpdateProjectKenesIntegrationAvailable } from '@/api/useUpdateProjectKenesIntegrationAvailable';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
// TODO モック用にOriginalを加工中
import { Project } from '@/types';

function useProjectKenesEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ProjectKenesEditDialog, props, open };
}

const projectKenesEditSchema = yup.object({
  isKenesIntegrationAvailable: yup.boolean(),
});

function ProjectKenesEditDialog({
  openState,
  close,
  onCancel,
  onSubmit,
  project,
}: {
  openState: boolean;
  close: () => void;
  onCancel?: () => void;
  onSubmit?: (project: Project) => void;
  project: Project;
}) {
  // TODO APIとモックを置き換える
  const {
    trigger: updateProjectKenesIntegrationAvailable,
    isLoading: isCreating,
    reset: swrReset,
  } = useUpdateProjectKenesIntegrationAvailable();

  const {
    control,
    handleSubmit,
    reset: formReset,
    watch,
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      isKenesIntegrationAvailable: false,
    },
    resolver: yupResolver(projectKenesEditSchema),
  });

  const formState = watch();

  function resetCache() {
    formReset();
    swrReset();
  }

  function handleCancel() {
    resetCache();
    onCancel?.();
    close();
  }

  const isKenesIntegrationAvailable = project?.isKenesIntegrationAvailable;
  const textItems: {
    mode: string;
    title: string;
    checkboxLabel: string;
    color: 'error' | 'primary';
    buttonLabel: string;
  } = {
    mode: isKenesIntegrationAvailable ? '解除' : '開始',
    title: isKenesIntegrationAvailable ? 'KENES連携を解除しますか?' : 'KENES連携を開始しますか?',
    checkboxLabel: isKenesIntegrationAvailable ? '連携を解除する' : '連携を開始する',
    color: isKenesIntegrationAvailable ? 'error' : 'primary',
    buttonLabel: isKenesIntegrationAvailable ? '解除' : '開始',
  };

  const handleOnFormSubmit = handleSubmit(async () => {
    const response = await updateProjectKenesIntegrationAvailable({
      urlParameter: {
        projectId: project.id,
      },
      body: {
        isKenesIntegrationAvailable: !isKenesIntegrationAvailable,
      },
    });
    if (response) {
      // APIからのリクエストを正しく反映する必要あり
      onSubmit?.(response.project);
      resetCache();
      close();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>{textItems.title}</DialogTitle>
      <DialogContent>
        <DialogContentText>
          {textItems.mode}する場合は､ チェックを入れて進んでください｡
        </DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            name='isKenesIntegrationAvailable'
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={<Checkbox {...field} checked={field.value} color={textItems.color} />}
                label={textItems.checkboxLabel}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button color={'inherit'} onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          color={textItems.color}
          loading={isCreating}
          variant='contained'
          disabled={!formState.isKenesIntegrationAvailable}
          onClick={handleOnFormSubmit}
        >
          {textItems.buttonLabel}
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useProjectKenesEditDialog };
