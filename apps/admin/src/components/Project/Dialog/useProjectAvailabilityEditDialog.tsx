import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Stack,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useUpdateProjectAvailable } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Project } from '@/types';

function useProjectAvailabilityEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ProjectAvailabilityEditDialog, props, open };
}

const projectAvailabilityEditSchema = yup.object({
  isAvailable: yup.boolean(),
});

function ProjectAvailabilityEditDialog({
  openState,
  close,
  onCancel,
  onSubmit,
  project,
}: {
  openState: boolean;
  close: () => void;
  onCancel?: () => void;
  onSubmit?: (project: Project) => void;
  project: Project;
}) {
  // TODO APIとモックを置き換える
  const {
    trigger: updateProjectAvailable,
    isLoading: isCreating,
    reset: swrReset,
  } = useUpdateProjectAvailable();

  const {
    control,
    handleSubmit,
    reset: formReset,
    watch,
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      isAvailable: false,
    },
    resolver: yupResolver(projectAvailabilityEditSchema),
  });

  const formState = watch();

  function resetCache() {
    formReset();
    swrReset();
  }

  function handleCancel() {
    resetCache();
    onCancel?.();
    close();
  }

  const isAvailable = project?.isAvailable;
  const textItems: {
    mode: string;
    title: string;
    checkboxLabel: string;
    color: 'error' | 'primary';
    buttonLabel: string;
  } = {
    mode: isAvailable ? '無効' : '有効',
    title: isAvailable ? '案件を無効にしますか?' : '案件を有効にしますか?',
    checkboxLabel: isAvailable ? '無効にする' : '有効にする',
    color: isAvailable ? 'error' : 'primary',
    buttonLabel: isAvailable ? '無効にする' : '有効にする',
  };

  const handleOnFormSubmit = handleSubmit(async () => {
    const response = await updateProjectAvailable({
      urlParameter: {
        projectId: project.id,
      },
      body: {
        isAvailable: !isAvailable,
      },
    });
    if (response) {
      // APIからのリクエストを正しく反映する必要あり
      onSubmit?.(response.project);
      resetCache();
      close();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>{textItems.title}</DialogTitle>
      <DialogContent>
        <DialogContentText>
          {textItems.mode}する場合は､ チェックを入れて進んでください｡
        </DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            name='isAvailable'
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={<Checkbox {...field} checked={field.value} color={textItems.color} />}
                label={textItems.checkboxLabel}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button color={'inherit'} onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          color={textItems.color}
          loading={isCreating}
          variant='contained'
          disabled={!formState.isAvailable}
          onClick={handleOnFormSubmit}
        >
          {textItems.buttonLabel}
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useProjectAvailabilityEditDialog };
