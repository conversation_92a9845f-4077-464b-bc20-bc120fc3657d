import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  Stack,
  DialogActions,
  Button,
  TextField,
  DialogContentText,
} from '@mui/material';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { addHyphen } from './helper';
import { projectSchema } from './projectSchema';
import { useUpdateProject } from '@/api';
import { UpdateProjectRequestBody } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { Project } from '@/types';

type ProjectEditForm = Pick<Project, 'name' | 'postcode' | 'address' | 'backlogIssueKey'>;

function defaultValues(project: Project): ProjectEditForm {
  return {
    name: project?.name || '',
    postcode: project?.postcode || '',
    address: project?.address || '',
    backlogIssueKey: project?.backlogIssueKey || '',
  };
}

function ProjectEditDialog({
  openState,
  close,
  onSubmit,
  project,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (project: Project) => void;
  project: Project;
}) {
  //updateProject mutation
  const { trigger: updateProject, isLoading, reset: swrReset } = useUpdateProject();

  // build form
  const {
    control,
    formState: { errors, isValid, isDirty },
    handleSubmit,
    reset: formReset,
  } = useForm({
    mode: 'onChange',
    defaultValues: defaultValues(project),
    resolver: yupResolver(projectSchema),
  });

  // 編集後に初期値をリストする処理
  useEffect(() => {
    formReset(defaultValues(project));
  }, [project, formReset]);

  async function projectUpdateRequest(project: Project, body: UpdateProjectRequestBody) {
    return await updateProject({
      urlParameter: {
        projectId: project.id,
      },
      body,
    });
  }

  function resetCache() {
    swrReset();
    formReset();
  }

  function handleCancel() {
    close();
    resetCache();
  }

  const handleFormSubmit = handleSubmit(async (data: ProjectEditForm) => {
    const requestBody = {
      ...data,
      postcode: addHyphen(data.postcode),
    };

    const result = await projectUpdateRequest(project, requestBody);
    if (result?.project) {
      onSubmit?.(result.project);
      resetCache();
      close();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'sm'}>
      <DialogTitle id='alert-dialog-title'>案件を編集</DialogTitle>
      <DialogContent>
        <DialogContentText>編集したい項目の値を変更してください</DialogContentText>
        <Stack component={'form'} spacing={2} py={2}>
          <Controller
            name='name'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='案件名'
                variant='outlined'
                fullWidth
                error={!!errors.name}
                helperText={errors.name?.message}
              />
            )}
          />
          <Controller
            name='postcode'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='郵便番号(必須)'
                variant='outlined'
                fullWidth
                error={!!errors.postcode}
                helperText={errors.postcode?.message}
              />
            )}
          />
          <Controller
            name='address'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='住所(必須)'
                variant='outlined'
                fullWidth
                error={!!errors.address}
                helperText={errors.address?.message}
              />
            )}
          />
          <Controller
            name='backlogIssueKey'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='Backlog課題'
                variant='outlined'
                fullWidth
                error={!!errors.backlogIssueKey}
                helperText={errors.backlogIssueKey?.message}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleFormSubmit}
        >
          編集
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { ProjectEditDialog };
