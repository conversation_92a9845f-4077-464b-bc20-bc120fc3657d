import { Grid, Divider } from '@mui/material';
import { SubMenu, SubMenuItem } from '@/components/SubMenu';

function ProjectMenu() {
  return (
    <Grid container>
      <Grid item>
        <SubMenu>
          <SubMenuItem to='detail' primary='基本情報' />
          <SubMenuItem to='location' primary='ロケーション' />
          <SubMenuItem to='graph' primary='グラフ設定' />
          <SubMenuItem to='utility' primary='ユーティリティ' />
          <SubMenuItem to='summary' primary='サマリー設定' />
          {/* <SubMenuItem to='setting' primary='設定' /> */}
        </SubMenu>
      </Grid>
      <Divider orientation='vertical' flexItem />
    </Grid>
  );
}

export { ProjectMenu };
