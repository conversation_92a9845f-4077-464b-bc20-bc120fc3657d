import {
  Card,
  LinearProgress,
  Box,
  Typography,
  Button,
  Divider,
  Card<PERSON>ontent,
  Grid,
  Stack,
} from '@mui/material';
import { StatusMarkDot } from '@/components/Datapoint/StatusMark';
import { Value } from '@/components/Display';
import { Project } from '@/types';

function ProjectKenesPanel({
  project,
  isLoading,
  onKenesEditButtonClick,
}: {
  project?: Project;
  isLoading: boolean;
  onKenesEditButtonClick?: () => void;
}) {
  return (
    <>
      <Card>
        {isLoading && <LinearProgress />}
        <Box p={1} px={2} display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
          <Box>
            <Typography variant={'subtitle1'} component={'h3'} mt={0}>
              KENES連携
            </Typography>
          </Box>
          <Button
            color={project?.isKenesIntegrationAvailable ? 'error' : 'primary'}
            variant={'contained'}
            disableElevation
            onClick={() => {
              onKenesEditButtonClick?.();
            }}
          >
            {project?.isKenesIntegrationAvailable ? '連携を解除' : '連携を開始'}
          </Button>
        </Box>
        <Divider />
        <CardContent>
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Value>
                  <Stack
                    direction={'row'}
                    gap={1}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                  >
                    <Stack direction={'row'} alignItems={'center'} spacing={1}>
                      <StatusMarkDot isAvailable={project?.isKenesIntegrationAvailable || false} />
                      <Typography>
                        {project?.isKenesIntegrationAvailable ? '連携中' : '未連携'}
                      </Typography>
                    </Stack>
                  </Stack>
                </Value>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </>
  );
}

export { ProjectKenesPanel };
