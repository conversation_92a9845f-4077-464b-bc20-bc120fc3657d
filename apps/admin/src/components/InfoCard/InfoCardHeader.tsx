import { Divider, Stack, StackProps, Typography } from '@mui/material';
import { ReactNode } from 'react';

function InfoCardHeader({
  children,
  actions,
  ...props
}: {
  actions?: ReactNode;
} & StackProps) {
  const flexProps: StackProps = {
    direction: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  return (
    <>
      <Stack py={1} px={2} {...(actions ? flexProps : {})} {...props}>
        <Typography variant='body1'>{children}</Typography>
        {actions && (
          <Stack {...(actions ? flexProps : {})} spacing={2}>
            {actions}
          </Stack>
        )}
      </Stack>
      <Divider />
    </>
  );
}

export { InfoCardHeader };
