import { FormControl, TextField } from '@mui/material';
import { Controller } from 'react-hook-form';
import { ClientUserPasswordField } from './ClientUserPasswordField';

function ClientUserForm({
  control,
  errors,
  mode = 'add',
}: {
  control: any;
  errors: any;
  mode?: 'add' | 'edit';
}) {
  return (
    <>
      <Controller
        name='username'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='clientUser-username'
            label='ユーザーネーム(必須)'
            variant='outlined'
            error={'username' in errors}
            helperText={'username' in errors ? errors.username?.message : ''}
          />
        )}
      />
      {mode === 'add' && <ClientUserPasswordField control={control} errors={errors} />}
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='clientUser-name'
            label='氏名'
            variant='outlined'
            error={'name' in errors}
            helperText={'name' in errors ? errors.name?.message : ''}
          />
        )}
      />

      <Controller
        name='email'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='clientUser-email'
            label='Email'
            variant='outlined'
            error={'email' in errors}
            helperText={'email' in errors ? errors.email?.message : ''}
          />
        )}
      />
      <Controller
        name='company'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='clientUser-company'
            label='会社'
            variant='outlined'
            error={'company' in errors}
            helperText={'company' in errors ? errors.company?.message : ''}
          />
        )}
      />
      <Controller
        name='department'
        control={control}
        render={({ field }) => (
          <FormControl fullWidth>
            <TextField
              {...field}
              id='clientUser-department'
              label='部署'
              variant='outlined'
              error={'department' in errors}
              helperText={'department' in errors ? errors.department?.message : ''}
            />
          </FormControl>
        )}
      />
      <Controller
        name='position'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='clientUser-position'
            label='役職'
            variant='outlined'
            error={'position' in errors}
            helperText={'position' in errors ? errors.position?.message : ''}
          />
        )}
      />
    </>
  );
}

export { ClientUserForm };
