import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { clientUserAddSchema, clientUserEditSchema } from './clientUserSchema';
import { defaultClientUserValues } from './defaultClientUserValues';
import { ClientUser } from '@/types';

type ClientUserAddFormValues = {
  username: string;
  password: string;
  name: string;
  email: string;
  company: string;
  department: string;
  position: string;
};

type ClientUserEditFormValues = Omit<ClientUserAddFormValues, 'password'>;

function useClientUserAddForm() {
  return useForm({
    mode: 'onChange',
    defaultValues: {
      username: '',
      password: '',
      name: '',
      email: '',
      company: '',
      department: '',
      position: '',
    },
    resolver: yupResolver(clientUserAddSchema),
  });
}

function useClientUserEditForm(clientUser: ClientUser) {
  return useForm({
    mode: 'onChange',
    defaultValues: defaultClientUserValues(clientUser),
    resolver: yup<PERSON>esolver(clientUserEditSchema),
  });
}

export { useClientUserAddForm, useClientUserEditForm };
export type { ClientUserAddFormValues, ClientUserEditFormValues };
