import { Loading<PERSON>utton } from '@mui/lab';
import { Di<PERSON>Title, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { useEffect } from 'react';
import { EcoisDialog } from '../Dialog/EcoisDialog';
import { ClientUserForm } from './ClientUserForm';
import { defaultClientUserValues } from './defaultClientUserValues';
import { useClientUserEditForm } from './useClientUserForm';
import { useUpdateClientUser } from '@/api';
import { useDialogState } from '@/hooks';
import { ClientUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useClientUserEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ClientUserEditDialog, props, open };
}

function ClientUserEditDialog({
  openState,
  close,
  onSubmit,
  clientUser,
}: {
  openState: boolean;
  close: () => void;
  clientUser: ClientUser;
  onSubmit?: (clientUser: ClientUser) => void;
}) {
  const { trigger, isLoading } = useUpdateClientUser();

  const { control, formState, handleSubmit, reset } = useClientUserEditForm(clientUser);

  function handleClose() {
    reset();
    close();
  }

  useEffect(() => {
    reset({
      ...defaultClientUserValues(clientUser),
    });
  }, [clientUser, reset]);

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>クライアントユーザーを編集</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <ClientUserForm mode='edit' control={control} errors={formState.errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!formState.isValid || !formState.isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                urlParameter: {
                  clientUserId: clientUser.id,
                },
                body: {
                  username: data.username,
                  name: data.name === '' ? null : data.name,
                  email: data.email === '' ? null : data.email,
                  company: data.company === '' ? null : data.company,
                  department: data.department === '' ? null : data.department,
                  position: data.position === '' ? null : data.position,
                },
              });
              reset();
              if (response) {
                onSubmit && onSubmit(response?.clientUser);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          変更
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useClientUserEditDialog };
