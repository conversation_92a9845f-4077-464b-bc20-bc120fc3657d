import { Box, Paper, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useDeleteClientUser } from '@/api';
import { DeleteDialog, useDeleteDialog } from '@/components/Dialog/DeleteDialog';
import { ClientUser } from '@/types';

function ClientUserDeleteDialog({
  clientUser,
  openState,
  close,
}: {
  clientUser: ClientUser;
  openState: boolean;
  close: () => void;
}) {
  const navigate = useNavigate();

  const { trigger: deleteClientUser, isLoading: deleteClientUserIsLoading } = useDeleteClientUser();

  return (
    <DeleteDialog
      {...{ openState, close }}
      title={`クライアントユーザーを削除`}
      confirmText='削除するとクライアントユーザーのデータは削除されます。注意して作業してください。'
      isLoading={deleteClientUserIsLoading}
      onSubmit={async () => {
        try {
          await deleteClientUser({
            urlParameter: { clientUserId: clientUser?.id },
          });
          navigate('/clientusers');
        } catch (e) {
          return Promise.reject(e);
        }
      }}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象のユーザー</Typography>
            <Typography variant='body1'>ユーザーネーム: {clientUser.username}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}

function useClientUserDeleteDialog() {
  const { props, open: openDeleteDialog } = useDeleteDialog();

  return {
    ClientUserDeleteDialog,
    props,
    openDeleteDialog,
  };
}

export { useClientUserDeleteDialog };
