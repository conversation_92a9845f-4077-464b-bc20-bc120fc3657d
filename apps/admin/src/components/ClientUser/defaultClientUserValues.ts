import { ClientUser } from '@/types';

function defaultClientUserValues(clientUser?: Partial<ClientUser> | undefined) {
  return {
    username: clientUser?.username ?? '',
    name: clientUser?.name ?? '',
    email: clientUser?.email ?? '',
    company: clientUser?.company ?? '',
    department: clientUser?.department ?? '',
    position: clientUser?.position ?? '',
  };
}
export { defaultClientUserValues };
