import { Box, CircularProgress, Alert, Chip, Stack } from '@mui/material';
import { AddText, Label, Value } from '../Display';
import { ClientUser } from '@/types';

function ClientUserDetailInfo({
  clientUser,
  isLoading,
  error,
  onAddTextButtonClick,
}: {
  clientUser: ClientUser | undefined;
  isLoading: boolean;
  error: Error;
  onAddTextButtonClick?: () => void;
}) {
  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity='error'>クライアントユーザーの読み込みに失敗しました</Alert>;
  }

  function handleAddButtonClick() {
    onAddTextButtonClick?.();
  }

  return (
    <>
      {clientUser && (
        <Stack spacing={2}>
          <Box>
            <Label>有効状態</Label>
            <Value>
              {clientUser.isAvailable ? (
                <Chip component={'span'} color={'primary'} label='有効' />
              ) : (
                <Chip component={'span'} color={'error'} label='無効' />
              )}
            </Value>
          </Box>

          <Box>
            <Label>ユーザーネーム</Label>
            <Value>{clientUser.username}</Value>
          </Box>

          <Box>
            <Label>Email</Label>
            <Value>
              {clientUser.email ?? <AddText onClick={handleAddButtonClick} label='Email' />}
            </Value>
          </Box>

          <Box>
            <Label>氏名</Label>
            <Value>
              {clientUser.name ?? <AddText onClick={handleAddButtonClick} label='氏名' />}
            </Value>
          </Box>

          <Box>
            <Label>会社</Label>
            <Value>
              {clientUser.company ?? <AddText onClick={handleAddButtonClick} label='会社' />}
            </Value>
          </Box>

          <Box>
            <Label>部署</Label>
            <Value>
              {clientUser.department ?? <AddText onClick={handleAddButtonClick} label='部署' />}
            </Value>
          </Box>

          <Box>
            <Label>役職</Label>
            <Value>
              {clientUser.position ?? <AddText onClick={handleAddButtonClick} label='役職' />}
            </Value>
          </Box>
        </Stack>
      )}
    </>
  );
}

export { ClientUserDetailInfo };
