import { Cancel, CheckCircle } from '@mui/icons-material';
import { Box, Stack, Typography } from '@mui/material';
import { PASSWORD_SYMBOLS_REGEXP } from './CONSTANTS';

const hasLowercaseAlphanumeric = (text: string) => /[a-z]/.test(text);
const hasCapitalAlphanumeric = (text: string) => /[A-Z]/.test(text);
const hasNumeric = (text: string) => /\d/.test(text);
const hasSymbols = (text: string) => PASSWORD_SYMBOLS_REGEXP.test(text);
const moreThan = (text: string, length: number) => text.length >= length;
const moreThan10 = (text: string) => moreThan(text, 10);

function PasswordChecker({ text }: { text: string }) {
  return (
    <Stack spacing={1}>
      <CheckerLabel condition={hasLowercaseAlphanumeric(text)} labelText='半角小文字英字を含む' />
      <CheckerLabel condition={hasCapitalAlphanumeric(text)} labelText='半角大文字英字を含む' />
      <CheckerLabel condition={hasNumeric(text)} labelText='半角数字を含む' />
      <CheckerLabel condition={hasSymbols(text)} labelText='記号を含む' />
      <CheckerLabel condition={moreThan10(text)} labelText='10文字以上' />
    </Stack>
  );
}

function CheckerLabel({ condition, labelText }: { condition: boolean; labelText: string }) {
  return (
    <>
      <Typography
        variant='body2'
        m={0}
        component={Stack}
        direction={'row'}
        alignItems={'center'}
        spacing={0.5}
      >
        {condition ? <CheckCircle color='success' /> : <Cancel color='error' />}
        <Box>{labelText}</Box>
      </Typography>
    </>
  );
}

export { PasswordChecker };
