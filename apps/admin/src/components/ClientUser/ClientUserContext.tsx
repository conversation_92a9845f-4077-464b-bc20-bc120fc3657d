import { PropsWithChildren, createContext, useContext, useMemo } from 'react';
import { RedirectToNotFound } from '../Routing';
import { ClientUser } from '@/types';

const ClientUserContext = createContext<{
  clientUser?: ClientUser;
  isLoading: boolean;
  error?: any;
  fetch: (clientUserId: number) => void;
} | null>(null);

export { ClientUserContext };

function ClientUserProvider({
  children,
  clientUser,
  isLoading,
  error,
  fetchFn,
}: PropsWithChildren<{
  clientUser: ClientUser | undefined;
  isLoading: boolean;
  error?: any;
  fetchFn: (clientUserId: number) => void;
}>) {
  const value = useMemo(() => {
    return {
      clientUser,
      isLoading,
      error,
      fetch: fetchFn,
    };
  }, [clientUser, isLoading, error, fetchFn]);

  if (error) {
    return <RedirectToNotFound error={error} />;
  }

  return <ClientUserContext.Provider value={value}>{children}</ClientUserContext.Provider>;
}

function useClientUser() {
  const context = useContext(ClientUserContext);

  if (!context) {
    throw new Error('useClientUser must be used within a ClientUserProvider');
  }
  return context;
}

export { ClientUserProvider, useClientUser };
