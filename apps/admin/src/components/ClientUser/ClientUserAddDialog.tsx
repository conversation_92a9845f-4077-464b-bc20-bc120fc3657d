import { <PERSON><PERSON><PERSON>utton } from '@mui/lab';
import { <PERSON><PERSON>T<PERSON>le, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { PropsWithChildren, useState } from 'react';
import { CopyInfoContent, EcoisDialog } from '../Dialog';
import { Label, Value } from '../Display';
import { ClientUserForm } from './ClientUserForm';
import { ClientUserAddFormValues, useClientUserAddForm } from './useClientUserForm';
import { useCreateClientUser } from '@/api';
import { useDialogState } from '@/hooks';
import { ClientUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useClientUserAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ClientUserAddDialog, props, open };
}

type pageState = 'add' | 'info';

function ClientUserAddDialog({
  openState,
  close,
  onSuccess,
  onClose,
  onCancel,
}: PropsWithChildren<{
  openState: boolean;
  close: () => void;
  onSuccess?: (clientUser: ClientUser) => void;
  onClose?: (clientUser: ClientUser) => void;
  onCancel?: () => void;
}>) {
  const [pageState, setPageState] = useState<pageState>('add');
  function changePageState(state: pageState) {
    setPageState(state);
  }

  const [createdUserInfo, setCreatedUserInfo] = useState<ClientUser | undefined>({} as ClientUser);
  function updateUserInfo(clientUser: ClientUser) {
    setCreatedUserInfo(clientUser);
  }

  const [formValues, setFormValues] = useState<ClientUserAddFormValues | undefined>(
    {} as ClientUserAddFormValues,
  );
  function updateFormValues(clientUser: ClientUserAddFormValues) {
    setFormValues(clientUser);
  }

  function handleOnClose() {
    close();

    // 遷移先のページに移動する前に、stateを初期化する
    // これをしないと、次回ダイアログを開いた時に、前回のstateが残っている
    // 閉じるを押した瞬間に初期化すると、ダイアログが閉じる前にstateが初期化されてしまう
    setTimeout(() => {
      setPageState('add');
    }, 500);

    if (createdUserInfo) onClose && onClose(createdUserInfo);
  }

  function handleOnSubmit(clientUserAddFormValues: ClientUserAddFormValues) {
    changePageState('info');
    updateFormValues(clientUserAddFormValues);
  }

  function handleOnSuccess(clientUser: ClientUser) {
    updateUserInfo(clientUser);
    onSuccess && onSuccess(clientUser);
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      {pageState === 'add' && (
        <ClientUserAddFrom
          close={close}
          onSubmit={handleOnSubmit}
          onSuccess={handleOnSuccess}
          onCancel={onCancel}
        />
      )}
      {pageState === 'info' && (
        <ClientUserInfoCopy close={close} formValues={formValues} onClose={handleOnClose} />
      )}
    </EcoisDialog>
  );
}

function ClientUserInfoCopy({
  formValues,
  close,
  onClose,
}: {
  formValues: ClientUserAddFormValues | undefined;
  close: () => void;
  onClose?: () => void;
}) {
  function handleCloseClick() {
    close();
    onClose && onClose();
  }

  const supportText = formValues?.email
    ? 'ユーザー名またはEmailとパスワードの組み合わせでログインできます。'
    : 'ユーザー名とパスワードの組み合わせでログインできます。';

  const usernameText = `ユーザー名\n${formValues?.username}`;
  const emailText = `Email\n${formValues?.email}`;
  const passwordText = `パスワード\n${formValues?.password}`;
  const brake = '\n\n';

  function generateCopyText(formValues: ClientUserAddFormValues) {
    return (
      supportText +
      '\n' +
      usernameText +
      brake +
      (formValues?.email && emailText + brake) +
      passwordText
    );
  }

  const clipboardText = formValues ? generateCopyText(formValues) : '';

  return (
    <CopyInfoContent
      titleText='クライアントユーザーが作成されました'
      bodyText={
        '以下のログイン情報をコピーして、クライアントユーザーに共有してください。\n※パスワードは一度しか表示されません。'
      }
      onClose={handleCloseClick}
      clipboardText={clipboardText}
      contentElements={
        <>
          <Label>ユーザー名</Label>
          <Value>{formValues?.username}</Value>
          {formValues?.email && (
            <>
              <Label>Email</Label>
              <Value>{formValues.email}</Value>
            </>
          )}
          <Label>パスワード</Label>
          <Value>{formValues?.password}</Value>
        </>
      }
    />
  );
}

function ClientUserAddFrom({
  close,
  onSubmit,
  onCancel,
  onSuccess,
}: {
  close: () => void;
  onCancel?: () => void;
  onSubmit: (formValues: ClientUserAddFormValues) => void;
  onSuccess?: (clientUser: ClientUser) => void;
}) {
  const { trigger, isLoading, reset: swrReset } = useCreateClientUser();
  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset: formReset,
  } = useClientUserAddForm();

  function resetCache() {
    swrReset();
    formReset();
  }

  function handleClose() {
    resetCache();
    close();
    onCancel && onCancel();
  }

  async function clientUserCreateRequest(formValues: ClientUserAddFormValues) {
    return await trigger({
      body: {
        username: formValues.username,
        password: formValues.password,
        ...(formValues.name !== '' && { name: formValues.name }),
        ...(formValues.email !== '' && { email: formValues.email }),
        ...(formValues.company !== '' && { company: formValues.company }),
        ...(formValues.department !== '' && { department: formValues.department }),
        ...(formValues.position !== '' && { position: formValues.position }),
      },
    });
  }

  const handleFormSubmit = handleSubmit(async (formValues) => {
    const response = await clientUserCreateRequest(formValues);
    if (response) {
      onSubmit && onSubmit(formValues);
      onSuccess && onSuccess(response?.clientUser);
      resetCache();
    }
  });

  return (
    <>
      <DialogTitle id='alert-dialog-title'>クライアントユーザーを作成</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <ClientUserForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleFormSubmit}
        >
          作成
        </LoadingButton>
      </DialogActions>
    </>
  );
}

export { useClientUserAddDialog };
