import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  Stack,
  DialogActions,
  Button,
  Typography,
} from '@mui/material';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { CopyInfoContent, EcoisDialog } from '../Dialog';
import { Label, Value } from '../Display';
import { ClientUserPasswordField } from './ClientUserPasswordField';
import { clientUserPasswordSchema } from './clientUserSchema';
import { useUpdateClientUserPassword } from '@/api';
import { useDialogState } from '@/hooks';
import { ClientUser } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useClientUserPasswordChangeDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ClientUserPasswordChangeDialog, props, open };
}

type ClientUserId = ClientUser['id'];
type PageState = 'passwordForm' | 'copyForm';
type FormValues = {
  password: string;
};

function ClientUserPasswordChangeDialog({
  openState,
  close,
  onSuccess,
  clientUser,
  onClose,
}: {
  onSuccess?: (clientUserId: ClientUserId) => void;
  onClose?: (clientUserId: ClientUserId) => void;
  clientUser: ClientUser;
  openState: boolean;
  close: () => void;
}) {
  const [pageState, setPageState] = useState<PageState>('passwordForm');
  function changePage(pageState: PageState) {
    setPageState(pageState);
  }

  const [passwordFormValues, setPasswordFormValues] = useState<FormValues>({} as FormValues);
  function updateSubmittedFormValues(formValues: FormValues) {
    setPasswordFormValues(formValues);
  }

  const [clientUserId, setClientUserId] = useState<ClientUserId | undefined>(undefined);
  function updateClientUserId(clientUserId: ClientUserId) {
    setClientUserId(clientUserId);
  }

  function handleFormSubmit(formValues: FormValues) {
    updateSubmittedFormValues(formValues);
    changePage('copyForm');
  }
  function handleFormOnSuccess(clientUserId: ClientUserId) {
    updateClientUserId(clientUserId);
    onSuccess && onSuccess(clientUserId);
  }

  function handleDialogClose() {
    clientUserId && onClose && onClose(clientUserId);
    close();

    setTimeout(() => {
      changePage('passwordForm');
      cleanUp();
    }, 500);
  }

  function cleanUp() {
    setClientUserId(undefined);
    setPasswordFormValues({} as FormValues);
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      {pageState === 'passwordForm' && (
        <ClientUserPasswordChangeForm
          close={close}
          onSuccess={handleFormOnSuccess}
          onSubmit={handleFormSubmit}
          clientUser={clientUser}
        />
      )}
      {pageState === 'copyForm' && (
        <ClientUserPasswordCopy
          formValues={passwordFormValues}
          close={close}
          onClose={handleDialogClose}
        />
      )}
    </EcoisDialog>
  );
}

function ClientUserPasswordCopy({
  formValues,
  close,
  onClose,
}: {
  formValues: FormValues | undefined;
  close: () => void;
  onClose?: () => void;
}) {
  function handleCloseClick() {
    close();
    onClose && onClose();
  }

  function generateCopyText(formValues: FormValues) {
    return `パスワード\n${formValues.password}`;
  }

  const clipboardText = formValues ? generateCopyText(formValues) : '';

  return (
    <CopyInfoContent
      onClose={handleCloseClick}
      titleText='パスワードが変更されました'
      bodyText={
        '以下のログイン情報をコピーして、アドミンユーザーに共有してください。\n※パスワードは一度しか表示されません。'
      }
      clipboardText={clipboardText}
      contentElements={
        <>
          <Label>パスワード</Label>
          <Value>{formValues?.password}</Value>
        </>
      }
    />
  );
}

function ClientUserPasswordChangeForm({
  close,
  onSubmit,
  onSuccess,
  clientUser,
}: {
  close: () => void;
  onSubmit?: (formValues: FormValues) => void;
  onSuccess?: (clientUserId: ClientUserId) => void;
  clientUser: ClientUser;
}) {
  const { trigger, isLoading } = useUpdateClientUserPassword();

  const {
    control,
    formState: { isValid, isDirty, errors },
    handleSubmit,
    reset,
  } = useForm({
    defaultValues: {
      password: '',
    },
    resolver: yupResolver(clientUserPasswordSchema),
  });

  function handleCancel() {
    reset();
    close();
  }

  async function requestChangePassword(password: string) {
    try {
      const response = await trigger({
        body: {
          password,
        },
        urlParameter: {
          clientUserId: clientUser.id,
        },
      });
      return response;
    } catch (error) {
      console.error(error);
    }
  }

  async function handleFormSubmit(formValues: { password: string }) {
    const response = await requestChangePassword(formValues.password);
    if (response) {
      onSuccess && onSuccess(response?.clientUserId);
    }
    onSubmit && onSubmit(formValues);
    reset();
  }

  return (
    <>
      <DialogTitle id='alert-dialog-title'>パスワードの変更</DialogTitle>
      <DialogContent>
        <Typography variant={'body2'} color={'textSecondary'}>
          新しいパスワードを入力してください。
        </Typography>

        <Stack component={'form'} spacing={2} py={2}>
          <ClientUserPasswordField control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit((data) => {
            handleFormSubmit(data);
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </>
  );
}
export { useClientUserPasswordChangeDialog };
