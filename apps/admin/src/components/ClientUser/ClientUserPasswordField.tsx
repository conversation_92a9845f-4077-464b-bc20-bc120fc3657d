import { TextField, InputAdornment, Button } from '@mui/material';
import { Controller } from 'react-hook-form';
import { generateRandomPassword } from './scripts/generateRandomPassword';
import { PasswordChecker } from './scripts/PasswordChecker';

function ClientUserPasswordField({ control, errors }: { control: any; errors: any }) {
  return (
    <Controller
      name='password'
      control={control}
      render={({ field }) => (
        <>
          <TextField
            {...field}
            id='clientUser-password'
            label='パスワード(必須)'
            variant='outlined'
            error={'password' in errors}
            InputProps={{
              endAdornment: (
                <InputAdornment position='end'>
                  <Button
                    onClick={() => {
                      const randomPassword = generateRandomPassword();
                      field.onChange(randomPassword);
                    }}
                  >
                    自動生成
                  </Button>
                </InputAdornment>
              ),
            }}
          />
          <PasswordChecker text={field.value} />
        </>
      )}
    />
  );
}

export { ClientUserPasswordField };
