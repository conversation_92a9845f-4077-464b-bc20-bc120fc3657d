import { LoadingButton } from '@mui/lab';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typography,
  Box,
  LinearProgress,
  DialogContent,
  Link,
  DialogTitle,
  DialogActions,
  Divider,
} from '@mui/material';
import { useMemo, useState, useRef, useEffect } from 'react';
import { TreeApi, Tree } from 'react-arborist';
import { NodeEnergyType } from '../GraphSetting';
import { useTreeData } from '../GraphSetting/hooks/useTreeData';
import { TreeNodeComponent } from '../GraphSetting/Node';
import { NodeDatapoints } from '../GraphSetting/NodeDatapoints';
import { NodeEditor } from '../GraphSetting/NodeEditor';
import { NodeFiler } from '../GraphSetting/NodeFiler';
import { NodeName } from '../GraphSetting/NodeName';
import { useListNodesTree, useListProjectLocations } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDOMRect, useDialogState } from '@/hooks';
import { Node, Project, TreeNodeWithHash, Utility } from '@/types';

type UseProjectNodeDialogProps = {
  projectId: Project['id'];
};
function useProjectNodeDialog({ projectId }: UseProjectNodeDialogProps) {
  const { open, openState, close, context } = useDialogState<Utility>();

  const props = {
    openState,
    close,
    projectId,
    context,
  };

  return { ProjectNodeDialog, props, open, projectId };
}

type ProjectNodeDialogProps = HooksDialogProps<UseProjectNodeDialogProps> & {
  onSubmit?: (node: Node, context: Utility) => void;
  context?: Utility;
};
function ProjectNodeDialog({
  projectId,
  openState,
  close,
  onSubmit,
  context,
}: ProjectNodeDialogProps) {
  const [treeWrapperRef, treeWrapperRect] = useDOMRect();

  const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null);
  const { data, isLoading, trigger } = useListNodesTree();

  // ノードの深さ制限を1にしている
  const { treeData, setValue } = useTreeData([], { maxDepth: 1 });

  const treeRef = useRef<TreeApi<TreeNodeWithHash> | null>(null);

  useEffect(() => {
    if (projectId === undefined) return;
    trigger({
      urlParameter: {
        projectId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  useEffect(() => {
    if (data === undefined) return;
    setValue(data ? data.nodesTree : []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const selectedNode = useMemo(() => {
    const searchNodeById = (nodes: TreeNodeWithHash[], id: number): TreeNodeWithHash | null => {
      for (const node of nodes) {
        if (node.id === id) {
          return node;
        }
        const result = searchNodeById(node.children, id);
        if (result) {
          return result;
        }
      }
      return null;
    };

    return selectedNodeId ? searchNodeById(treeData, selectedNodeId) : null;
  }, [treeData, selectedNodeId]);

  const { trigger: fetchProjectLocations, data: projectLocationResponse } =
    useListProjectLocations();

  useEffect(() => {
    if (projectId === undefined) return;
    fetchProjectLocations({
      urlParameter: {
        projectId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  const locationNotFound = useMemo(() => {
    return projectLocationResponse?.projectLocations.length === 0;
  }, [projectLocationResponse]);

  function handleSubmit() {
    if (selectedNodeId === null || selectedNode === null || context === undefined) return;
    onSubmit?.(selectedNode, context);
    close();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xl'}>
      <DialogTitle
        component={Stack}
        direction={'row'}
        alignItems={'center'}
        justifyContent={'space-between'}
      >
        <Stack direction={'row'} alignItems={'center'} spacing={2}>
          <Typography variant='body1'>ノードを選択</Typography>
          <Divider orientation={'vertical'} flexItem />
          <Stack>
            <Typography variant='caption'>対象のユーティリティ</Typography>
            <Typography>{context?.name}</Typography>
          </Stack>
        </Stack>
        <DialogActions>
          <Button onClick={close}>キャンセル</Button>
          <LoadingButton
            disabled={selectedNodeId === null}
            loading={isLoading}
            variant={'contained'}
            onClick={handleSubmit}
            disableElevation
          >
            追加
          </LoadingButton>
        </DialogActions>
      </DialogTitle>
      <DialogContent>
        <Box width={'90vw'} height={'80vh'}>
          {locationNotFound ? (
            <Stack spacing={2}>
              <Typography>
                ノードがありません。案件とロケーションを紐づける必要があります。
              </Typography>
              <Box>
                <Button
                  LinkComponent={Link}
                  href={'/projects/' + projectId + '/location'}
                  variant='contained'
                >
                  案件とロケーションを紐づける
                </Button>
              </Box>
            </Stack>
          ) : (
            <NodeFiler variant={'outlined'}>
              <NodeFiler.Left>
                <NodeFiler.ColumnTitle>
                  <NodeFiler.TitleLabel>ノード</NodeFiler.TitleLabel>
                </NodeFiler.ColumnTitle>
                <Box position={'relative'} height={'100%'} ref={treeWrapperRef}>
                  {isLoading && (
                    <Box position={'absolute'} width={'100%'}>
                      <LinearProgress />
                    </Box>
                  )}
                  {treeData.length > 0 && (
                    <Tree
                      ref={treeRef}
                      data={treeData}
                      idAccessor={'hash'}
                      rowHeight={32}
                      width={'100%'}
                      height={treeWrapperRect.height}
                      onActivate={(node) => {
                        setSelectedNodeId(node.data.id);
                      }}
                      onSelect={(node) => {
                        if (node.length === 0) {
                          setSelectedNodeId(null);
                        }
                      }}
                      disableDrag
                      openByDefault={false}
                      disableMultiSelection
                    >
                      {TreeNodeComponent}
                    </Tree>
                  )}
                </Box>
              </NodeFiler.Left>
              <NodeFiler.Right>
                <NodeFiler.ColumnTitle>
                  <NodeFiler.TitleLabel>ノード詳細</NodeFiler.TitleLabel>
                </NodeFiler.ColumnTitle>
                <NodeEditor>
                  {treeData.length === 0 ? (
                    <>
                      <Typography>登録されているノードは0件です</Typography>
                      <Box>
                        <Button
                          variant={'contained'}
                          LinkComponent={Link}
                          href={'/projects/' + projectId + '/graph/edit'}
                        >
                          ノードを追加
                        </Button>
                      </Box>
                    </>
                  ) : selectedNodeId ? (
                    <>
                      <NodeName node={selectedNode} />
                      <NodeDatapoints node={selectedNode} />
                      {selectedNode && <NodeEnergyType node={selectedNode} />}
                    </>
                  ) : (
                    <Typography>ノードが選択されていません</Typography>
                  )}
                </NodeEditor>
              </NodeFiler.Right>
            </NodeFiler>
          )}
        </Box>
      </DialogContent>
    </EcoisDialog>
  );
}
export { useProjectNodeDialog };
