import { LoadingButton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { LocationForm } from './LocationForm';
import { useLocationForm } from './useLocationForm';
import { useCreateLocation } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Location } from '@/types';

/* 

Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useLocationAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { LocationAddDialog, props, open };
}

function LocationAddDialog({
  openState,
  close,
  onSubmit,
  onCancel,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (location: Location) => void;
  onCancel?: () => void;
}) {
  const { trigger, isLoading } = useCreateLocation();
  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset,
  } = useLocationForm();

  return (
    <EcoisDialog
      open={openState}
      close={close}
      fullWidth
      maxWidth={'xs'}
      onClose={() => {
        onCancel && onCancel();
      }}
    >
      <DialogTitle id='alert-dialog-title'>ロケーションを追加</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <LocationForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            onCancel && onCancel();
            close();
          }}
        >
          キャンセル
        </Button>
        <LoadingButton
          disabled={!isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                body: {
                  name: data.name,
                  backlogIssueKey: data.backlogIssueKey,
                },
              });
              reset();
              if (response) {
                onSubmit && onSubmit(response?.location);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useLocationAddDialog };
