import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { defaultLocationValues } from './defaultLocationValues';
import { Location } from '@/types';

const schema = yup.object().shape({
  name: yup.string().required('ロケーション名は必須です'),
  backlogIssueKey: yup.string(),
});

function useLocationForm(defaultValues?: Partial<Location>) {
  const { reset, ...rest } = useForm({
    mode: 'onChange',
    defaultValues: {
      ...defaultLocationValues(defaultValues),
    },
    resolver: yupResolver(schema),
  });

  return { reset, ...rest };
}

export { useLocationForm };
