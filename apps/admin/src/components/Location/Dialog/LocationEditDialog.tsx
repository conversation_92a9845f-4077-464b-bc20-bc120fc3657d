import { Loading<PERSON>utton } from '@mui/lab';
import { <PERSON><PERSON>T<PERSON><PERSON>, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { useEffect } from 'react';
import { defaultLocationValues } from './defaultLocationValues';
import { LocationForm } from './LocationForm';
import { useLocationForm } from './useLocationForm';
import { useUpdateLocation } from '@/api/useUpdateLocation';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Location } from '@/types';

/* 

Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useLocationEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { LocationEditDialog, props, open };
}

function LocationEditDialog({
  openState,
  close,
  onSubmit,
  location,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (location: Location) => void;
  location: Location;
}) {
  const { trigger, isLoading, reset: requestReset } = useUpdateLocation();
  const { control, formState, handleSubmit, reset } = useLocationForm({
    ...location,
  });

  function handleClose() {
    reset();
    requestReset();
    close();
  }

  useEffect(() => {
    reset({
      ...defaultLocationValues(location),
    });
  }, [location, reset]);

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>ロケーションを編集</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <LocationForm control={control} errors={formState.errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!formState.isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                body: {
                  name: data.name,
                  backlogIssueKey: data.backlogIssueKey,
                },
                urlParameter: {
                  locationId: location?.id,
                },
              });

              if (response) {
                const { name, backlogIssueKey } = response.location;
                reset({
                  name,
                  backlogIssueKey,
                });
                onSubmit && onSubmit(response?.location);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          編集
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useLocationEditDialog };
