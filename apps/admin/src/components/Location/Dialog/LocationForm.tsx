import { TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

function LocationForm({ control, errors }: { control: any; errors: any }) {
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='location-name'
            label='ロケーション名(必須)'
            variant='outlined'
            error={'name' in errors}
            helperText={'name' in errors ? errors.name?.message : ''}
          />
        )}
      />
      <Controller
        name='backlogIssueKey'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='location-backlogIssueKey'
            label='Backlog課題Key'
            variant='outlined'
          />
        )}
      />
    </>
  );
}

export { LocationForm };
