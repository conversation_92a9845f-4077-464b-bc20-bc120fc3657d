import { Edit, Delete, Search } from '@mui/icons-material';
import { Button, InputAdornment, Link, Stack, TextField } from '@mui/material';
import { useDebounce } from 'lib/hooks';
import { useState } from 'react';
import { useEffectOnce } from 'react-use';
import {
  ColumnListTitleButton,
  CLMenuItem,
  CLDetailPaneContentItem,
  CLDetailPaneContentLabel,
  CLDetailPaneContentValue,
  CLDetailPaneTitleButton,
} from '../ColumnList/ColumnList';
import { ColumnListBase } from '../ColumnList/ColumnListBase';
import { useMultiColumnViewContext } from '../ColumnList/context/MultiColumViewContext';
import { ColumnProps } from '../ColumnList/type';
import { useDeleteDialog } from '../Dialog/DeleteDialog';
import { useLocationAddDialog } from './Dialog/LocationAddDialog';
import { useLocationEditDialog } from './Dialog/LocationEditDialog';
import { useDeleteLocation } from '@/api';
import { useDialogNavigation } from '@/hooks';
import { Location } from '@/types';

type SearchState = {
  id: string;
  name: string;
  locationAddress: string;
};

const initialSearchState: SearchState = {
  id: '',
  name: '',
  locationAddress: '',
};

function filterLocation(locations: Location[], searchState: SearchState): Location[] {
  const { id, name, locationAddress } = searchState;
  const ids = id.split(' ');
  const names = name.split(' ');
  const locationAddresses = locationAddress.split(' ');

  return locations.filter((location) => {
    const idMatch = id === '' || ids.some((id) => location.id === parseInt(id, 10));
    const nameMatch = name === '' || names.some((name) => location.name.includes(name));
    const locationAddressMatch =
      locationAddress === '' ||
      locationAddresses.some((locationAddress) => location.address.includes(locationAddress));

    return (idMatch && nameMatch && locationAddressMatch) as boolean;
  });
}

function LocationColumn({
  mode = 'view',
  checkboxSelection = false,
  onSelectionModelChange,
  locationIds,
}: ColumnProps & {
  mode?: 'view' | 'edit';
  locationIds?: number[];
}) {
  // TODO フィルタリング機能を実装?
  const context = useMultiColumnViewContext();

  const selectedLocationItem = context.location.selectedItem;

  const { trigger: deleteLocation, isLoading: isDeleting } = useDeleteLocation();

  const { LocationAddDialog, open: openAddDialog, props: addDialogProps } = useLocationAddDialog();
  const {
    LocationEditDialog,
    open: openEditDialog,
    props: editDialogProps,
  } = useLocationEditDialog();

  const { DeleteDialog, open: openDeleteDialog, props: deleteDialogProps } = useDeleteDialog();

  useEffectOnce(() => {
    context.location.fetch(locationIds ? [...locationIds] : []);
  });

  const { cancelNavigate, submitNavigate } = useDialogNavigation({
    dialogTriggerUrl: '/location/add',
    handleAccess: () => {
      openAddDialog();
    },
  });

  function handleLocationAddSubmit() {
    context.location.fetch([]);
    submitNavigate(`/location`);
  }

  function handleLocationAddCancel() {
    cancelNavigate('/location');
  }

  // TODO フィルタリング機能を実装?
  // - Debouseを実装する
  // - あと一歩
  //
  const [searchState, setSearchState] = useState<SearchState>(initialSearchState);

  const debounce = useDebounce(300);

  function handleInputChange(
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    targeKey: keyof SearchState,
    data: Location[],
    update: (data: Location[]) => void,
  ) {
    const currentSearchState = {
      ...searchState,
      [targeKey]: event.target.value,
    };

    setSearchState(currentSearchState);

    debounce(() => {
      update(filterLocation(data, currentSearchState));
    });
  }

  return (
    <>
      <ColumnListBase
        title='ロケーション'
        data={context.location.data}
        error={context.location.error}
        isLoading={context.location.isLoading}
        checkboxSelection={checkboxSelection}
        onCheckedItemsChange={onSelectionModelChange}
        onListItemClick={(location) => {
          context.unit.fetch([location.id]);
        }}
        listControlButtons={(location) =>
          mode === 'edit' && (
            <Button
              size='small'
              startIcon={<Edit />}
              onClick={() => {
                context.location.updateSelectedId(location.id);
                openEditDialog();
                context.unit.fetch([location.id]);
              }}
            >
              編集
            </Button>
          )
        }
        selectedId={context.location.selectedId}
        onUpdateSelectedId={context.location.updateSelectedId}
        normalStateButton={
          mode === 'edit' && (
            <ColumnListTitleButton
              onClick={() => {
                openAddDialog();
              }}
            >
              追加
            </ColumnListTitleButton>
          )
        }
        optionMenuButtons={
          mode === 'edit' && (
            <>
              <CLDetailPaneTitleButton label={'オプション'}>
                <CLMenuItem
                  label='編集'
                  icon={<Edit />}
                  onClick={() => {
                    openEditDialog();
                  }}
                />
                <CLMenuItem
                  label='削除'
                  icon={<Delete />}
                  color='error'
                  onClick={() => {
                    openDeleteDialog();
                  }}
                />
              </CLDetailPaneTitleButton>
            </>
          )
        }
        detailContents={
          <>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>ロケーションID</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedLocationItem?.id}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>ロケーションアドレス</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedLocationItem?.address}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>Backlog課題</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {(
                  <Link
                    href={`https://picoada.backlog.jp/view/${selectedLocationItem?.backlogIssueKey}`}
                    target='_blank'
                    rel='noreferrer noopener'
                  >
                    {selectedLocationItem?.backlogIssueKey}
                  </Link>
                ) || '未設定'}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
          </>
        }
        useAdvancedSearch
        advancedSearchComponent={(data, update) => (
          <Stack spacing={1}>
            <TextField
              size='small'
              variant='outlined'
              placeholder='ロケーションアドレス'
              onChange={(e) => handleInputChange(e, 'locationAddress', data, update)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              size='small'
              variant='outlined'
              placeholder='名前'
              onChange={(e) => handleInputChange(e, 'name', data, update)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              size='small'
              variant='outlined'
              placeholder='ロケーションID'
              onChange={(e) => handleInputChange(e, 'id', data, update)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Stack>
        )}
      />
      <LocationAddDialog
        {...addDialogProps}
        onCancel={handleLocationAddCancel}
        onSubmit={handleLocationAddSubmit}
      />
      {selectedLocationItem && (
        <>
          <LocationEditDialog
            {...editDialogProps}
            location={selectedLocationItem}
            onSubmit={() => {
              context.location.fetch([]);
            }}
          />

          <DeleteDialog
            {...deleteDialogProps}
            title={`${selectedLocationItem?.name}の削除`}
            isLoading={isDeleting}
            onSubmit={async () => {
              try {
                await deleteLocation({
                  urlParameter: { locationId: selectedLocationItem?.id },
                });
                context.location.updateSelectedId(null);
                context.location.fetch([]);
                context.unit.reset();
                context.datapoint.reset();
              } catch (e) {
                return Promise.reject(e);
              }
            }}
          />
        </>
      )}
    </>
  );
}

export { LocationColumn };
