type Location = {
  id: number;
  name: string;
  address: string;
  backlogIssueKey: string;
};

type LocationItem = Location & {
  detail: DetailItem[];
};

type ColumnListDetailItems<T> = {
  label: string;
  property: keyof T;
};

type Unit = {
  id: number;
  name: string;
  uniType: string;
  simId: string;
  locationId: number;
};

type Datapoint = {
  id: number;
  name: string;
  terminalId: string;
  datapointAddress: string;
  pulserateValue: number;
  pulserateUnit: string;
  measurementInterval: number;
  isActivate: boolean;
  deviceNo: number;
  unitId: number;
};

type SampleUnitItem = Unit & {
  detail: DetailItem[];
};

type DetailItem = {
  label: string;
  value: number | string | boolean;
};

type ContentProps = {
  id: number;
  name: string;
  checked?: boolean;
  detail: DetailItem[];
};
export type {
  LocationItem,
  SampleUnitItem,
  Location,
  Unit,
  DetailItem,
  ContentProps,
  ColumnListDetailItems,
  Datapoint,
};
