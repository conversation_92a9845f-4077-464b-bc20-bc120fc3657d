import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { DatapointsAvailableForm } from './DatapointsAvailableForm';
import { useUpdateDatapointsAvailable } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Datapoint } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useDatapointsAvailableDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { DatapointsAvailableDialog, props, open };
}

const schema = yup.object().shape({
  isAvailable: yup.boolean().required(),
});

function DatapointsAvailableDialog({
  checkedItemIds,
  openState,
  close,
  onSubmit,
}: {
  openState: boolean;
  close: () => void;
  checkedItemIds: number[];
  onSubmit?: (datapoints: Datapoint[]) => void;
}) {
  const { trigger, isLoading } = useUpdateDatapointsAvailable();

  const {
    control,
    formState: { errors, isValid, isDirty },
    handleSubmit,
    reset,
  } = useForm({
    defaultValues: {
      isAvailable: true,
    },
    resolver: yupResolver(schema),
  });

  function handleClose() {
    reset();
    close();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>
        {checkedItemIds.length}件のデータポイントの有効化を一括変更
      </DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <DatapointsAvailableForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const response = await trigger({
                body: {
                  datapointIds: checkedItemIds,
                  isAvailable: data.isAvailable,
                },
              });

              if (response) {
                console.log(response.datapoints);
                // const datapointValue = response.datapoint;
                // reset({
                //   ...datapointValue,
                // });
                onSubmit && onSubmit(response?.datapoints);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          変更
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useDatapointsAvailableDialog };
