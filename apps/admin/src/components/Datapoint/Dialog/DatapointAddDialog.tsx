import { Loading<PERSON>utton } from '@mui/lab';
import { DialogTitle, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { DatapointForm } from './DatapointForm';
import { useDatapointForm } from './useDatapointForm';
import { useCreateDatapoint } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Datapoint, PulserateUnit } from '@/types';

/* 
 
Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useDatapointAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { DatapointAddDialog, props, open };
}

function DatapointAddDialog({
  openState,
  close,
  onSubmit,
  selectedUnitId,
  terminalIds,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (datapoint: Datapoint) => void;
  selectedUnitId: number;
  terminalIds: number[];
}) {
  const { trigger, isLoading } = useCreateDatapoint();

  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset,
  } = useDatapointForm({ terminalIds });

  function handleClose() {
    reset();
    close();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>データポイントを追加</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <DatapointForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const body = {
                unitId: selectedUnitId,
                name: data.name,
                terminalId: Number(data.terminalId),
                deviceNo: data.deviceNo === null ? null : Number(data.deviceNo),
                pulserate: Number(data.pulserate),
                pulserateUnit: data.pulserateUnit as PulserateUnit,
                interval: Number(data.interval),
                calcMethod: data.calcMethod,
                isAvailable: data.isAvailable,
              };
              const response = await trigger({
                body: body,
              });
              reset();
              if (response) {
                onSubmit && onSubmit(response?.datapoint);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          追加
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useDatapointAddDialog };
