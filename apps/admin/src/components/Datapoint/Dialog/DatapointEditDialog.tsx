import { Lo<PERSON><PERSON><PERSON>on } from '@mui/lab';
import { <PERSON><PERSON>T<PERSON>le, DialogContent, Stack, DialogActions, Button } from '@mui/material';
import { useEffect } from 'react';
import { DatapointForm } from './DatapointForm';
import { defaultDatapointValues } from './defaultDatapointValues';
import { useDatapointForm } from './useDatapointForm';
import { useUpdateDatapoint } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { Datapoint, PulserateUnit } from '@/types';

/* 

Render Hooks pattern

const { LocationAddDialog, props, open } = useLocationAddDialog();

<LocationAddDialog {...props} />
                   ^^^^^^^^ set props to component
*/
function useDatapointEditDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { DatapointEditDialog, props, open };
}

function DatapointEditDialog({
  openState,
  close,
  onSubmit,
  datapoint,
  terminalIds,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (datapoint: Datapoint) => void;
  datapoint: Datapoint;
  terminalIds: number[];
}) {
  const { trigger, isLoading } = useUpdateDatapoint();
  const {
    control,
    formState: { errors, isValid, isDirty },
    handleSubmit,
    reset,
  } = useDatapointForm({
    defaultValues: datapoint,
    terminalIds,
  });

  function handleClose() {
    reset();
    close();
  }

  useEffect(() => {
    reset({
      ...defaultDatapointValues(datapoint),
    });
  }, [datapoint, reset]);

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle id='alert-dialog-title'>データポイントを編集</DialogTitle>
      <DialogContent>
        <Stack component={'form'} spacing={2} py={2}>
          <DatapointForm control={control} errors={errors} />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>キャンセル</Button>
        <LoadingButton
          disabled={!isValid || !isDirty}
          loading={isLoading}
          variant={'contained'}
          onClick={handleSubmit(async (data) => {
            try {
              const body = {
                ...data,
                terminalId: Number(data.terminalId),
                unitId: datapoint.unitId,
                deviceNo: data.deviceNo === null ? null : Number(data.deviceNo),
                pulserateUnit: data.pulserateUnit as PulserateUnit,
                interval: Number(data.interval),
                pulserate: Number(data.pulserate),
              };
              const response = await trigger({
                body: body,
                urlParameter: {
                  datapointId: datapoint.id,
                },
              });

              if (response) {
                const datapointValue = response.datapoint;
                reset({
                  ...datapointValue,
                });
                onSubmit && onSubmit(response?.datapoint);
              }
              close();
            } catch (error) {
              console.error(error);
            }
          })}
        >
          編集
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useDatapointEditDialog };
