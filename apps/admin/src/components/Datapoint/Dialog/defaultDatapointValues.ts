import { Datapoint } from '@/types';

function defaultDatapointValues(datapoint: Partial<Datapoint> | undefined) {
  return {
    name: datapoint?.name ?? '',
    terminalId: datapoint?.terminalId ?? '',
    deviceNo: datapoint?.deviceNo ?? '',
    pulserate: datapoint?.pulserate ?? '',
    pulserateUnit: datapoint?.pulserateUnit ?? '',
    interval: datapoint?.interval ?? 10,
    calcMethod: datapoint?.calcMethod ?? true,
    isAvailable: datapoint?.isAvailable ?? true,
  };
}
export { defaultDatapointValues };
