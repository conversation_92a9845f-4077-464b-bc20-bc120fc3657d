import { FormControl, FormControlLabel, FormLabel, Radio, RadioGroup } from '@mui/material';
import { Controller } from 'react-hook-form';

function DatapointsAvailableForm({ control }: { control: any; errors: any }) {
  return (
    <>
      <Controller
        name='isAvailable'
        control={control}
        render={({ field }) => (
          <FormControl>
            <FormLabel id='datapoint-isAvailable-label'>データ計測有効化</FormLabel>
            <RadioGroup
              {...field}
              aria-labelledby='datapoint-isAvailable-label'
              defaultValue={true}
              name='datapoint-isAvailable'
            >
              <FormControlLabel value={true} control={<Radio />} label='有効' />
              <FormControlLabel value={false} control={<Radio />} label='無効' />
            </RadioGroup>
          </FormControl>
        )}
      />
    </>
  );
}

export { DatapointsAvailableForm };
