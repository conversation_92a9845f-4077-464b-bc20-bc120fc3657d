import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { defaultDatapointValues } from './defaultDatapointValues';
import { Datapoint } from '@/types';

// yup schema methods

const schema = ({
  terminalIds,
  defaultValues,
}: {
  terminalIds: number[];
  defaultValues: Partial<Datapoint> | undefined;
}) => {
  const filterDefaultTerminalId = (terminalId: number) => terminalId !== defaultValues?.terminalId;

  return yup.object().shape({
    name: yup.string().required('データポイント名は必須です'),
    terminalId: yup
      .string()
      .required('必須項目です')
      .matches(/^[1-9]\d*$/, '1以上の数値が入力可能です')
      .matches(/^\d+$/, '整数のみ入力可能です')
      .test('is-unique', '他に重複する値が存在します', (value) => {
        if (value === '' || value === null) return true; // 空文字とnullの場合の重複チェック回避
        return !terminalIds.filter(filterDefaultTerminalId).includes(Number(value));
      }),
    deviceNo: yup
      .string()
      .nullable()
      .transform((value) => (value === '' ? null : value)) //空文字をnullに変換
      .matches(/^[1-9]\d*$/, '1以上の数値が入力可能です')
      .matches(/^\d+$/, '整数のみ入力可能です'),
    pulserate: yup
      .string()
      .required('必須項目です')
      .test('is-decimal', '0以上の整数か少数を入力してください', (value) => {
        if (value !== undefined) {
          // Check if the value starts with '0' followed by another digit and not a decimal point
          if (/^0[0-9]+/.test(value)) {
            return false;
          }
          // Check if the value is a positive integer or decimal number
          return /^(0|[1-9][0-9]*)(\.[0-9]+)?$/.test(value);
        }
        return true;
      }),
    pulserateUnit: yup.string().required('必須項目です'),
    interval: yup
      .string()
      .required('必須項目です')
      .matches(/^\d+$/, '整数のみ入力可能です')
      .matches(/^[1-9]\d*$/, 'ゼロで始まらない数値が入力可能です'),
    calcMethod: yup.string(),
    isAvailable: yup.boolean(),
  });
};

function useDatapointForm({
  defaultValues,
  terminalIds,
}: {
  defaultValues?: Partial<Datapoint>;
  terminalIds: number[];
}) {
  return useForm({
    mode: 'onChange',
    defaultValues: {
      ...defaultDatapointValues(defaultValues),
    },
    resolver: yupResolver(
      schema({
        terminalIds,
        defaultValues,
      }),
    ),
  });
}

export { useDatapointForm };
