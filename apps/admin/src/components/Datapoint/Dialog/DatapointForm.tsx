import {
  FormControl,
  FormControlLabel,
  FormLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  TextField,
} from '@mui/material';
import { Control, Controller } from 'react-hook-form';

function DatapointForm({
  control,
  errors,
}: {
  control: Control<
    {
      name: string;
      terminalId: string | number;
      deviceNo: string | number;
      pulserate: string | number;
      pulserateUnit: string;
      interval: number;
      calcMethod: boolean;
      isAvailable: boolean;
    },
    any
  >;
  errors: any;
}) {
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='datapoint-name'
            label='データポイント名(必須)'
            variant='outlined'
            error={'name' in errors}
            helperText={'name' in errors ? errors.name?.message : ''}
          />
        )}
      />
      <Controller
        name='terminalId'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='datapoint-terminalId'
            label='端子ID(必須)'
            InputProps={{
              startAdornment: <InputAdornment position='end'>p</InputAdornment>,
            }}
            variant='outlined'
            error={'terminalId' in errors}
            helperText={'terminalId' in errors ? errors.terminalId?.message : ''}
          />
        )}
      />
      <Controller
        name='deviceNo'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='deviceNo'
            label='機器No'
            variant='outlined'
            InputProps={{
              startAdornment: <InputAdornment position='end'>U</InputAdornment>,
            }}
            error={'deviceNo' in errors}
            helperText={'deviceNo' in errors ? errors.deviceNo?.message : ''}
          />
        )}
      />
      <Stack spacing={1} direction={'row'} width={'100%'}>
        <Controller
          name='pulserate'
          control={control}
          render={({ field }) => (
            <FormControl fullWidth>
              <TextField
                {...field}
                id='datapoint-pulserate'
                label='パルスレート値(必須)'
                variant='outlined'
                error={'pulserate' in errors}
                helperText={'pulserate' in errors ? errors.pulserate?.message : ''}
              />
            </FormControl>
          )}
        />
        <Controller
          name='pulserateUnit'
          control={control}
          render={({ field, formState }) => (
            <FormControl fullWidth>
              <InputLabel id='pulserateUnit'>単位</InputLabel>
              <Select
                {...field}
                labelId='pulserateUnit'
                id='datapoint-pulserateUnit'
                label='単位'
                variant='outlined'
                disabled={formState?.defaultValues?.pulserateUnit === 'L'}
                error={'pulserateUnit' in errors}
              >
                {formState?.defaultValues?.pulserateUnit === 'L' && (
                  <MenuItem value={'L'} selected>
                    L
                  </MenuItem>
                )}
                <MenuItem value={'m3'}>m3</MenuItem>
                <MenuItem value={'kWh'}>kWh</MenuItem>
                <MenuItem value={'℃'}>℃</MenuItem>
              </Select>
            </FormControl>
          )}
        />
      </Stack>
      <Controller
        name='interval'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            id='datapoint-interval'
            label='計測間隔(必須)'
            variant='outlined'
            error={'interval' in errors}
            InputProps={{
              endAdornment: <InputAdornment position='end'>分</InputAdornment>,
            }}
            helperText={'interval' in errors ? errors.interval?.message : ''}
          />
        )}
      />
      <Controller
        name='calcMethod'
        control={control}
        render={({ field }) => (
          <FormControl>
            <FormLabel id='datapoint-calcMethod-label'>積算設定</FormLabel>
            <RadioGroup
              {...field}
              aria-labelledby='datapoint-calcMethod-label'
              defaultValue={true}
              name='datapoint-calcMethod'
              sx={{
                '& > *': {
                  width: 'max-content',
                },
              }}
            >
              <FormControlLabel value={true} control={<Radio />} label='積算値' />
              <FormControlLabel value={false} control={<Radio />} label='平均値' />
            </RadioGroup>
          </FormControl>
        )}
      />
      {/* <Controller
        name='isAvailable'
        control={control}
        render={({ field }) => (
          <FormControl>
            <FormLabel id='datapoint-isAvailable-label'>データ計測有効化</FormLabel>
            <RadioGroup
              {...field}
              aria-labelledby='datapoint-isAvailable-label'
              defaultValue={true}
              name='datapoint-isAvailable'
              sx={{
                '& > *': {
                  width: 'max-content',
                },
              }}
            >
              <FormControlLabel value={true} control={<Radio />} label='有効' />
              <FormControlLabel value={false} control={<Radio />} label='無効' />
            </RadioGroup>
          </FormControl>
        )}
      /> */}
    </>
  );
}

export { DatapointForm };
