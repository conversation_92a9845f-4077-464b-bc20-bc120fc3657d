import { Edit, Delete } from '@mui/icons-material';
import { <PERSON>ton, Box } from '@mui/material';
import { useEffect, useState } from 'react';
import {
  ColumnListTitleButton,
  CLMenuItem,
  CLDetailPaneContentItem,
  CLDetailPaneContentLabel,
  CLDetailPaneContentValue,
  CLDetailPaneTitleButton,
} from '../ColumnList/ColumnList';
import { ColumnListBase } from '../ColumnList/ColumnListBase';
import { useMultiColumnViewContext } from '../ColumnList/context/MultiColumViewContext';
import { ColumnProps } from '../ColumnList/type';
import { useDeleteDialog } from '../Dialog/DeleteDialog';
import { useDatapointAddDialog } from './Dialog/DatapointAddDialog';
import { useDatapointEditDialog } from './Dialog/DatapointEditDialog';
import { useDatapointsAvailableDialog } from './Dialog/DatapointsAvailableDialog';
import { StatusMark } from './StatusMark';
import { useDeleteDatapoint } from '@/api';

function DatapointColumn({
  mode = 'view',
  checkboxSelection = false,
  onSelectionModelChange,
}: ColumnProps) {
  const context = useMultiColumnViewContext();

  const selectedDatapointItem = context.datapoint.selectedItem;

  const [checkedItemIds, setCheckedItemIds] = useState<number[]>([]);
  function handleCheckedItemChange(ids: number[]) {
    setCheckedItemIds(ids);
    onSelectionModelChange && onSelectionModelChange(ids);
  }

  useEffect(() => {
    setCheckedItemIds([]);
  }, [context.datapoint.data]);

  const { trigger: deleteDatapoint, isLoading: isDeleting } = useDeleteDatapoint();

  const {
    DatapointAddDialog,
    open: openAddDialog,
    props: addDialogProps,
  } = useDatapointAddDialog();

  const {
    DatapointEditDialog,
    open: openEditDialog,
    props: editDialogProps,
  } = useDatapointEditDialog();

  const {
    DatapointsAvailableDialog,
    open: openDatapointsAvailableDialog,
    props: datapointsAvailableDialogProps,
  } = useDatapointsAvailableDialog();

  const { DeleteDialog, open: openDeleteDialog, props: deleteDialogProps } = useDeleteDialog();

  return (
    <>
      <ColumnListBase
        title='データポイント'
        data={context.datapoint.data}
        error={context.datapoint.error}
        isLoading={context.datapoint.isLoading}
        checkboxSelection={checkboxSelection}
        onCheckedItemsChange={handleCheckedItemChange}
        onUpdateSelectedId={context.datapoint.updateSelectedId}
        selectedId={context.datapoint.selectedId}
        showDatapointStatus={true}
        listControlButtons={(datapoint) =>
          mode === 'edit' && (
            <Button
              size='small'
              startIcon={<Edit />}
              onClick={() => {
                context.datapoint.updateSelectedId(datapoint.id);
                openEditDialog();
              }}
            >
              編集
            </Button>
          )
        }
        normalStateButton={
          mode === 'edit' && (
            <ColumnListTitleButton
              disabled={!context.datapoint.data || !context.unit.selectedId}
              onClick={() => {
                openAddDialog();
              }}
            >
              追加
            </ColumnListTitleButton>
          )
        }
        checkedStateButton={
          mode === 'edit' && (
            <ColumnListTitleButton
              variant={'outlined'}
              onClick={() => {
                openDatapointsAvailableDialog();
              }}
            >
              計測状態の切替
            </ColumnListTitleButton>
          )
        }
        optionMenuButtons={
          mode === 'edit' && (
            <CLDetailPaneTitleButton label={'オプション'}>
              <CLMenuItem
                label='編集'
                icon={<Edit />}
                onClick={() => {
                  openEditDialog();
                }}
              />
              <CLMenuItem
                label='削除'
                icon={<Delete />}
                color='error'
                onClick={() => {
                  openDeleteDialog();
                }}
              />
            </CLDetailPaneTitleButton>
          )
        }
        detailContents={
          <>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>データポイントID</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedDatapointItem?.id}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>データポイントアドレス</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>{selectedDatapointItem?.address}</CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>パルスレート値</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {selectedDatapointItem?.pulserate}
                {selectedDatapointItem?.pulserateUnit === 'm3' ? (
                  <>m&sup3;</>
                ) : (
                  selectedDatapointItem?.pulserateUnit
                )}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>計測間隔</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {selectedDatapointItem?.interval + '分'}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>データ計測有効化</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {selectedDatapointItem?.isAvailable ? (
                  <Box style={{ display: 'flex', alignItems: 'center' }}>
                    <StatusMark isAvailable={true} />
                    <span>有効</span>
                  </Box>
                ) : (
                  <Box style={{ display: 'flex', alignItems: 'center' }}>
                    <StatusMark isAvailable={false} />
                    <span>無効</span>
                  </Box>
                )}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>端子ID</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {'p' + selectedDatapointItem?.terminalId}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>機器No</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {selectedDatapointItem?.deviceNo ? 'U' + selectedDatapointItem?.deviceNo : '未設定'}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
            <CLDetailPaneContentItem>
              <CLDetailPaneContentLabel>積算設定</CLDetailPaneContentLabel>
              <CLDetailPaneContentValue>
                {selectedDatapointItem?.calcMethod ? '積算値' : '平均値'}
              </CLDetailPaneContentValue>
            </CLDetailPaneContentItem>
          </>
        }
      />
      {context.unit.selectedId && context.datapoint.data && (
        <>
          <DatapointAddDialog
            {...addDialogProps}
            onSubmit={(datapoint) => {
              context.datapoint.fetch([datapoint.unitId]);
            }}
            selectedUnitId={context.unit.selectedId}
            terminalIds={context.datapoint.data?.map((datapoint) => datapoint.terminalId)}
          />
        </>
      )}
      {selectedDatapointItem && context.datapoint.data && (
        <>
          <DatapointEditDialog
            {...editDialogProps}
            datapoint={selectedDatapointItem}
            onSubmit={() => {
              context.datapoint.fetch([selectedDatapointItem.unitId]);
            }}
            terminalIds={context.datapoint.data?.map((datapoint) => datapoint.terminalId)}
          />
          <DeleteDialog
            {...deleteDialogProps}
            title={`${selectedDatapointItem.name}の削除`}
            isLoading={isDeleting}
            onSubmit={async () => {
              try {
                await deleteDatapoint({
                  urlParameter: { datapointId: selectedDatapointItem.id },
                });
                context.unit.selectedId && context.datapoint.fetch([context.unit.selectedId]);
              } catch (e) {
                return Promise.reject(e);
              }
            }}
          />
          <DatapointsAvailableDialog
            checkedItemIds={checkedItemIds}
            {...datapointsAvailableDialogProps}
            onSubmit={() => {
              context.datapoint.fetch([selectedDatapointItem.unitId]);
            }}
          />
        </>
      )}
    </>
  );
}

export { DatapointColumn };
