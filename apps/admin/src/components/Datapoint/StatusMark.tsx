import { Box } from '@mui/material';

export function StatusMarkDot(props: { isAvailable: boolean }) {
  return (
    <Box
      sx={(theme) => ({
        display: 'block',
        position: 'relative',
        width: '12px',
        height: '12px',
        borderRadius: '50%',
        backgroundColor: props.isAvailable ? theme.palette.success.light : theme.palette.grey[400],
      })}
    />
  );
}

export function StatusMark({ isAvailable = true }: { isAvailable: boolean }) {
  return (
    <Box sx={{ p: '0.2rem' }}>
      <StatusMarkDot isAvailable={isAvailable} />
    </Box>
  );
}
