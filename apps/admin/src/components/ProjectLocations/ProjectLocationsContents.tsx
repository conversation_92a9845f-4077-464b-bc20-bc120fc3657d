import { Alert, CircularProgress } from '@mui/material';
import { PropsWithChildren } from 'react';
import { ProjectLocationsEmpty } from './ProjectLocationsEmpty';
import { ProjectLocation } from '@/types';

function ProjectLocationContents({
  data,
  isLoading,
  error,
  children,
}: PropsWithChildren<{
  data: ProjectLocation[] | undefined;
  isLoading: boolean;
  error: any;
}>) {
  if (error) {
    return <Alert severity='error'>リストの取得に失敗しました。</Alert>;
  }

  if (isLoading) {
    return <CircularProgress />;
  }

  if (data?.length === 0) {
    return <ProjectLocationsEmpty />;
  }

  return <>{children}</>;
}

export { ProjectLocationContents };
