import { Loading<PERSON>utton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Paper,
  Box,
  Stack,
} from '@mui/material';
import { useState } from 'react';
import { MultiColumnView } from '../ColumnList/context/MultiColumViewContext';
import { LocationColumn } from '../Location/LocationColumn';
import { useUpsertProjectLocations } from '@/api';
import { UpsertProjectLocationsResponse } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';

function useProjectLocationsAddDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { ProjectLocationsAddDialog, props, open };
}

function ProjectLocationsAddDialog({
  openState,
  close,
  onSubmit,
  projectId,
}: {
  openState: boolean;
  close: () => void;
  onSubmit?: (projectLocations: UpsertProjectLocationsResponse) => void;
  projectId: number;
}) {
  const { trigger, isLoading } = useUpsertProjectLocations();

  const [checkedLocationIds, setCheckedLocationIds] = useState<number[]>([]);

  async function submit(locationIds: number[]) {
    if (locationIds.length > 0) {
      const response = await trigger({
        body: {
          locationIds: locationIds,
        },
        urlParameter: {
          projectId,
        },
      });

      if (response) {
        onSubmit?.(response);
        close();
      }
    }
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'sm'}>
      <DialogTitle
        component={Stack}
        direction={'row'}
        alignItems={'center'}
        justifyContent={'space-between'}
      >
        <Box>ロケーションを追加</Box>
        <DialogActions>
          <Button onClick={close}>キャンセル</Button>
          <LoadingButton
            disabled={checkedLocationIds.length === 0}
            loading={isLoading}
            variant={'contained'}
            onClick={() => {
              submit(checkedLocationIds);
            }}
            disableElevation
          >
            追加
          </LoadingButton>
        </DialogActions>
      </DialogTitle>
      <DialogContent>
        <Paper
          variant={'outlined'}
          sx={{
            height: '75vh',
          }}
        >
          <MultiColumnView>
            <LocationColumn
              checkboxSelection
              onSelectionModelChange={(locationIds) => {
                setCheckedLocationIds(locationIds);
              }}
            />
          </MultiColumnView>
        </Paper>
      </DialogContent>
    </EcoisDialog>
  );
}

export { useProjectLocationsAddDialog };
