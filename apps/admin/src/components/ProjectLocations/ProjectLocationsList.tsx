import { Delete } from '@mui/icons-material';
import {
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { useViewportTopToBottom } from '@/hooks';
import { Location, ProjectLocation } from '@/types';

const iconButtonVisibilityToggleStyle = {
  '& .MuiIconButton-root': {
    visibility: 'hidden',
  },
  '&:hover .MuiIconButton-root': {
    visibility: 'visible',
  },
};

function ProjectLocationsList({
  data,
  onDeleteClick,
}: {
  data: ProjectLocation[] | undefined;
  onDeleteClick?: (location: Location) => void;
}) {
  const height = useViewportTopToBottom('title');

  return (
    <Box
      component={Paper}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
        maxHeight: (theme) => `calc(100vh - ${height}px - ${theme.spacing(4)})`,
      }}
    >
      {/* <Box p={2}>
            <Typography variant='body2'>案件に紐づく案件リスト</Typography>
          </Box>
          <Divider /> */}
      <TableContainer
        sx={{
          flex: 1,
          overflow: 'auto',
        }}
      >
        <Table stickyHeader size='small'>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: '10%' }}>ID</TableCell>
              <TableCell>ロケーション名</TableCell>
              <TableCell>ロケーションアドレス</TableCell>
              <TableCell>Backlog課題</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.map(({ location }) => (
              <TableRow
                hover
                key={location.id}
                sx={{
                  textDecoration: 'none',
                  ...iconButtonVisibilityToggleStyle,
                }}
                onClick={() => {
                  //   navigate(`/location/${location.id}`);
                }}
              >
                <TableCell sx={{ width: '10%' }}>{location.id}</TableCell>
                <TableCell>{location.name}</TableCell>
                <TableCell>{location.address}</TableCell>
                <TableCell>{location.backlogIssueKey}</TableCell>
                <TableCell sx={{ width: '5%' }}>
                  <IconButton
                    onClick={() => {
                      onDeleteClick?.(location);
                    }}
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export { ProjectLocationsList };
