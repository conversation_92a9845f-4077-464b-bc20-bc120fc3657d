import { Link, Typography } from '@mui/material';
import { PropsWithChildren } from 'react';

type PageHeaderChildProps = {
  label: string;
  href: string;
};

function PageHeaderChild({ label, href, ...rest }: PropsWithChildren<PageHeaderChildProps>) {
  return (
    <>
      <Typography> / </Typography>
      <Link href={href} {...rest}>
        {label}
      </Link>
    </>
  );
}

export { PageHeaderChild };
