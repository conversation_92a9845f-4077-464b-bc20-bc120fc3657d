import { Box, <PERSON>, Typography, alpha } from '@mui/material';
import { Fragment } from 'react';

type PageHeaderProps = {
  contents: PageHeaderContent[];
  enableCaptionMode?: boolean;
};

type PageHeaderContent = {
  label: string;
  href?: string;
};

function PageHeader({ contents, enableCaptionMode }: PageHeaderProps) {
  return (
    <Box py={2} pl={0.8}>
      {contents.map(({ label, href }, index) => (
        <Fragment key={label}>
          {enableCaptionMode ? (
            <>
              {index === 0 && <PageTitleCaption href={href} label={label} />}
              {index !== 0 && (
                <PageTitleLink visibleSlashCond={index >= 2} href={href} label={label} />
              )}
            </>
          ) : (
            <>
              <PageTitleLink visibleSlashCond={index >= 1} href={href} label={label} />
            </>
          )}
        </Fragment>
      ))}
    </Box>
  );
}

function PageTitleCaption({ label, href }: { label: string; href: string | undefined }) {
  return (
    <Box
      px={1.5}
      py={0.2}
      mb={0.5}
      borderRadius={100}
      sx={(theme) => ({
        display: 'block',
        width: 'fit-content',
        backgroundColor: theme.palette.primary.light,
        textDecoration: 'none',
        ':hover': {
          cursor: href ? 'pointer' : 'default',
          backgroundColor: href ? alpha(theme.palette.primary.light, 0.8) : 'inherit',
        },
      })}
      href={href ?? '#'}
      component={Link}
    >
      <Typography fontSize={'0.75rem'} fontWeight={400} color={'white'}>
        {label}
      </Typography>
    </Box>
  );
}

function PageTitleLink({
  label,
  href,
  visibleSlashCond,
}: {
  label: string;
  href: string | undefined;
  visibleSlashCond?: boolean;
}) {
  return (
    <Typography variant='h5' component={'span'} fontWeight={700}>
      {visibleSlashCond && <> / </>}
      <Link
        sx={(theme) => ({
          ...(!href && { textDecoration: 'none', cursor: 'default' }),
          color: theme.palette.text.primary,
          ':not(:hover)': {
            textDecoration: 'none',
          },
        })}
        href={href ?? '#'}
      >
        {label}
      </Link>
    </Typography>
  );
}

export { PageHeader };
