import { Box, Button } from '@mui/material';
import { forwardRef } from 'react';
import { Editable, useEditableRow } from './Editable';
import { EditIconButton } from './EditIconButton';
import { Horizontal } from './Horizontal';
import { MonthForm, fieldObjectToMonthValues } from './MonthForm';
import { MonthValues } from './MonthValues';
import { RowItem } from './RowItem';
import { useReplaceReductionTargetData } from '@/api';
import { ReplaceReductionTargetDataResponse } from '@/api/types';
import { ReductionTargetData } from '@/types';

type ReductionTargetDataRowProps = {
  reductionTargetData: ReductionTargetData;
  onEditSuccess?: (response: ReplaceReductionTargetDataResponse) => void;
};

const ReductionTargetDataRow = forwardRef<Element, ReductionTargetDataRowProps>(
  function ReductionTargetDataRow({ reductionTargetData, onEditSuccess }, ref) {
    const { switchEditable, control } = useEditableRow();

    const { trigger } = useReplaceReductionTargetData();

    function handleEditBtnClick() {
      switchEditable();
    }

    function handleOnCancel() {
      switchEditable(false);
    }

    async function handleOnSubmit(data: { [month: string]: number | null }) {
      const values = fieldObjectToMonthValues(data);

      // if all values are null, delete the standard data
      const isDeleteRequest = values.every(({ value }) => value === null);

      try {
        const body = {
          utilityId: reductionTargetData.utilityId,
          contractTermId: reductionTargetData.contractTermId,
          values: isDeleteRequest ? null : values,
        };
        const response = await trigger({
          body,
        });
        if (response) {
          onEditSuccess?.(response);
        }

        switchEditable(false);
      } catch (error) {
        console.error(error);
      }
    }

    const { values } = reductionTargetData;

    return (
      <RowItem ref={ref}>
        <Editable control={control}>
          <Editable.ViewContent>
            {values !== null ? (
              <Horizontal controls={<EditIconButton onClick={handleEditBtnClick} />}>
                <MonthValues values={values} />
              </Horizontal>
            ) : (
              <Box>
                <Button onClick={handleEditBtnClick}>削減目標を追加</Button>
              </Box>
            )}
          </Editable.ViewContent>
          <Editable.EditContent>
            <MonthForm monthValues={values} onCancel={handleOnCancel} onSubmit={handleOnSubmit} />
          </Editable.EditContent>
        </Editable>
      </RowItem>
    );
  },
);

export { ReductionTargetDataRow };
