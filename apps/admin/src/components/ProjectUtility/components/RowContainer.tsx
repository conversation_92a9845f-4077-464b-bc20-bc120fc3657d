import { useTheme, Theme } from '@mui/material';
import { Stack, Box } from '@mui/material';
import { PropsWithChildren } from 'react';
import { RowTitle } from './RowTitle';
import { useDOMRect } from '@/hooks';
import { Utility } from '@/types';

type RowContainerProps = {
  utilities: Utility[];
  rect: ReturnType<typeof useRowContainer>['rect'];
};

// return height that return combination object of useDOMRect
function useRowContainer() {
  const [utilityRef, utilityRect] = useDOMRect();
  const [unitcostRef, unitcostRect] = useDOMRect();
  const [standardDataRef, standardDataRect] = useDOMRect();
  const [reductionTargetRef, reductionTargetRect] = useDOMRect();

  return {
    ref: {
      utilityRef,
      unitcostRef,
      standardDataRef,
      reductionTargetRef,
    },
    rect: {
      utilityRect,
      unitcostRect,
      standardDataRect,
      reductionTargetRect,
    },
  };
}

function RowContainer({ utilities, rect, children }: PropsWithChildren<RowContainerProps>) {
  const theme = useTheme() as Theme;

  const itemLength = utilities.length;

  const smallRowHeight = `minmax(${theme.spacing(10)} , max-content)` as const;

  return (
    <Stack direction={'row'}>
      <Box>
        <RowTitle title={'ユーティリティ'} height={`${rect.utilityRect.height}px`} />
        <RowTitle title={'単価'} height={`${rect.unitcostRect.height}px`} />
        <RowTitle
          sticky
          title={'基準値'}
          height={`${rect.standardDataRect.height}px`}
          alignItems={'flex-start'}
        />
        <RowTitle
          sticky
          title={'削減目標'}
          height={`${rect.reductionTargetRect.height}px`}
          alignItems={'flex-start'}
        />
      </Box>
      <Box
        flexGrow={1}
        sx={{
          display: 'grid',
          gridTemplateColumns: `repeat(${itemLength}, minmax(max-content, 1fr))`,
          gridTemplateRows: `${smallRowHeight} ${smallRowHeight} minmax(auto, max-content)  minmax(auto, max-content) minmax(auto, max-content)`,
          overflow: 'hidden',
          overflowX: 'auto',
        }}
      >
        {children}
      </Box>
    </Stack>
  );
}

export { RowContainer, useRowContainer };
