import { yupResolver } from '@hookform/resolvers/yup';
import { Error } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Stack,
  Box,
  Typography,
  TextField,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
} from '@mui/material';
import { SyntheticEvent, useState, Fragment, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { AnnualTotal } from './AnnualTotal';
import { MonthValue } from '@/types';

const monthFieldsArray = Array.from({ length: 12 }, (_, i) => ({
  [i + 1]: yup
    .number()
    .transform((value) => {
      return isNaN(value) ? null : value;
    })
    .nullable(),
}));

const monthFields = Object.assign({}, ...monthFieldsArray);
const monthSchema = yup.object().shape(monthFields);

function monthValue(month: number) {
  return {
    month: month,
    value: '',
  };
}

// generate 12 month values
function generateMonthValues() {
  return Array.from({ length: 12 }, (_, i) => monthValue(i + 1));
}

type EmptyMonthValue = MonthValue | { month: number; value: string };
function monthValueToFieldObject(monthValues: EmptyMonthValue[]): {
  [month: string]: number | null;
} {
  return Object.assign(
    {},
    ...monthValues.map((monthValue) => ({ [monthValue.month]: monthValue.value })),
  );
}

function fieldObjectToMonthValues(data: { [month: string]: number | null }) {
  return Object.keys(data).map((month) => ({
    month: parseInt(month, 10),
    value: data[month] === null ? null : data[month],
  }));
}

/*  


Component


*/

type MonthFormProps = {
  monthValues: MonthValue[] | null;
  onCancel: (event?: SyntheticEvent) => void;
  onSubmit: (data: { [month: string]: number | null }) => Promise<void> | void;
};
function MonthForm({ monthValues, onCancel, onSubmit }: MonthFormProps) {
  const valuesForRender = monthValues === null ? generateMonthValues() : monthValues;
  const [total, setTotal] = useState<number>(0);

  const {
    control,
    getValues,
    formState: { errors, isValid },
    handleSubmit,
    setError,
    watch,
    reset: formReset,
  } = useForm<{ [month: string]: number | null }>({
    resolver: yupResolver(monthSchema),
    mode: 'onChange',
    defaultValues: monthValueToFieldObject(valuesForRender),
  });

  const formValues = watch();

  // Iterate formValues and set error if any value is '' but if every value is '', don't set error
  useEffect(() => {
    const isEveryValueNull = Object.values(formValues).every((value) => String(value) === '');
    if (!isEveryValueNull) {
      Object.entries(formValues).forEach(([month, value]) => {
        if (String(value) === '') {
          setError(month, {
            type: 'manual',
            message: '入力してください',
          });
        }
      });
    }
  }, [formValues, setError]);

  useEffect(() => {
    const calculatedTotal = Object.values(formValues).reduce((sum: number, value) => {
      const numberValue = Number(value);
      return isNaN(numberValue) ? sum : sum + numberValue;
    }, 0);
    setTotal(calculatedTotal);
  }, [formValues]); // formValues が変更されるたびに合計を再計算

  const [disable, setDisable] = useState<boolean>(false);
  function switchDisable() {
    setDisable((prev) => !prev);
  }

  const [tempFormValues, setTempFormValues] = useState<{ [month: string]: number | null }>(
    monthValueToFieldObject(valuesForRender),
  );

  function handleCheckBoxClick() {
    if (disable) {
      // true -> false
      formReset(tempFormValues);
      switchDisable();
    } else {
      setTempFormValues(getValues());
      clearAll();
      switchDisable();
    }
  }

  function clearAll() {
    const monthVoidArray = Array.from({ length: 12 }).map((_, i) => ({
      month: i + 1,
      value: '',
    }));
    const monthVoidObject = monthValueToFieldObject(monthVoidArray);
    formReset(monthVoidObject);
  }

  function handleOnCancelBtnClick(event: SyntheticEvent) {
    onCancel(event);
  }

  const [isLoading, setIsLoading] = useState<boolean>(false);

  function handleOnSubmitBtnClick() {
    return handleSubmit(async (data) => {
      setIsLoading(true);
      await onSubmit(data);
      setIsLoading(false);
    });
  }

  return (
    <>
      <Stack direction={'column'} spacing={1.5}>
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: 'max-content max-content',
            rowGap: 0.5,
            alignItems: 'center',
            '& .MuiTextField-root': { width: '10ch' },
          }}
        >
          {valuesForRender.map(({ month }) => {
            return (
              <Fragment key={month}>
                <Box>
                  <Typography variant='body2' mr={1}>
                    {month}月
                  </Typography>
                </Box>
                <Stack direction={'row'} alignItems={'center'}>
                  <Controller
                    control={control}
                    name={`${month}`}
                    render={({ field }) => (
                      <TextField
                        sx={(theme) => ({
                          '& .MuiInputBase-input': {
                            fontSize: theme.typography.body2.fontSize,
                            height: theme.spacing(1),
                            padding: `${theme.spacing(1.25)} ${theme.spacing(1)}`,
                            ...(disable && {
                              backgroundColor: theme.palette.grey[100],
                            }),
                          },
                        })}
                        // eslint-disable-next-line jsx-a11y/no-autofocus
                        autoFocus={month === 1}
                        {...field}
                        disabled={disable || isLoading}
                        type='number'
                        size={'small'}
                        error={month.toString() in errors}
                        // helperText={'value' in errors ? errors.value?.message : ''}
                      />
                    )}
                  />
                  {month.toString() in errors && <Error color='error' />}
                </Stack>
              </Fragment>
            );
          })}
        </Box>
        <Stack>
          <Divider />
          <AnnualTotal total={total} />
        </Stack>

        <Stack spacing={1}>
          <Box textAlign={'center'}>
            <FormControlLabel
              control={<Checkbox value={disable} size='small' onClick={handleCheckBoxClick} />}
              label='設定しない'
            />
          </Box>
          <Stack direction={'row'} spacing={1}>
            <Button size='small' variant={'outlined'} onClick={handleOnCancelBtnClick}>
              キャンセル
            </Button>
            <LoadingButton
              size='small'
              variant={'contained'}
              disableElevation
              onClick={handleOnSubmitBtnClick()}
              loading={isLoading}
              disabled={(isLoading || !disable) && (isLoading || !isValid)}
            >
              確定
            </LoadingButton>
          </Stack>
        </Stack>
      </Stack>
    </>
  );
}

export { MonthForm, fieldObjectToMonthValues };
