import { Theme, Typography, useTheme } from '@mui/material';
import { RowItem } from './RowItem';

type RowTitleProps = {
  title: string;
  height?: string;
  sticky?: boolean;
  alignItems?: 'flex-start' | 'center' | 'flex-end'; // default: center
};
function RowTitle({ title, height, sticky, alignItems = 'center' }: RowTitleProps) {
  const theme = useTheme() as Theme;

  return (
    <RowItem
      sx={{
        borderRight: `1px solid ${theme.palette.divider}`,
        height,
        position: 'relative',
        display: 'flex',
        alignItems,
      }}
    >
      <Typography
        sx={(theme) =>
          sticky
            ? {
                position: 'sticky',
                top: theme.spacing(10),
                height: 'min-content',
              }
            : {}
        }
        variant={'body2'}
        noWrap
      >
        {title}
      </Typography>
    </RowItem>
  );
}

export { RowTitle };
