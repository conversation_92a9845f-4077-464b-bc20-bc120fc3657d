import { SyntheticEvent, forwardRef } from 'react';
import { UtilityRow } from './UtilityRow';
import { Utility } from '@/types';

type UtilityRowsProps = {
  utilities: Utility[];
  onClick?: (utility: Utility) => void;
};

const UtilityRows = forwardRef<Element, UtilityRowsProps>(function UtilityRows(
  { utilities, onClick },
  ref,
) {
  function handleOnClick(utility: Utility) {
    return (event: SyntheticEvent) => {
      event.stopPropagation();
      onClick?.(utility);
    };
  }

  return (
    <>
      {utilities.map((utility) => {
        return (
          <UtilityRow
            ref={ref}
            key={utility.id}
            utility={utility}
            onClick={handleOnClick(utility)}
          />
        );
      })}
    </>
  );
});

export { UtilityRows };
