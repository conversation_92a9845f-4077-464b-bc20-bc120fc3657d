import { Box, Typography, Stack, Divider } from '@mui/material';
import { Fragment, useMemo } from 'react';
import { AnnualTotal } from './AnnualTotal';
import { MonthValue } from '@/types';

type MonthValuesProps = {
  values: MonthValue[];
};

function MonthValues({ values }: MonthValuesProps) {
  // 合計値を計算
  const total = useMemo(() => {
    return values.reduce((sum, { value }) => sum + (value || 0), 0); // value が null の場合は 0 を加算
  }, [values]);

  return (
    <>
      <Stack direction='column' gap={1}>
        <Box
          sx={{
            display: 'grid',
            rowGap: 0.8,
            columnGap: 1,
            gridTemplateColumns: 'max-content max-content',
          }}
        >
          {values.map(({ value, month }) => {
            return (
              <Fragment key={month}>
                <Typography variant='body2' textAlign={'center'}>
                  {month}月
                </Typography>
                <Typography variant='body2' textAlign={'right'}>
                  {value?.toLocaleString() ?? '計算中'}
                </Typography>
              </Fragment>
            );
          })}
        </Box>
        <Divider />
        <AnnualTotal total={total} />
      </Stack>
    </>
  );
}

export { MonthValues };
