import { forwardRef } from 'react';
import { UnitcostRow } from './UnitcostRow';
import { ReplaceUnitcostResponse } from '@/api/types';
import { ContractTerm, UnitcostWithKey } from '@/types';

type UnitcostRowsProps = {
  unitcosts: UnitcostWithKey[];
  contractTermId: ContractTerm['id'];
  onEditSuccess?: (response: ReplaceUnitcostResponse) => void;
};

const UnitcostRows = forwardRef<Element, UnitcostRowsProps>(function UnitcostRows(
  { unitcosts, contractTermId, onEditSuccess },
  ref,
) {
  return (
    <>
      {unitcosts.map((unitcost) => {
        if (unitcost.contractTermId !== contractTermId) return null;
        return (
          <UnitcostRow
            ref={ref}
            key={unitcost.key}
            unitcost={unitcost}
            onEditSuccess={onEditSuccess}
          />
        );
      })}
    </>
  );
});

export { UnitcostRows };
