import { Box, Button } from '@mui/material';
import { forwardRef } from 'react';
import { Editable, useEditableRow } from './Editable';
import { EditIconButton } from './EditIconButton';
import { Horizontal } from './Horizontal';
import { fieldObjectToMonthValues, MonthForm } from './MonthForm';
import { MonthValues } from './MonthValues';
import { RowItem } from './RowItem';
import { useReplaceStandardData } from '@/api';
import { ReplaceStandardDataResponse } from '@/api/types';
import { StandardData } from '@/types';

type StandardDataRowProps = {
  standardData: StandardData;
  onEditSuccess?: (response: ReplaceStandardDataResponse) => void;
};

const StandardDataRow = forwardRef<Element, StandardDataRowProps>(function StandardDataRow(
  { standardData, onEditSuccess },
  ref,
) {
  const { switchEditable, control } = useEditableRow();

  const { trigger } = useReplaceStandardData();

  function handleEditBtnClick() {
    switchEditable();
  }

  function handleOnCancel() {
    switchEditable(false);
  }

  async function handleOnSubmit(data: { [month: string]: number | null }) {
    const values = fieldObjectToMonthValues(data);
    // const values: MonthValue[] = data.map((val) => {});

    // if all values are null, delete the standard data
    const isDeleteRequest = values.every(({ value }) => value === null);

    const body = {
      utilityId: standardData.utilityId,
      contractTermId: standardData.contractTermId,
      values: isDeleteRequest ? null : values,
    };

    try {
      const response = await trigger({
        body,
      });
      if (response) {
        onEditSuccess?.(response);
      }
      switchEditable(false);
    } catch (error) {
      console.error(error);
    }
  }

  const { values } = standardData;

  return (
    <RowItem ref={ref}>
      <Editable control={control}>
        <Editable.ViewContent>
          {values !== null ? (
            <Horizontal controls={<EditIconButton onClick={handleEditBtnClick} />}>
              <MonthValues values={values} />
            </Horizontal>
          ) : (
            <Box>
              <Button onClick={handleEditBtnClick}>基準値を追加</Button>
            </Box>
          )}
        </Editable.ViewContent>
        <Editable.EditContent>
          <MonthForm monthValues={values} onCancel={handleOnCancel} onSubmit={handleOnSubmit} />
        </Editable.EditContent>
      </Editable>
    </RowItem>
  );
});

export { StandardDataRow };
