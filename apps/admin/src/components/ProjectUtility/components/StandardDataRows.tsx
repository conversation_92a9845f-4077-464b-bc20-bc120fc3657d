import { forwardRef } from 'react';
import { StandardDataRow } from './StandardDataRow';
import { ReplaceStandardDataResponse } from '@/api/types';
import { ContractTerm, StandardDataWithKey } from '@/types';

type StandardDataRowsProps = {
  standardData: StandardDataWithKey[];
  contractTermId: ContractTerm['id'];
  onEditSuccess?: (response: ReplaceStandardDataResponse) => void;
};

const StandardDataRows = forwardRef<Element, StandardDataRowsProps>(function StandardDataRows(
  { standardData, contractTermId, onEditSuccess },
  ref,
) {
  return (
    <>
      {standardData.map((standardData) => {
        if (standardData.contractTermId !== contractTermId) return null;
        return (
          <StandardDataRow
            ref={ref}
            key={standardData.key}
            standardData={standardData}
            onEditSuccess={onEditSuccess}
          />
        );
      })}
    </>
  );
});

export { StandardDataRows };
