import { PropsWithChildren, useState, createContext, useContext } from 'react';

const EditableContext = createContext<{
  isEditable: boolean;
  switchEditable: (state?: boolean) => void;
} | null>(null);

function useEditableRow() {
  const [isEditable, setIsEditable] = useState(false);

  function switchEditable(state?: boolean) {
    if (state) {
      setIsEditable(state);
      return;
    }
    setIsEditable((state) => !state);
  }

  const control = {
    isEditable,
    switchEditable,
  };

  return {
    control,
    switchEditable,
  };
}

function Editable({
  children,
  control,
}: PropsWithChildren<{
  control: {
    isEditable: boolean;
    switchEditable: (state?: boolean) => void;
  };
}>) {
  const { isEditable, switchEditable } = control;

  return (
    <>
      <EditableContext.Provider value={{ isEditable, switchEditable }}>
        {children}
      </EditableContext.Provider>
    </>
  );
}

function ViewContent({ children }: PropsWithChildren) {
  const context = useContext(EditableContext);
  const { isEditable } = context ?? { isEditable: false };
  return <>{!isEditable && children}</>;
}

function EditContent({ children }: PropsWithChildren) {
  const context = useContext(EditableContext);
  const { isEditable } = context ?? { isEditable: false };
  return <>{isEditable && children}</>;
}

Editable.ViewContent = ViewContent;
Editable.EditContent = EditContent;

export { useEditableRow, Editable };
