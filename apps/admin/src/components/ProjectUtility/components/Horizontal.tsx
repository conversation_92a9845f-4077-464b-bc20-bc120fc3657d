import { Stack, Box, Theme, useTheme } from '@mui/material';
import { PropsWithChildren, ReactNode } from 'react';

function Horizontal({ children, controls }: PropsWithChildren<{ controls?: ReactNode }>) {
  const theme = useTheme() as Theme;

  return (
    <Stack direction={'row'} spacing={1} alignItems={'center'}>
      {children}
      <Box minWidth={theme.spacing(4)}>{controls}</Box>
    </Stack>
  );
}

export { Horizontal };
