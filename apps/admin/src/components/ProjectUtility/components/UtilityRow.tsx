import { SyntheticEvent, forwardRef } from 'react';
import { EditIconButton } from './EditIconButton';
import { Horizontal } from './Horizontal';
import { RowItem } from './RowItem';
import { RowNormalValue } from './RowNormalValue';
import { Utility } from '@/types';

type UtilityRowProps = {
  utility: Utility;
  onClick?: (event: SyntheticEvent) => void;
};

const UtilityRow = forwardRef<Element, UtilityRowProps>(function UtilityRow(
  { utility, onClick },
  ref,
) {
  function handleOnClick(event: SyntheticEvent) {
    onClick?.(event);
  }

  return (
    <RowItem key={utility.id} ref={ref}>
      <Horizontal controls={<EditIconButton onClick={handleOnClick} />}>
        <RowNormalValue value={utility.name} />
      </Horizontal>
    </RowItem>
  );
});

export { UtilityRow };
