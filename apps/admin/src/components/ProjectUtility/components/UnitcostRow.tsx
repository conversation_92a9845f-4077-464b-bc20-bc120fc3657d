import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Stack,
  Button,
  TextField,
  InputAdornment,
  Box,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { forwardRef, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { Editable, useEditableRow } from './Editable';
import { EditIconButton } from './EditIconButton';
import { Horizontal } from './Horizontal';
import { RowItem } from './RowItem';
import { RowNormalValue } from './RowNormalValue';
import { Vertical } from './Vertical';
import { useReplaceUnitcosts } from '@/api';
import { ReplaceUnitcostResponse } from '@/api/types';
import { Unitcost } from '@/types';

type UnitcostRowProps = {
  unitcost: Unitcost;
  onEditSuccess?: (response: ReplaceUnitcostResponse) => void;
};

const UnitcostRow = forwardRef<Element, UnitcostRowProps>(function UnitcostRow(
  { unitcost, onEditSuccess },
  ref,
) {
  const { switchEditable, control } = useEditableRow();

  function handleEditBtnClick() {
    switchEditable();
  }

  const { trigger: replaceUnitcost, isLoading } = useReplaceUnitcosts();

  const {
    control: fromControl,
    formState: { errors },
    watch,
    handleSubmit,
    getValues,
    reset: formReset,
  } = useForm({
    resolver: yupResolver(
      yup.object().shape({
        value: yup
          .number()
          .transform((value) => {
            return isNaN(value) ? null : value;
          })
          .nullable(),
      }),
    ),
    defaultValues: {
      value: unitcost.value ?? '', // '' means null
    },
  });

  const [disable, setDisable] = useState(false);
  function switchDisable() {
    setDisable((prev) => !prev);
  }

  const [tempFormValue, setTempFormValue] = useState<{
    value: string | number;
  }>();

  function handleCheckboxChange() {
    if (disable) {
      // ☑ -> ☐
      formReset(tempFormValue);
      setTempFormValue({ value: '' });
    } else {
      // ☐ -> ☑
      setTempFormValue(getValues());
      formReset({ value: '' });
    }
    switchDisable();
  }

  // if value is '', it means null
  const isEmpty = watch('value') === '';

  function handleOnSubmit() {
    return handleSubmit(async (data) => {
      if (typeof data.value === 'string') return; // avoid initial value ''

      try {
        const response = await replaceUnitcost({
          body: {
            utilityId: unitcost.utilityId,
            contractTermId: unitcost.contractTermId,
            value: data.value,
          },
        });
        if (response) {
          reset();
          onEditSuccess?.(response);
        }
      } catch (error) {
        console.error(error);
      }
    });
  }

  function reset() {
    switchEditable(false);
    formReset();
  }

  return (
    <RowItem
      ref={ref}
      key={unitcost.utilityId}
      sx={{
        '& .MuiTextField-root': { width: '15ch' },
      }}
    >
      <Editable control={control}>
        <Editable.ViewContent>
          {unitcost.value !== null ? (
            <Horizontal controls={<EditIconButton onClick={handleEditBtnClick} />}>
              <RowNormalValue value={`¥${unitcost.value}`} />
            </Horizontal>
          ) : (
            <Button onClick={handleEditBtnClick}>単価を追加</Button>
          )}
        </Editable.ViewContent>
        <Editable.EditContent>
          <Vertical
            controls={
              <Stack direction={'row'} spacing={1}>
                <Button size={'small'} variant={'outlined'} onClick={handleEditBtnClick}>
                  キャンセル
                </Button>
                <LoadingButton
                  size={'small'}
                  variant={'contained'}
                  disableElevation
                  onClick={handleOnSubmit()}
                  disabled={(isLoading || !disable) && (isLoading || isEmpty)}
                  loading={isLoading}
                >
                  確定
                </LoadingButton>
              </Stack>
            }
          >
            <Stack spacing={1}>
              <Controller
                control={fromControl}
                name='value'
                render={({ field }) => (
                  <TextField
                    {...field}
                    // eslint-disable-next-line jsx-a11y/no-autofocus
                    autoFocus
                    disabled={isLoading || disable}
                    sx={(theme) => ({
                      ...(disable && {
                        '& .MuiInputBase-root': {
                          backgroundColor: theme.palette.grey[100],
                        },
                      }),
                    })}
                    type='number'
                    InputProps={{
                      startAdornment: <InputAdornment position='start'>¥</InputAdornment>,
                    }}
                    size={'small'}
                    error={'value' in errors}
                    helperText={'value' in errors ? errors.value?.message : ''}
                  />
                )}
              />
              <Box>
                <FormControlLabel
                  control={<Checkbox value={disable} size='small' />}
                  label='設定しない'
                  onChange={handleCheckboxChange}
                  componentsProps={{
                    typography: {
                      variant: 'body2',
                    },
                  }}
                />
              </Box>
            </Stack>
          </Vertical>
        </Editable.EditContent>
      </Editable>
    </RowItem>
  );
});

export { UnitcostRow };
