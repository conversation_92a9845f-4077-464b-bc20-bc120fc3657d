import { forwardRef } from 'react';
import { ReductionTargetDataRow } from './ReductionTargetDataRow';
import { ReplaceReductionTargetDataResponse } from '@/api/types';
import { ContractTerm, ReductionTargetDataWithKey } from '@/types';

type ReductionTargetDataRowsProps = {
  reductionTargetData: ReductionTargetDataWithKey[];
  contractTermId: ContractTerm['id'];
  onEditSuccess?: (response: ReplaceReductionTargetDataResponse) => void;
};

const ReductionTargetDataRows = forwardRef<Element, ReductionTargetDataRowsProps>(
  function ReductionTargetDataTows({ reductionTargetData, contractTermId, onEditSuccess }, ref) {
    return (
      <>
        {reductionTargetData.map((reductionTargetData) => {
          if (reductionTargetData.contractTermId !== contractTermId) return null;
          const { key } = reductionTargetData;
          return (
            <ReductionTargetDataRow
              ref={ref}
              key={key}
              reductionTargetData={reductionTargetData}
              onEditSuccess={onEditSuccess}
            />
          );
        })}
      </>
    );
  },
);

export { ReductionTargetDataRows };
