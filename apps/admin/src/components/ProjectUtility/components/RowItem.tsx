import { BoxProps, Box } from '@mui/material';
import { PropsWithChildren, forwardRef } from 'react';

const RowItem = forwardRef<Element, PropsWithChildren<BoxProps>>(function RowItem(
  { children, ...props },
  ref,
) {
  return (
    <Box
      {...props}
      ref={ref}
      px={2}
      py={props.py ?? 3}
      sx={{
        display: 'flex',
        borderBottom: 1,
        borderColor: 'divider',
        transition: 'width 1s ease-in-out',
        '&:hover': {
          '.editButton': {
            visibility: 'visible',
          },
        },
        ...props.sx,
      }}
    >
      {children}
    </Box>
  );
});

export { RowItem };
