import { Edit } from '@mui/icons-material';
import { IconButton } from '@mui/material';
import { SyntheticEvent } from 'react';

function EditIconButton({ onClick }: { onClick: (event: SyntheticEvent) => void }) {
  return (
    <IconButton size='small' onClick={onClick} className='editButton' sx={{ visibility: 'hidden' }}>
      <Edit fontSize='small' />
    </IconButton>
  );
}

export { EditIconButton };
