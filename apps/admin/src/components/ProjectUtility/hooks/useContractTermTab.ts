import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

function useContractTermTab() {
  const empty = 'empty' as const;
  const [searchParams, setSearchParams] = useSearchParams();

  const [tabValue, setTabValue] = useState(searchParams.get('tab') ?? empty); // TabContextのvalue

  function handleTabChange(tabId: string) {
    setSearchParams({ tab: tabId }, { replace: true });
    setTabValue(tabId);
  }

  function reset() {
    searchParams.delete('tab');
    setSearchParams(searchParams);
    setTabValue(empty);
  }

  return { tabValue, handleTabChange, reset } as const;
}

export { useContractTermTab };
