import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';
import { useEffect } from 'react';
import { UtilityForm, useUtilityForm } from './UtilityForm';
import { useUpdateUtility } from '@/api';
import { UpdateUtilityResponse } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { Utility } from '@/types';

function useEditUtilityDialog() {
  const { openState, close, open, context } = useDialogState<Utility>();

  const props = {
    openState,
    close,
    context,
  };

  return {
    EditUtilityDialog,
    props,
    open,
  };
}

type EditUtilityDialogProps = {
  context: Utility | undefined;
  onSuccess: (response: UpdateUtilityResponse) => void;
} & HooksDialogProps;
function EditUtilityDialog({
  context: utility,
  onSuccess,
  close,
  openState,
}: EditUtilityDialogProps) {
  const { trigger: updateRequest, isLoading } = useUpdateUtility();

  const energyTypeId = utility?.energyType?.id ? String(utility?.energyType.id) : 'default';

  const {
    control,
    formState: { errors, isValid, isDirty },
    handleSubmit,
    reset: formReset,
  } = useUtilityForm({
    defaultValues: {
      name: utility?.name ?? '',
      energyTypeId,
    },
  });

  function handleCancel() {
    formReset();
    close();
  }

  useEffect(() => {
    formReset({
      name: utility?.name ?? '',
      energyTypeId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [utility]);

  function handleOnFormSubmit() {
    return handleSubmit(async (data) => {
      if (utility === undefined) return;
      try {
        const response = await updateRequest({
          urlParameter: {
            utilityId: utility.id,
          },
          body: {
            name: data.name,
            energyTypeId: Number(data.energyTypeId),
          },
        });
        if (response) {
          onSuccess?.(response);
          close();
        }
      } catch (e) {
        return Promise.reject(e);
      }
    });
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>ユーティリティを編集</DialogTitle>
      <DialogContent>
        <DialogContentText>編集したい項目の値を変更してください</DialogContentText>
        <UtilityForm control={control} errors={errors} />
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          loading={isLoading}
          variant='contained'
          disabled={!isValid || !isDirty}
          onClick={handleOnFormSubmit()}
        >
          変更
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useEditUtilityDialog };
