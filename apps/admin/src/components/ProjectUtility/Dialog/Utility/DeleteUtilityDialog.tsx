import { Box, Paper, Typography } from '@mui/material';
import { useDeleteUtility } from '@/api';
import { DeleteUtilityResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { Utility } from '@/types';

function useDeleteUtilityDialog() {
  const { context, open, openState, close } = useDialogState<Utility>();

  const props = {
    context,
    openState,
    close,
  };

  return { DeleteUtilityDialog, props, open };
}

type DeleteUtilityDialogProps = {
  context: Utility | undefined;
  onSuccess: (response: DeleteUtilityResponse) => void;
} & HooksDialogProps;
function DeleteUtilityDialog({ context: utility, onSuccess, ...props }: DeleteUtilityDialogProps) {
  const { trigger: deleteRequest, isLoading } = useDeleteUtility();

  async function handleSubmit() {
    if (utility === undefined) {
      return Promise.reject('Project is undefined');
    }
    try {
      const response = await deleteRequest({
        urlParameter: {
          utilityId: utility.id,
        },
      });
      if (response) {
        onSuccess?.(response);
      }
    } catch (e) {
      return Promise.reject(e);
    }
  }

  return (
    <DeleteDialog
      {...props}
      title={`ユーティリティの削除`}
      confirmText='ユーティリティに紐づく、基準値、単価、削減目標値も削除されます'
      isLoading={isLoading}
      onSubmit={handleSubmit}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象のユーティリティ</Typography>
            <Typography variant='body1'>{utility?.name}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteUtilityDialog };
