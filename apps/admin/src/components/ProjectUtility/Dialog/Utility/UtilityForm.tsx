import { yupResolver } from '@hookform/resolvers/yup';
import { Stack, TextField } from '@mui/material';
import { Control, Controller, FieldErrors, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { EnergyTypeSelect } from '@/components/EnergyType/EnergyTypeSelect';

const schema = yup.object().shape({
  name: yup.string().required('ユーティリティの名称は必須です'),
  energyTypeId: yup.string().required('エネルギータイプは必須です'),
});

type UseUtilityFormProps = {
  defaultValues?: {
    name: string;
    energyTypeId: string;
  };
};

function useUtilityForm({ defaultValues }: UseUtilityFormProps = {}) {
  const form = useForm({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      name: defaultValues?.name ? defaultValues.name : '',
      energyTypeId: defaultValues?.energyTypeId ? defaultValues.energyTypeId : 'default',
    },
  });

  return {
    ...form,
  };
}

type UtilityFormProps = {
  control: Control<
    {
      name: string;
      energyTypeId: string;
    },
    any
  >;
  errors: FieldErrors<{
    name: string;
  }>;
};
function UtilityForm({ control, errors }: UtilityFormProps) {
  return (
    <Stack pt={2} gap={3}>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            id='utility-name'
            label='ユーティリティ名(必須)'
            variant='outlined'
            error={'name' in errors}
            helperText={'name' in errors ? errors.name?.message : ''}
          />
        )}
      />
      <Controller
        name='energyTypeId'
        control={control}
        render={({ field }) => (
          <EnergyTypeSelect id='energyTypeLabel' {...field} showColor={true} />
        )}
      />
    </Stack>
  );
}
export { UtilityForm, useUtilityForm };
