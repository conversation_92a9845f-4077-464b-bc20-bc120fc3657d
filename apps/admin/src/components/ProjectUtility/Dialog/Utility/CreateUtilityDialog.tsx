import { Loading<PERSON>utton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';
import { UtilityForm, useUtilityForm } from './UtilityForm';
import { useCreateUtility } from '@/api';
import { CreateUtilityResponse } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { Project } from '@/types';

function useCreateUtilityDialog() {
  const { openState, close, open } = useDialogState();

  const props = {
    openState,
    close,
  };

  return {
    CreateUtilityDialog,
    props,
    open,
  };
}

type CreateUtilityDialogProps = {
  projectId: Project['id'];
  onSuccess: (response: CreateUtilityResponse) => void;
} & HooksDialogProps;
function CreateUtilityDialog({ projectId, onSuccess, close, openState }: CreateUtilityDialogProps) {
  const { trigger: createRequestTrigger, isLoading } = useCreateUtility();

  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    reset: formReset,
  } = useUtilityForm();

  function handleCancel() {
    formReset();
    close();
  }

  function handleOnFormSubmit() {
    return handleSubmit(async (data) => {
      try {
        const response = await createRequestTrigger({
          body: {
            projectId: projectId,
            name: data.name,
            energyTypeId: Number(data.energyTypeId),
          },
        });
        if (response) {
          onSuccess?.(response);
          close();
        }
      } catch (e) {
        return Promise.reject(e);
      }
    });
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>ユーティリティを追加</DialogTitle>
      <DialogContent>
        <DialogContentText>項目の値を入力してください</DialogContentText>
        <UtilityForm control={control} errors={errors} />
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          loading={isLoading}
          variant='contained'
          disabled={!isValid}
          onClick={handleOnFormSubmit()}
        >
          作成
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useCreateUtilityDialog };
