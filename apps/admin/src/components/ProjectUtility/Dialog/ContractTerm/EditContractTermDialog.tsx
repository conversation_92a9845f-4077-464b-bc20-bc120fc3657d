import { Loading<PERSON>utton } from '@mui/lab';
import {
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/ja';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { ContractTermsForm } from './ContractTermsForm';
import { formatDateForRequest } from './formatDateForRequest';
import { useContractTermsForm } from './useContractTermsForm';
import { useUpdateContractTerm } from '@/api';
import { UpdateContractTermResponse } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ContractTerm } from '@/types';

function useEditContractTermDialog() {
  const { openState, open: openDialog, close: dialogClose } = useDialogState();

  const [contractTerm, setContractTerm] = useState<ContractTerm | undefined>(undefined);

  function open(contractTerm: ContractTerm) {
    openDialog();
    setContractTerm(contractTerm);
  }

  function close() {
    dialogClose();
    setContractTerm(undefined);
  }

  const props = {
    openState,
    close,
    contractTerm,
  };

  return {
    EditContractTermDialog,
    props,
    open,
  };
}

type EditContractTermDialogProps = {
  contractTerm: ContractTerm | undefined;
  onSuccess?: (response: UpdateContractTermResponse) => void;
} & HooksDialogProps;

function EditContractTermDialog({
  contractTerm,
  close,
  openState,
  ...props
}: EditContractTermDialogProps) {
  const { trigger: updateRequestTrigger, isLoading, reset: requestReset } = useUpdateContractTerm();

  const {
    control,
    formState: { errors, isValid, isDirty },
    watchStartAt,
    handleFormChange,
    reset: formReset,
    handleSubmit,
  } = useContractTermsForm({
    defaultValues: {
      startAt: dayjs(contractTerm?.startAt) ?? '',
      endAt: dayjs(contractTerm?.endAt) ?? '',
    },
  });

  function handleCancel() {
    dialogClose();
  }

  function resetDialog() {
    formReset();
    requestReset();
  }

  function dialogClose() {
    resetDialog();
    close();
  }

  useEffect(() => {
    formReset({
      startAt: dayjs(contractTerm?.startAt) ?? '',
      endAt: dayjs(contractTerm?.endAt) ?? '',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contractTerm]);

  function handleOnFormSubmit() {
    return handleSubmit(async (data) => {
      if (!contractTerm) return;
      try {
        const { startAt, endAt } = {
          startAt: formatDateForRequest(data.startAt),
          endAt: formatDateForRequest(data.endAt),
        };

        const body = {
          startAt,
          endAt,
        };

        const response = await updateRequestTrigger({
          body,
          urlParameter: { contractTermId: contractTerm.id },
        });
        if (response) {
          dialogClose();
          props.onSuccess?.(response);
        }
      } catch (error) {
        console.error(error);
      }
    });
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={'ja'}>
        <DialogTitle>契約期間を編集</DialogTitle>
        <DialogContent>
          <DialogContentText>開始・終了時点を指定してください</DialogContentText>
          <ContractTermsForm
            control={control}
            errors={errors}
            onFormChange={handleFormChange}
            watchStartAt={watchStartAt}
          />
        </DialogContent>
        <DialogActions>
          <Button variant='outlined' onClick={handleCancel}>
            キャンセル
          </Button>
          <LoadingButton
            loading={isLoading}
            variant='contained'
            disabled={!isValid || !isDirty}
            onClick={handleOnFormSubmit()}
          >
            編集
          </LoadingButton>
        </DialogActions>
      </LocalizationProvider>
    </EcoisDialog>
  );
}
export { useEditContractTermDialog };
