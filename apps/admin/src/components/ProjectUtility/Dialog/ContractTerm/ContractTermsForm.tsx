import { Stack, Box, Typography } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import { Control, Controller, FieldErrors } from 'react-hook-form';

type ContractTermsFormProps = {
  control: Control<
    {
      startAt: string | Dayjs;
      endAt: string | Dayjs;
    },
    any
  >;
  errors: FieldErrors<{
    startAt: string;
    endAt: string;
  }>;
  watchStartAt: string | Dayjs;
  onFormChange: (name: string) => void;
};

function ContractTermsForm({
  control,
  errors,
  watchStartAt,
  onFormChange,
}: ContractTermsFormProps) {
  return (
    <Stack py={2} spacing={2} direction={'row'} alignItems={'stretch'}>
      <Controller
        control={control}
        name='startAt'
        render={({ field }) => (
          <DatePicker
            {...field}
            onChange={(event) => {
              field.onChange(event);
              onFormChange(field.name);
            }}
            label='開始(必須)'
            views={['year', 'month']}
            format='YYYY/MM'
            openTo={'year'}
            minDate={dayjs('2000-01-01')}
            maxDate={dayjs('2249-12-31')}
            slotProps={{
              textField: {
                error: !!errors.startAt,
                helperText: errors.startAt?.message,
              },
            }}
          />
        )}
      />
      <Box alignSelf={'center'}>
        <Typography>〜</Typography>
      </Box>
      <Controller
        control={control}
        name='endAt'
        render={({ field }) => (
          <DatePicker
            {...field}
            onChange={(event) => {
              field.onChange(event);
              onFormChange(field.name);
            }}
            minDate={watchStartAt}
            maxDate={dayjs('2249-12-31')}
            label='終了(必須)'
            views={['year', 'month']}
            format='YYYY/MM'
            openTo={'year'}
            slotProps={{
              textField: {
                error: !!errors.endAt,
                helperText: errors.endAt?.message,
              },
            }}
          />
        )}
      />
    </Stack>
  );
}
export { ContractTermsForm };
