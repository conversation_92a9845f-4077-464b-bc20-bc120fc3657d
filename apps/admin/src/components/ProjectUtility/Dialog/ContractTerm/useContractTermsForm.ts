import { yupResolver } from '@hookform/resolvers/yup';
import dayjs, { Dayjs } from 'dayjs';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

// バリデーションルール
// - 開始日は必須
// - 終了日は必須
// - 開始日は終了日よりも前の日付であること
// - 終了日は開始日よりも後の日付であること
// - 開始日と終了日は同じ日付であることは許容する
const schema = yup.object().shape({
  startAt: yup
    .date()
    .typeError('対応していない日付です')
    .required('開始時点を指定してください')
    .test('dateRange', '開始時点は終了時点よりも前の日付にしてください', validateDateRange),
  endAt: yup
    .date()
    .typeError('対応していない日付です')
    .required('終了時点を指定してください')
    .test('dateRange', '終了時点は開始時点よりも後の日付にしてください', validateDateRange),
});

function validateDateRange(_: Date, context: any) {
  const { startAt, endAt } = context.parent;
  const isStartAtIsValid = dayjs(startAt).isValid();
  const isEndAtIsValid = dayjs(endAt).isValid();
  if (!isStartAtIsValid || !isEndAtIsValid) return true;

  return compareDate(startAt, endAt);
}

function compareDate(shouldBefore: string, shouldAfter: string): boolean {
  const diff = {
    year: dayjs(shouldBefore).diff(shouldAfter, 'y'),
    month: dayjs(shouldBefore).diff(shouldAfter, 'm'),
  };

  const isSame = diff.month === 0 && diff.year === 0;

  return dayjs(shouldBefore).isBefore(shouldAfter) || isSame;
}

type UseContractTermsFormProps = {
  defaultValues: {
    startAt: Dayjs | string;
    endAt: Dayjs | string;
  };
};

function useContractTermsForm({ defaultValues }: UseContractTermsFormProps) {
  const { watch, trigger, ...rest } = useForm({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues,
  });

  const watchStartAt = watch('startAt');
  const watchEndAt = watch('endAt');

  function handleFormChange(name: string) {
    if (name === 'startAt') {
      if (watchEndAt === '') return;
      trigger('endAt');
    }
    if (name === 'endAt') {
      if (watchStartAt === '') return;
      trigger('startAt');
    }
  }

  return {
    trigger,
    watchStartAt,
    watchEndAt,
    handleFormChange,
    ...rest,
  };
}

export { useContractTermsForm };
