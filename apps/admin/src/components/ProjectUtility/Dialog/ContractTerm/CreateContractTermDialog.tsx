import { Loading<PERSON>utton } from '@mui/lab';
import {
  <PERSON>ton,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/ja';
import { ContractTermsForm } from './ContractTermsForm';
import { formatDateForRequest } from './formatDateForRequest';
import { useContractTermsForm } from './useContractTermsForm';
import { useCreateContractTerm } from '@/api';
import { CreateContractTermResponse } from '@/api/types';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { Project } from '@/types';

function useCreateContractTermDialog() {
  const { openState, open, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return {
    CreateContractTermDialog,
    props,
    open,
  };
}

type CreateContractTermDialogProps = {
  projectId: Project['id'];
  onSuccess?: (response: CreateContractTermResponse) => void;
} & HooksDialogProps;

function CreateContractTermDialog({
  projectId,
  close,
  openState,
  ...props
}: CreateContractTermDialogProps) {
  const { trigger: createRequestTrigger, isLoading, reset: requestReset } = useCreateContractTerm();

  const {
    control,
    formState: { errors, isValid },
    watchStartAt,
    handleFormChange,
    reset: formReset,
    handleSubmit,
  } = useContractTermsForm({
    defaultValues: {
      startAt: '',
      endAt: '',
    },
  });

  function handleCancel() {
    dialogClose();
  }

  function resetDialog() {
    formReset();
    requestReset();
  }

  function dialogClose() {
    resetDialog();
    close();
  }

  function handleOnFormSubmit() {
    return handleSubmit(async (data) => {
      try {
        const { startAt, endAt } = {
          startAt: formatDateForRequest(data.startAt),
          endAt: formatDateForRequest(data.endAt),
        };

        const body = {
          projectId,
          startAt,
          endAt,
        };

        const response = await createRequestTrigger({ body });
        if (response) {
          dialogClose();
          props.onSuccess?.(response);
        }
      } catch (error) {
        console.error(error);
      }
    });
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={'ja'}>
        <DialogTitle>契約期間を追加</DialogTitle>
        <DialogContent>
          <DialogContentText>開始・終了時点を指定してください</DialogContentText>
          <ContractTermsForm
            control={control}
            errors={errors}
            onFormChange={handleFormChange}
            watchStartAt={watchStartAt}
          />
        </DialogContent>
        <DialogActions>
          <Button variant='outlined' onClick={handleCancel}>
            キャンセル
          </Button>
          <LoadingButton
            loading={isLoading}
            variant='contained'
            disabled={!isValid}
            onClick={handleOnFormSubmit()}
          >
            作成
          </LoadingButton>
        </DialogActions>
      </LocalizationProvider>
    </EcoisDialog>
  );
}
export { useCreateContractTermDialog };
