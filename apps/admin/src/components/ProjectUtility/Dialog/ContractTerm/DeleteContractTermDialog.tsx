import { Box, Paper, Typography } from '@mui/material';
import { UTCtoDisplayTime } from '../../utils';
import { useDeleteContractTerm } from '@/api';
import { DeleteContractTermResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ContractTerm } from '@/types';

function useDeleteContractTermDialog() {
  const { context, open, openState, close } = useDialogState<ContractTerm>();

  const props = {
    context,
    openState,
    close,
  };

  return { DeleteContractTermDialog, props, open };
}

type DeleteContractTermDialogProps = {
  context: ContractTerm | undefined;
  onSuccess: (response: DeleteContractTermResponse) => void;
} & HooksDialogProps;
function DeleteContractTermDialog({
  context: contractTerm,
  onSuccess,
  ...props
}: DeleteContractTermDialogProps) {
  const { trigger: deleteRequest, isLoading, reset: requestReset } = useDeleteContractTerm();

  async function handleSubmit() {
    if (contractTerm === undefined) {
      return Promise.reject('Project is undefined');
    }
    try {
      const response = await deleteRequest({
        urlParameter: {
          contractTermId: contractTerm.id,
        },
      });
      if (response) {
        onSuccess?.(response);
      }
    } catch (e) {
      return Promise.reject(e);
    }
  }

  function resetForm() {
    requestReset();
  }

  const contractTermString =
    contractTerm?.startAt && contractTerm?.endAt
      ? `${UTCtoDisplayTime(contractTerm?.startAt)} ~ ${UTCtoDisplayTime(contractTerm?.endAt)}`
      : '';

  return (
    <DeleteDialog
      {...props}
      title={`契約期間の削除`}
      confirmText='契約期間に紐づく、基準値、単価、削減目標値も削除されます'
      isLoading={isLoading}
      onSubmit={handleSubmit}
      onClosed={resetForm}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象の契約期間</Typography>
            <Typography variant='body1'>{contractTermString}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteContractTermDialog };
