import { Edit, Delete } from '@mui/icons-material';
import { Typography, IconButton, Box } from '@mui/material';
import { EnergyTypeIcons } from 'lib/Component/Icons/EnergyTypeIcons';
import { SyntheticEvent, forwardRef, useImperativeHandle } from 'react';
import { useCreateUtilityDialog, useDeleteUtilityDialog, useEditUtilityDialog } from './Dialog';
import { SubPanel } from './SubPanel';
import { Project, Utility } from '@/types';

type UtilitiesPanelProps = {
  utilities: Utility[];
  projectId: Project['id'];
  onRequestSuccess: () => void;
};

type UtilitiesPanelRef = {
  openEditUtilityDialog: (utility: Utility) => void;
};

const UtilitiesPanel = forwardRef<UtilitiesPanelRef, UtilitiesPanelProps>(function UtilitiesPanel(
  { utilities, projectId, onRequestSuccess },
  ref,
) {
  const {
    CreateUtilityDialog,
    props: createUtilityDialogProps,
    open: openCreateUtilityDialog,
  } = useCreateUtilityDialog();

  const {
    EditUtilityDialog,
    props: editUtilityDialogProps,
    open: openEditUtilityDialog,
  } = useEditUtilityDialog();

  const {
    DeleteUtilityDialog,
    props: deleteUtilityDialogProps,
    open: openDeleteUtilityDialog,
  } = useDeleteUtilityDialog();

  function handleSuccess() {
    onRequestSuccess();
  }

  useImperativeHandle(ref, () => ({
    openEditUtilityDialog: openEditUtilityDialog,
  }));

  function handleEditBtnClick(utility: Utility) {
    return (event: SyntheticEvent) => {
      event.stopPropagation();
      openEditUtilityDialog(utility);
    };
  }

  function handleDeleteBtnClick(utility: Utility) {
    return (event: SyntheticEvent) => {
      event.stopPropagation();
      openDeleteUtilityDialog(utility);
    };
  }

  return (
    <>
      <SubPanel.Content>
        <SubPanel.Title text='ユーティリティ' onButtonClick={openCreateUtilityDialog} />
        <SubPanel.List>
          {utilities.length === 0 ? (
            <Typography variant='body2' px={1}>
              登録されていません。追加ボタンより追加してください。
            </Typography>
          ) : (
            utilities.map(({ id, name, energyType, ...rest }) => {
              return (
                <SubPanel.ListItem
                  key={id}
                  disableRipple
                  primary={name}
                  secondary={
                    <>
                      <Box display={'flex'} alignItems='center' gap={0.3}>
                        <IconButton sx={{ ml: -1.3, mr: -1.3 }} disableRipple>
                          <EnergyTypeIcons energyTypeId={energyType.id} sx={{ fontSize: 16 }} />
                        </IconButton>
                        {energyType.name}
                      </Box>
                    </>
                  }
                  actions={
                    <>
                      <IconButton
                        size='small'
                        onClick={handleEditBtnClick({
                          id,
                          name,
                          energyType,
                          ...rest,
                        })}
                      >
                        <Edit fontSize='small' />
                      </IconButton>
                      <IconButton
                        size='small'
                        onClick={handleDeleteBtnClick({
                          id,
                          name,
                          energyType,
                          ...rest,
                        })}
                      >
                        <Delete fontSize='small' />
                      </IconButton>
                    </>
                  }
                />
              );
            })
          )}
        </SubPanel.List>
      </SubPanel.Content>
      <CreateUtilityDialog
        {...createUtilityDialogProps}
        projectId={projectId}
        onSuccess={handleSuccess}
      />
      <EditUtilityDialog {...editUtilityDialogProps} onSuccess={handleSuccess} />
      <DeleteUtilityDialog {...deleteUtilityDialogProps} onSuccess={handleSuccess} />
    </>
  );
});

export type { UtilitiesPanelRef };
export { UtilitiesPanel };
