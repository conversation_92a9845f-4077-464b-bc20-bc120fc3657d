import { Add } from '@mui/icons-material';
import {
  <PERSON>,
  Button,
  Divider,
  Paper,
  Stack,
  Typography,
  List as <PERSON><PERSON><PERSON>ist,
  ListItem as <PERSON><PERSON><PERSON>ist<PERSON><PERSON>,
  ListItemButton,
  ListItemText,
  Grid,
} from '@mui/material';
import React, { ReactNode, SyntheticEvent } from 'react';
import { PropsWithChildren } from 'react';

function SubPanel() {
  return <></>;
}

function Container({ children }: PropsWithChildren) {
  return (
    <Grid container spacing={2} alignItems={'stretch'}>
      {children}
    </Grid>
  );
}

function Content({ children }: PropsWithChildren) {
  return (
    <Grid item xs={6} lg={12}>
      <Paper
        sx={{
          height: '100%',
        }}
      >
        {children}
      </Paper>
    </Grid>
  );
}

function Title({ text, onButtonClick }: { text: string; onButtonClick: () => void }) {
  return (
    <Box px={1} pt={1}>
      <Stack direction={'row'} alignItems={'center'} justifyContent={'space-between'} pb={1}>
        <Typography variant='body2'>{text}</Typography>
        <Box>
          <Button
            size='small'
            disableElevation
            startIcon={<Add />}
            variant='contained'
            onClick={onButtonClick}
          >
            追加
          </Button>
        </Box>
      </Stack>
      <Divider
        sx={(theme) => ({
          marginLeft: `-${theme.spacing(1)}`,
          marginRight: `-${theme.spacing(1)}`,
        })}
      />
    </Box>
  );
}

function List({ children }: PropsWithChildren) {
  return (
    <MuiList
      sx={(theme) => ({
        maxHeight: theme.spacing(37),
        overflowY: 'auto',
        [theme.breakpoints.down('lg')]: {
          maxHeight: theme.spacing(26),
        },
      })}
    >
      {children}
    </MuiList>
  );
}

type ListItemProps = PropsWithChildren<{
  primary: string;
  secondary?: string | ReactNode;
  actions?: React.ReactNode;
  onClick?: (event: SyntheticEvent) => void;
  disableRipple?: boolean;
  selected?: boolean;
}>;

function ListItem({
  primary,
  secondary,
  actions,
  onClick,
  disableRipple = false,
  selected,
}: ListItemProps) {
  return (
    <MuiListItem
      disableGutters
      disablePadding
      secondaryAction={actions}
      onClick={onClick}
      sx={{
        '.MuiListItemSecondaryAction-root': {
          visibility: 'hidden',
        },
        '&:hover': {
          '.MuiListItemSecondaryAction-root': {
            visibility: 'inherit',
          },
        },
      }}
    >
      <ListItemButton disableRipple={disableRipple} selected={selected}>
        <ListItemText
          primary={primary}
          secondary={secondary}
          primaryTypographyProps={{ variant: 'body2' }}
        />
      </ListItemButton>
    </MuiListItem>
  );
}

SubPanel.Container = Container;
SubPanel.Content = Content;
SubPanel.Title = Title;
SubPanel.List = List;
SubPanel.ListItem = ListItem;

export { SubPanel };
