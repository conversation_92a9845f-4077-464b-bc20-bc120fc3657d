import { nanoid } from 'nanoid';
import { PropsWithChildren, useState } from 'react';
import { useEffectOnce } from 'react-use';
import { ProjectUtilityContext } from './ProjectUtilityContext';
import {
  useListContractTerms,
  useListReductionTargetData,
  useListStandardData,
  useListUnitcosts,
  useListUtilities,
} from '@/api';
import {
  ContractTerm,
  Project,
  ReductionTargetDataWithKey,
  StandardDataWithKey,
  UnitcostWithKey,
  Utility,
} from '@/types';

function ProjectUtilityProvider({
  projectId,
  children,
}: PropsWithChildren<{ projectId: Project['id'] }>) {
  const { trigger: listUtilities, isLoading: isListUtilitiesLoading } = useListUtilities();
  const { trigger: listContractTerms, isLoading: isListContractTermsLoading } =
    useListContractTerms();
  const { trigger: listUnitcosts, isLoading: isListUnitcostsLoading } = useListUnitcosts();
  const { trigger: listStandardData, isLoading: isListStandardDataLoading } = useListStandardData();
  const { trigger: listReductionTargetData, isLoading: isListReductionTargetDataLoading } =
    useListReductionTargetData();
  // TODO: reductionGuaranteeDataを追加する

  const [data, setData] = useState<
    | {
        utilities: Utility[];
        contractTerms: ContractTerm[];
        unitcosts: UnitcostWithKey[];
        standardData: StandardDataWithKey[];
        reductionTargetData: ReductionTargetDataWithKey[];
        // TODO: reductionGuaranteeDataを追加する
      }
    | undefined
  >(undefined);

  const isLoading =
    isListUtilitiesLoading ||
    isListContractTermsLoading ||
    isListUnitcostsLoading ||
    isListStandardDataLoading ||
    isListReductionTargetDataLoading;
  // TODO: reductionGuaranteeDataを追加する

  async function fetchAll() {
    const [utilitiesRes, contractTermsRes, unitcostsRes, standardDataRes, reductionTargetDataRes] =
      await Promise.all([
        listUtilities({
          queryParameter: {
            projectIds: [projectId],
          },
        }),
        listContractTerms({
          queryParameter: {
            projectIds: [projectId],
          },
        }),
        listUnitcosts({
          queryParameter: {
            projectIds: [projectId],
          },
        }),
        listStandardData({
          queryParameter: {
            projectIds: [projectId],
          },
        }),
        listReductionTargetData({
          queryParameter: {
            projectIds: [projectId],
          },
        }),
        // TODO: reductionGuaranteeDataを追加する
      ]);

    if (
      !contractTermsRes?.contractTerms ||
      !utilitiesRes?.utilities ||
      !unitcostsRes?.unitcosts ||
      !standardDataRes?.standardData ||
      !reductionTargetDataRes?.reductionTargetData
      // TODO: reductionGuaranteeDataを追加する
    ) {
      throw new Error('Failed to fetch data');
    }

    // utilitiesとcontractTermsで2重ループを回して、unitcostsで存在しないものはオブジェクトを作成する
    // 存在するものは、そのまま利用する

    const newUnitcosts: UnitcostWithKey[] = contractTermsRes.contractTerms.flatMap(
      (contractTerm) => {
        return utilitiesRes.utilities.map((utility) => {
          const unitcost = unitcostsRes.unitcosts.find(
            (unitcost) =>
              unitcost.contractTermId === contractTerm.id && unitcost.utilityId === utility.id,
          );
          if (unitcost) {
            return {
              key: nanoid(),
              ...unitcost,
            };
          } else {
            return {
              key: nanoid(),
              contractTermId: contractTerm.id,
              projectId: projectId,
              utilityId: utility.id,
              value: null,
            };
          }
        });
      },
    );

    const newStandardData: StandardDataWithKey[] = contractTermsRes.contractTerms.flatMap(
      (contractTerm) => {
        return utilitiesRes.utilities.map((utility) => {
          const standardData = standardDataRes.standardData.find(
            (standardData) =>
              standardData.contractTermId === contractTerm.id &&
              standardData.utilityId === utility.id,
          );
          if (standardData) {
            return {
              key: nanoid(),
              ...standardData,
            };
          } else {
            return {
              key: nanoid(),
              contractTermId: contractTerm.id,
              projectId: projectId,
              utilityId: utility.id,
              values: null,
            };
          }
        });
      },
    );

    const newReductionTargetData: ReductionTargetDataWithKey[] =
      contractTermsRes.contractTerms.flatMap((contractTerm) => {
        return utilitiesRes.utilities.map((utility) => {
          const reductionTargetData = reductionTargetDataRes.reductionTargetData.find(
            (reductionTargetData) =>
              reductionTargetData.contractTermId === contractTerm.id &&
              reductionTargetData.utilityId === utility.id,
          );
          if (reductionTargetData) {
            return {
              key: nanoid(),
              ...reductionTargetData,
            };
          } else {
            return {
              key: nanoid(),
              contractTermId: contractTerm.id,
              projectId: projectId,
              utilityId: utility.id,
              values: null,
            };
          }
        });
      });

    // TODO: reductionGuaranteeDataを追加する

    setData({
      utilities: utilitiesRes.utilities,
      contractTerms: contractTermsRes.contractTerms,
      unitcosts: newUnitcosts,
      standardData: newStandardData,
      reductionTargetData: newReductionTargetData,
      // TODO: reductionGuaranteeDataを追加する
    });
  }

  useEffectOnce(() => {
    fetchAll();
  });

  const value = {
    fetchAll,
    isLoading,
    data,
    projectId,
  };

  return <ProjectUtilityContext.Provider value={value}>{children}</ProjectUtilityContext.Provider>;
}

// TODO: ここから下は、今後の実装で必要になったら実装する(現状は1部の更新でも全てのツリーを作り直す設計とする)
// function insertContractTerm(
//   tree: ProjectUtilityTreeState,
//   next: ContractTerm,
// ): ProjectUtilityTreeState {
//   const newContractTerms = [
//     ...tree.projectUtilityTree.contractTerms,
//     {
//       ...next,
//       utilities: [
//         ...tree.projectUtilityTree.contractTerms[0].utilities.map((utility) => {
//           if (utility !== undefined) {
//             return {
//               ...utility,
//               unitcost: undefined,
//               standardData: undefined,
//               reductionTargetData: undefined,
//             };
//           } else {
//             return undefined;
//           }
//         }),
//       ],
//     },
//   ];
//   return {
//     projectUtilityTree: {
//       contractTerms: newContractTerms,
//     },
//   };
// }

export { ProjectUtilityProvider };
