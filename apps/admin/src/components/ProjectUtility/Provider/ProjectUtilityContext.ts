import { createContext, useContext } from 'react';
import {
  ContractTerm,
  Project,
  ReductionTargetData,
  ReductionTargetDataWithKey,
  StandardData,
  StandardDataWithKey,
  Unitcost,
  UnitcostWithKey,
  Utility,
} from '@/types';

type ProjectUtilityContextType = {
  fetchAll: () => Promise<void>;
  isLoading: boolean;
  data:
    | {
        contractTerms: ContractTerm[];
        utilities: Utility[];
        unitcosts: UnitcostWithKey[];
        standardData: StandardDataWithKey[];
        reductionTargetData: ReductionTargetDataWithKey[];
        // TODO: reductionTargetDataを追加
      }
    | undefined;
  projectId: Project['id'];
  // updateProjectUtilityTree: updateProjectUtilityTreeFunc;
};

// 初期状態はundefinedとする
type TargetsToDataMap = {
  contractTerms: ContractTerm;
  utility: Utility;
  unitcost: Unitcost;
  standardData: StandardData;
  reductionTargetData: ReductionTargetData;
  // TODO: reductionTargetDataを追加
};
type updateProjectUtilityTreeFunc = <T extends keyof TargetsToDataMap>(
  target: T,
  data: TargetsToDataMap[T],
) => void;

const ProjectUtilityContext = createContext<ProjectUtilityContextType | null>(null);

// useContext
function useProjectUtilityContext() {
  const context = useContext(ProjectUtilityContext);
  if (!context) {
    throw new Error('useProjectUtilityContext must be used within a ProjectUtilityContext');
  }
  return context;
}

export type { updateProjectUtilityTreeFunc, ProjectUtilityContextType };
export { ProjectUtilityContext, useProjectUtilityContext };
