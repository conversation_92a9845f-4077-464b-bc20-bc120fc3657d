import { Edit, Delete } from '@mui/icons-material';
import { IconButton } from '@mui/material';
import { SyntheticEvent, forwardRef, useImperativeHandle } from 'react';
import { useCreateContractTermDialog, useEditContractTermDialog } from './Dialog';
import { useDeleteContractTermDialog } from './Dialog';
import { SubPanel } from './SubPanel';
import { UTCtoDisplayTime } from './utils';
import { CreateContractTermResponse, UpdateContractTermResponse } from '@/api/types';
import { ContractTerm, Project } from '@/types';

type ContractTermsPanelProps = {
  contractTerms: ContractTerm[];
  projectId: Project['id'];
  currentItemId?: number;
  onRequestSuccess?: () => void;
  onItemClick: (id: string) => void;
  onDeleted?: () => void;
  onCreated?: (response: CreateContractTermResponse) => void;
  onEdit?: (response: UpdateContractTermResponse) => void;
};

type ContractTermsPanelRef = {
  openEditContractTermDialog: (contractTerm: ContractTerm) => void;
  openDeleteContractTermDialog: (contractTerm: ContractTerm) => void;
  openCreateContractTermDialog: () => void;
};

const ContractTermsPanel = forwardRef<ContractTermsPanelRef, ContractTermsPanelProps>(
  function ContractTermsPanel(
    { contractTerms, projectId, onItemClick, onDeleted, onEdit, currentItemId, onCreated },
    ref,
  ) {
    const {
      CreateContractTermDialog,
      open: openCreateContractTermDialog,
      props: createContractTermProps,
    } = useCreateContractTermDialog();

    const {
      EditContractTermDialog,
      open: openEditContractTermDialog,
      props: editContractTermProps,
    } = useEditContractTermDialog();

    const {
      DeleteContractTermDialog,
      open: openDeleteContractTermDialog,
      props: deleteContractTermProps,
    } = useDeleteContractTermDialog();

    useImperativeHandle(ref, () => ({
      openEditContractTermDialog: openEditContractTermDialog,
      openDeleteContractTermDialog: openDeleteContractTermDialog,
      openCreateContractTermDialog: openCreateContractTermDialog,
    }));

    function handleOnAddBtnClick() {
      openCreateContractTermDialog();
    }

    function handleEditSuccess(response: UpdateContractTermResponse) {
      onEdit?.(response);
    }

    function handleDeleteSuccess() {
      onDeleted?.();
    }

    function handleCreateSuccess(response: CreateContractTermResponse) {
      onCreated?.(response);
    }

    function handleEditBtnClick(contractTerm: ContractTerm) {
      return (event: SyntheticEvent) => {
        event.stopPropagation();
        openEditContractTermDialog(contractTerm);
      };
    }

    function handleDeleteBtnClick(contractTerm: ContractTerm) {
      return (event: SyntheticEvent) => {
        event.stopPropagation();
        openDeleteContractTermDialog(contractTerm);
      };
    }

    return (
      <>
        <SubPanel.Content>
          <SubPanel.Title text='契約期間' onButtonClick={handleOnAddBtnClick} />
          <SubPanel.List>
            {contractTerms.length === 0 ? (
              <SubPanel.ListItem primary={`契約期間の登録件数は0件です。`} />
            ) : (
              contractTerms.map((contractTerm) => {
                return (
                  <SubPanel.ListItem
                    key={contractTerm.id}
                    primary={`${UTCtoDisplayTime(contractTerm.startAt)}〜${UTCtoDisplayTime(
                      contractTerm.endAt,
                    )}`}
                    selected={contractTerm.id === currentItemId}
                    actions={
                      <>
                        <IconButton size='small' onClick={handleEditBtnClick(contractTerm)}>
                          <Edit fontSize='small' />
                        </IconButton>
                        <IconButton size='small' onClick={handleDeleteBtnClick(contractTerm)}>
                          <Delete fontSize='small' />
                        </IconButton>
                      </>
                    }
                    onClick={() => {
                      onItemClick(contractTerm.id.toString());
                    }}
                  />
                );
              })
            )}
          </SubPanel.List>
        </SubPanel.Content>
        <CreateContractTermDialog
          {...createContractTermProps}
          projectId={projectId}
          onSuccess={handleCreateSuccess}
        />
        <EditContractTermDialog {...editContractTermProps} onSuccess={handleEditSuccess} />
        <DeleteContractTermDialog {...deleteContractTermProps} onSuccess={handleDeleteSuccess} />
      </>
    );
  },
);

export type { ContractTermsPanelRef };
export { ContractTermsPanel };
