import { ArrowDropDown, Delete, Edit } from '@mui/icons-material';
import { Tab<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, TabPanel } from '@mui/lab';
import {
  Paper,
  Stack,
  Typography,
  Box,
  Button,
  CircularProgress,
  Grid,
  Tab,
  Menu,
  MenuItem,
  ListItemIcon,
} from '@mui/material';
import { MouseEvent, useEffect, useRef, useState } from 'react';
import {
  UtilityRows,
  UnitcostRows,
  RowContainer,
  StandardDataRows,
  useRowContainer,
} from './components';
import { ReductionTargetDataRows } from './components/ReductionTargetDataRows';
import { ContractTermsPanel, ContractTermsPanelRef } from './ContractTermsPanel';
import { useCreateUtilityDialog } from './Dialog';
import { useContractTermTab } from './hooks';
import { useProjectUtilityContext } from './Provider';
import { SubPanel } from './SubPanel';
import { UtilitiesPanel, UtilitiesPanelRef } from './UtilitiesPanel';
import { UTCtoDisplayTime } from './utils';
import { CreateContractTermResponse, UpdateContractTermResponse } from '@/api/types';
import { ContractTerm, Utility } from '@/types';

function ProjectUtilityContent() {
  const { isLoading, data, projectId, fetchAll } = useProjectUtilityContext();

  // TODO Menuコンポーネントにまとめても良いかも
  // Start
  const [menuAnchor, setMenuAnchor] = useState<
    | {
        anchorEl: undefined | (EventTarget & Element);
        contractTerm: ContractTerm | undefined;
      }
    | undefined
  >(undefined);

  const handleTabMenuOpenBtnClick = (contractTerm: ContractTerm) => {
    return (event: MouseEvent) => {
      event.stopPropagation();
      setMenuAnchor({
        anchorEl: event.currentTarget,
        contractTerm,
      });
    };
  };

  const handleMenuClose = () => {
    setMenuAnchor(undefined);
  };
  // End

  // 契約期間のタブ
  const { tabValue, handleTabChange: setTabValue, reset: tabReset } = useContractTermTab();

  function handleTabChange(newValue: string) {
    setTabValue(newValue);
  }

  // パネルのRef関連
  const utilitiesPanelRef = useRef<UtilitiesPanelRef>(null);
  const contractTermsPanelRef = useRef<ContractTermsPanelRef>(null);

  /* Data が存在しないとPanelコンポーネントが存在しないので、これは重複するが読み込み必須 */
  const {
    CreateUtilityDialog,
    props: createUtilityDialogProps,
    open: openCreateUtilityDialog,
  } = useCreateUtilityDialog();

  function openEditUtilityDialog(utility: Utility) {
    utilitiesPanelRef?.current?.openEditUtilityDialog?.(utility);
  }

  async function fetchAndFocusTab({
    contractTerm: { id },
  }: UpdateContractTermResponse | CreateContractTermResponse) {
    await fetchAll();
    setTabValue(id.toString());
  }

  function openCreateContractTermDialog() {
    contractTermsPanelRef?.current?.openCreateContractTermDialog?.();
  }

  function openEditContractTermDialog() {
    if (menuAnchor?.contractTerm === undefined) return;
    contractTermsPanelRef?.current?.openEditContractTermDialog?.(menuAnchor?.contractTerm);
    handleMenuClose();
  }

  function openDeleteContractTermDialog() {
    if (menuAnchor?.contractTerm === undefined) return;
    contractTermsPanelRef?.current?.openDeleteContractTermDialog?.(menuAnchor?.contractTerm);
    handleMenuClose();
  }

  // リクエスト成功時の処理
  async function handleRequestSuccess() {
    await fetchAll();
  }

  async function handleContactTermDeleted() {
    await fetchAll();
    tabReset();
  }

  // 初期表示時に、契約期間が登録されていない場合は、タブを「契約期間を登録する」にする
  useEffect(() => {
    if (tabValue === 'empty' && data?.contractTerms) {
      if (data?.contractTerms.length === 0) return;
      setTabValue(data?.contractTerms[0]?.id.toString());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.contractTerms.length, tabValue, data?.contractTerms]);

  const {
    ref: { utilityRef, unitcostRef, standardDataRef, reductionTargetRef },
    rect,
  } = useRowContainer();

  // 読み込み時は、ローディングを表示する
  if (isLoading) {
    return <CircularProgress />;
  }

  return (
    <>
      {data?.utilities.length === 0 ? (
        <>
          <Paper>
            <Stack spacing={2} sx={{ p: 2 }} textAlign={'center'}>
              <Typography variant='body1'>ユーティリティの登録件数は0件です。</Typography>
              <Box>
                <Button variant={'contained'} disableElevation onClick={openCreateUtilityDialog}>
                  ユーティリティを登録する
                </Button>
              </Box>
            </Stack>
          </Paper>
          {/* Data が存在しないとPanelコンポーネントが存在しないので、これは重複するが読み込み必須 */}
          <CreateUtilityDialog
            {...createUtilityDialogProps}
            projectId={projectId}
            onSuccess={handleRequestSuccess}
          />
        </>
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12} lg={3}>
            <SubPanel.Container>
              {data && (
                <>
                  <UtilitiesPanel
                    ref={utilitiesPanelRef}
                    utilities={data.utilities}
                    projectId={projectId}
                    onRequestSuccess={handleRequestSuccess}
                  />
                  <ContractTermsPanel
                    ref={contractTermsPanelRef}
                    contractTerms={data.contractTerms}
                    projectId={projectId}
                    onEdit={fetchAndFocusTab}
                    onCreated={fetchAndFocusTab}
                    currentItemId={parseInt(tabValue, 10)}
                    onItemClick={(id: string) => {
                      handleTabChange(id);
                    }}
                    onRequestSuccess={handleRequestSuccess}
                    onDeleted={handleContactTermDeleted}
                  />
                </>
              )}
            </SubPanel.Container>
          </Grid>
          <Grid item xs={12} lg={9}>
            <Box mb={2}>
              <Paper>
                {!isLoading && data?.contractTerms && (
                  <TabContext value={tabValue}>
                    {data?.contractTerms.length === 0 ? (
                      <TabPanel value={'empty'}>
                        <Box p={2}>
                          <Stack alignItems={'center'} spacing={2}>
                            <Typography variant={'body1'}>
                              契約期間を登録すると、「単価」「基準値」「削減目標」を入力できます 。
                            </Typography>
                            <Box>
                              <Button
                                variant={'contained'}
                                disableElevation
                                onClick={openCreateContractTermDialog}
                              >
                                契約期間の追加
                              </Button>
                            </Box>
                          </Stack>
                        </Box>
                      </TabPanel>
                    ) : (
                      <>
                        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                          <TabList
                            variant='scrollable'
                            onChange={(_, tabId) => handleTabChange(tabId)}
                            aria-label='tabs'
                          >
                            {data?.contractTerms.map((contractTerm) => {
                              const { id, endAt, startAt } = contractTerm;
                              return (
                                <Tab
                                  key={id}
                                  label={`${UTCtoDisplayTime(startAt)}〜${UTCtoDisplayTime(endAt)}`}
                                  value={id.toString()}
                                  sx={{
                                    '& .MuiSvgIcon-root': {
                                      visibility: 'hidden',
                                    },
                                    '&:hover .MuiSvgIcon-root': {
                                      visibility: 'visible',
                                    },
                                  }}
                                  icon={
                                    <ArrowDropDown
                                      onClick={handleTabMenuOpenBtnClick(contractTerm)}
                                      sx={{
                                        visibility: menuAnchor ? 'visible' : 'hidden',
                                      }}
                                    />
                                  }
                                  iconPosition='end'
                                />
                              );
                            })}
                            {/* データが無い場合、エラー回避のためのタブ  */}
                            <Tab value={'empty'} sx={{ visibility: 'hidden' }} />
                          </TabList>
                          <Menu
                            anchorOrigin={{
                              vertical: -4,
                              horizontal: 'left',
                            }}
                            anchorEl={menuAnchor?.anchorEl}
                            open={Boolean(menuAnchor?.anchorEl)}
                            onClose={handleMenuClose}
                          >
                            <MenuItem onClick={openEditContractTermDialog}>
                              <ListItemIcon>
                                <Edit color={'inherit'} fontSize='small' />
                              </ListItemIcon>
                              <Typography variant='inherit'>編集</Typography>
                            </MenuItem>
                            <MenuItem onClick={openDeleteContractTermDialog}>
                              <ListItemIcon>
                                <Delete color={'inherit'} fontSize='small' />
                              </ListItemIcon>
                              <Typography variant='inherit'>削除</Typography>
                            </MenuItem>
                          </Menu>
                        </Box>
                        {data?.contractTerms.map((contractTerm) => {
                          return (
                            <TabPanel
                              key={contractTerm.id}
                              value={contractTerm.id.toString()}
                              sx={{
                                padding: 0,
                              }}
                            >
                              <RowContainer utilities={data.utilities} rect={rect}>
                                <UtilityRows
                                  ref={utilityRef}
                                  utilities={data.utilities}
                                  onClick={openEditUtilityDialog}
                                />
                                <UnitcostRows
                                  ref={unitcostRef}
                                  unitcosts={data.unitcosts}
                                  contractTermId={contractTerm.id}
                                  onEditSuccess={handleRequestSuccess}
                                />
                                <StandardDataRows
                                  ref={standardDataRef}
                                  standardData={data.standardData}
                                  contractTermId={contractTerm.id}
                                  onEditSuccess={handleRequestSuccess}
                                />

                                <ReductionTargetDataRows
                                  ref={reductionTargetRef}
                                  reductionTargetData={data.reductionTargetData}
                                  contractTermId={contractTerm.id}
                                  onEditSuccess={handleRequestSuccess}
                                />
                              </RowContainer>
                            </TabPanel>
                          );
                        })}
                      </>
                    )}
                  </TabContext>
                )}
              </Paper>
            </Box>
          </Grid>
        </Grid>
      )}
    </>
  );
}

export { ProjectUtilityContent };
