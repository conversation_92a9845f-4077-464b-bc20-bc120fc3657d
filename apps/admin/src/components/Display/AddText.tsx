import { Link } from '@mui/material';

type InputProps = {
  label: string;
  onClick?: () => void;
};

function AddText({ label, onClick }: InputProps) {
  return (
    <Link
      sx={(theme) => ({
        color: theme.palette.text.secondary,
        '&:hover': {
          color: theme.palette.text.primary,
        },
        textDecoration: 'none',
      })}
      onClick={() => {
        onClick?.();
      }}
    >
      {label}を追加
    </Link>
  );
}

export { AddText };
