import { Stack, Box, Typography, StackProps, TypographyProps } from '@mui/material';
import { PropsWithChildren } from 'react';

function ContentTitle({ children, alignItems, ...props }: PropsWithChildren<StackProps>) {
  return (
    <Stack
      direction={'row'}
      justifyContent={'space-between'}
      alignItems={alignItems ?? 'center'}
      mb={1}
      {...props}
    >
      {children}
    </Stack>
  );
}

function Label({ children, ...props }: PropsWithChildren<TypographyProps<'h2'>>) {
  return (
    <Box>
      <Typography component={'h2'} variant='h6' {...props}>
        {children}
      </Typography>
    </Box>
  );
}

function Buttons({ children }: PropsWithChildren) {
  return (
    <Stack direction={'row'} spacing={2}>
      {children}
    </Stack>
  );
}

ContentTitle.Label = Label;
ContentTitle.Buttons = Buttons;

export { ContentTitle };
