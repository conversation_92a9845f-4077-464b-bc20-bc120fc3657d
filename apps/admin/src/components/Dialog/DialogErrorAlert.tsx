import { Alert, Box, Typography } from '@mui/material';
import { PropsWithChildren } from 'react';

type DialogErrorAlertProps = PropsWithChildren<{
  error: any;
  label: string;
  textCustomizer?: (text: string) => string;
}>;

function DialogErrorAlert({ error, label, textCustomizer }: DialogErrorAlertProps) {
  function generateErrorText() {
    return textCustomizer
      ? textCustomizer(error.response.data.error.message)
      : error.response.data.error.message;
  }

  return (
    <Alert severity='error'>
      {label}
      <Box>
        <Typography variant={'caption'} whiteSpace={'pre-wrap'}>
          {generateErrorText()}
        </Typography>
      </Box>
    </Alert>
  );
}

export { DialogErrorAlert };
