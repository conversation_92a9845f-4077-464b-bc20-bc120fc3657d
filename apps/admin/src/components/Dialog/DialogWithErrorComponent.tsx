import { Box, Dialog, DialogProps } from '@mui/material';
import { useErrorAlert } from 'lib/ErrorAlert';
import { HooksDialogProps } from '@/hooks';

function DialogWithErrorComponent({
  open,
  close,
  children,
  onClose,
  ...props
}: DialogProps & Pick<HooksDialogProps, 'close'>) {
  const { errors } = useErrorAlert();

  function handleClose(...event: Parameters<NonNullable<DialogProps['onClose']>>) {
    onClose && onClose(...event);
    close();
    // setOpenState(false);
  }

  return (
    <Dialog open={open} {...props} onClose={handleClose}>
      {errors.length > 0 && (
        <Box mt={1} mx={2}>
          {/* <ErrorAlertComponent/> */}
        </Box>
      )}
      {children}
    </Dialog>
  );
}

export { DialogWithErrorComponent };
