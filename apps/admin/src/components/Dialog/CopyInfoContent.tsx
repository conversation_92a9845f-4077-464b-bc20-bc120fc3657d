import { CopyAll } from '@mui/icons-material';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  Paper,
  Stack,
  Box,
  Button,
  DialogActions,
} from '@mui/material';
import { ReactElement } from 'react';
import { useCopyToClipboard } from 'react-use';
import { Label } from '../Display';

function CopyInfoContent({
  titleText,
  bodyText,
  clipboardText,
  contentElements,
  onClose,
}: {
  titleText: string;
  bodyText: string;
  clipboardText: string;
  contentElements: ReactElement;
  onClose?: () => void;
}) {
  function handleCloseClick() {
    onClose && onClose();
  }

  const [state, copyToClipboard] = useCopyToClipboard();

  function handleCopy() {
    copyToClipboard(clipboardText);
  }

  return (
    <>
      <DialogTitle id='alert-dialog-title'>{titleText}</DialogTitle>
      <DialogContent>
        {bodyText.split(/(\n)/).map((text, index) => {
          return <DialogContentText key={index}>{text}</DialogContentText>;
        })}
        <Paper
          component={Stack}
          justifyContent={'space-between'}
          direction={'row'}
          variant='outlined'
          sx={{ p: 2, my: 2 }}
          elevation={0}
        >
          <Box>{contentElements}</Box>
          <Box textAlign={'right'}>
            <Button size='small' onClick={handleCopy} startIcon={<CopyAll />}>
              コピー
            </Button>
            <Box>{!!state.value && <Label>コピーしました</Label>}</Box>
          </Box>
        </Paper>
      </DialogContent>
      <DialogActions>
        <Button disableElevation onClick={handleCloseClick} variant={'contained'}>
          閉じる
        </Button>
      </DialogActions>
    </>
  );
}

export { CopyInfoContent };
