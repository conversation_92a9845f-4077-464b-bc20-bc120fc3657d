/* 

- タイトル
- コンテンツ
- ボタン
- サブミットイベント

*/

import { LoadingButton } from '@mui/lab';
import {
  Button,
  Checkbox,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
} from '@mui/material';
import { PropsWithChildren, useState } from 'react';
import { EcoisDialog } from './EcoisDialog';
import { useDialogState } from '@/hooks';

function useDeleteDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { DeleteDialog, props, open };
}

type DeleteDialogProps = PropsWithChildren<{
  title?: string;
  openState: boolean;
  labelText?: string;
  confirmText?: string;
  buttonText?: string;
  close: () => void;
  onSubmit?: () => Promise<void> | void;
  isLoading?: boolean;
  onClosed?: () => void;
}>;

function DeleteDialog({
  title,
  openState,
  close,
  onSubmit,
  isLoading,
  children,
  labelText,
  confirmText,
  buttonText,
  onClosed,
}: DeleteDialogProps) {
  const [confirm, setConfirm] = useState(false);

  function toggle() {
    setConfirm(!confirm);
  }

  function reset() {
    setConfirm(false);
  }

  function closeDialog() {
    reset();
    close();
    onClosed && onClosed();
  }

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth={'xs'}>
      <DialogTitle color={'error'}>{title || '削除'}</DialogTitle>
      <DialogContent>
        <DialogContentText mt={1}>{confirmText || '本当に削除しますか？'}</DialogContentText>
        {children}
        <FormControlLabel
          control={<Checkbox color='error' value={confirm} onChange={toggle} />}
          label={labelText || '削除する'}
        />
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            closeDialog();
          }}
          color='inherit'
        >
          キャンセル
        </Button>
        <LoadingButton
          onClick={async () => {
            onSubmit && (await onSubmit());
            closeDialog();
          }}
          disabled={!confirm}
          disableElevation
          loading={isLoading}
          variant={'contained'}
          color={'error'}
        >
          {buttonText || '削除'}
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}

export { useDeleteDialog, DeleteDialog };
