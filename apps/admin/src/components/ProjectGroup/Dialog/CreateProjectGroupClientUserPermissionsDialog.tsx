import { LoadingButton } from '@mui/lab';
import {
  <PERSON>,
  Button,
  DialogContent,
  DialogTitle,
  Stack,
  TableCell,
  Theme,
  Typography,
  useTheme,
} from '@mui/material';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { useEffectOnce } from 'react-use';
import { useUpsertProjectGroupClientUserPermission, useListClientUsers } from '@/api';
import { UpsertProjectGroupClientUserPermissionResponse } from '@/api/types';
import { CLCheckbox } from '@/components/ColumnList/ColumnList';
import { EcoisDialog } from '@/components/Dialog';
import { SimpleSearchField, useSimpleSearch } from '@/components/SimpleSearch';
import { EcoisSimpleDataTable } from '@/components/Table';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { DatalistLayout } from '@/layout';
import { ClientUser } from '@/types';

function useCreateProjectGroupClientUserPermissionsDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { CreateProjectGroupClientUserPermissionsDialog, props, open };
}

type CreateProjectGroupClientUserPermissionsDialogProps = {
  onSuccess?: (response: UpsertProjectGroupClientUserPermissionResponse) => void;
  projectGroupId: number;
  onSubmit?: (selectedClientIds: ClientUser['id'][]) => Promise<void>;
  onCancel?: () => void;
};

function CreateProjectGroupClientUserPermissionsDialog({
  projectGroupId,
  onSuccess,
  onCancel,
  openState,
  close,
}: CreateProjectGroupClientUserPermissionsDialogProps & HooksDialogProps) {
  const { trigger: createClientUserPermission, isLoading: isCreateLoading } =
    useUpsertProjectGroupClientUserPermission();

  async function createProjectGroupClientUserPermissions(selectedClientUserIds: number[]) {
    try {
      const response = await createClientUserPermission({
        urlParameter: { projectGroupId },
        body: {
          clientUserIds: selectedClientUserIds,
        },
      });
      if (response) {
        onSuccess?.(response);
        close();
      }
    } catch (error) {
      console.log(error);
    }
  }

  // プロジェクト取得,プロジェクト権限更新
  const { data, isLoading, trigger: fetchClientUsers } = useListClientUsers();

  useEffectOnce(() => {
    fetchClientUsers({
      queryParameter: {},
    });
  });

  // Theme
  const theme = useTheme() as Theme;

  // フォーム送信時の処理
  async function handleOnFormSubmit() {
    await createProjectGroupClientUserPermissions(selectedClientUserIds);
  }

  // キャンセル時の処理
  function handleCancel() {
    onCancel?.();
    close();
  }

  //
  // インクリメンタルサーチ
  //
  const {
    data: filteredProjects,
    state: searchState,
    register,
  } = useSimpleSearch<ClientUser>({
    defaultValues: data?.clientUsers,
    filterFunction: (clientUser, searchText) => {
      return (
        clientUser?.name?.includes(searchText) ||
        clientUser?.id?.toString().includes(searchText) ||
        clientUser?.email?.includes(searchText) ||
        clientUser?.username?.includes(searchText)
      );
    },
  });

  const state = useMemo(
    () => ({
      isEmptyData: data?.clientUsers.length === 0,
      isTableLoading: isLoading || searchState === 'pending',
      isSearchResultEmpty:
        !!data?.clientUsers && filteredProjects.length === 0 && searchState === 'ready',
    }),
    [data, filteredProjects, isLoading, searchState],
  );

  // チェックボックス制御
  const [selectedClientUserIds, setSelectedClientUserIds] = useState<ClientUser['id'][]>([]);
  function addId(id: ClientUser['id']) {
    setSelectedClientUserIds([...selectedClientUserIds, id]);
  }
  function removeId(id: ClientUser['id']) {
    setSelectedClientUserIds(selectedClientUserIds.filter((selectedId) => selectedId !== id));
  }

  function handleCheckOnChange(clientUser: ClientUser) {
    return (event: ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        addId(clientUser.id);
      } else {
        removeId(clientUser.id);
      }
    };
  }

  function handleFullCheckOnChange(event: ChangeEvent<HTMLInputElement>) {
    if (event.target.checked) {
      setSelectedClientUserIds(data?.clientUsers.map((clientUser) => clientUser.id) ?? []);
    } else {
      setSelectedClientUserIds([]);
    }
  }

  const isIndeterminate =
    selectedClientUserIds.length > 0 && selectedClientUserIds.length < filteredProjects.length;

  function handleListItemOnClick(project: ClientUser) {
    return () => {
      if (selectedClientUserIds.includes(project.id)) {
        removeId(project.id);
      } else {
        addId(project.id);
      }
    };
  }

  // ダイアログが閉じるときのリセット処理
  function reset() {
    setSelectedClientUserIds([]);
  }

  // ダイアログが閉じるときのフック
  useEffect(() => {
    openState && reset();
  }, [openState]);

  return (
    <EcoisDialog
      open={openState}
      close={close}
      fullWidth
      maxWidth='xl'
      sx={(theme) => ({
        '.MuiDialog-paper': { height: '100%', minWidth: theme.breakpoints.values.sm + 'px' },
      })}
    >
      <DialogTitle
        component={Stack}
        direction={'row'}
        justifyContent={'space-between'}
        flexWrap={'wrap'}
      >
        <DialogTitle sx={{ padding: 0 }}>
          権限を付与するクライアントユーザーを選択してください
        </DialogTitle>
        <Stack
          direction={'row'}
          spacing={1}
          alignItems={'center'}
          justifyContent={'end'}
          flexGrow={1}
          py={(theme) => (theme.breakpoints.down('sm') ? 1 : 0)}
        >
          <Box px={2}>
            <Typography>{selectedClientUserIds.length}件選択中</Typography>
          </Box>
          <Button variant='outlined' onClick={handleCancel}>
            キャンセル
          </Button>
          <LoadingButton
            loading={isCreateLoading}
            variant='contained'
            disabled={selectedClientUserIds.length === 0}
            disableElevation
            onClick={handleOnFormSubmit}
          >
            追加
          </LoadingButton>
        </Stack>
      </DialogTitle>
      <DialogContent sx={{ display: 'flex', overflow: 'visible' }}>
        <DatalistLayout
          sx={(theme) => ({ [theme.breakpoints.down('lg')]: { alignContent: 'baseline' } })}
        >
          <DatalistLayout.ControlIsland>
            <Box>
              <SimpleSearchField
                {...register}
                fullWidth
                label='ID、ユーザーネーム、氏名、または、Emailで検索'
                name='simple-project-search'
              />
            </Box>
          </DatalistLayout.ControlIsland>
          <DatalistLayout.DataIsland>
            <EcoisSimpleDataTable
              heightAdjuster={{
                value: -parseInt(theme.spacing(5).replace('px', ''), 10),
                unit: 'px',
              }}
              data={filteredProjects}
              tableHeadRow={
                <>
                  <TableCell sx={{ width: 0 }}>
                    <CLCheckbox
                      checked={selectedClientUserIds.length === filteredProjects.length}
                      onChange={handleFullCheckOnChange}
                      indeterminate={isIndeterminate}
                    />
                  </TableCell>
                  <TableCell sx={{ width: 0, whiteSpace: 'nowrap' }}>ID</TableCell>
                  <TableCell>ユーザーネーム</TableCell>
                  <TableCell>氏名</TableCell>
                  <TableCell>Email</TableCell>
                </>
              }
              rowProps={(clientUser) => ({
                onClick: handleListItemOnClick(clientUser),
              })}
              tableBodyRow={(clientUser) => (
                <>
                  <TableCell sx={{ width: 0 }}>
                    <CLCheckbox
                      onChange={handleCheckOnChange(clientUser)}
                      checked={selectedClientUserIds.includes(clientUser.id)}
                    />
                  </TableCell>
                  <TableCell sx={{ width: 0 }}>{clientUser.id}</TableCell>
                  <TableCell>{clientUser.username}</TableCell>
                  <TableCell>{clientUser.name}</TableCell>
                  <TableCell>{clientUser.email}</TableCell>
                </>
              )}
              isEmpty={state.isSearchResultEmpty}
              isProgress={state.isTableLoading}
            />
          </DatalistLayout.DataIsland>
        </DatalistLayout>
      </DialogContent>
    </EcoisDialog>
  );
}

export { useCreateProjectGroupClientUserPermissionsDialog };
