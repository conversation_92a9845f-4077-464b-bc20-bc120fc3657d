import { Box, Paper, Typography } from '@mui/material';
import { useState } from 'react';
import { useDeleteProjectGroupMember } from '@/api';
import { DeleteProjectGroupMemberResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { ProjectGroup, ProjectOverview } from '@/types';

type DeleteProjectGroupMemberDialogProps = {
  projectGroupId: ProjectGroup['id'];
  project: ProjectOverview;
  close: () => void;
  openState: boolean;
  onSuccess?: (response: DeleteProjectGroupMemberResponse) => void;
};
function useDeleteProjectGroupMemberDialog() {
  const { open: openDialog, close, openState } = useDialogState();

  const [project, setProject] = useState<ProjectOverview>({} as ProjectOverview);

  function open(project: ProjectOverview) {
    if (project === undefined) {
      throw new Error('Project is undefined');
    }
    setProject(project);
    openDialog();
  }

  const props = {
    project,
    openState,
    close,
  };

  return {
    DeleteProjectGroupMemberDialog,
    open,
    props,
  };
}

function DeleteProjectGroupMemberDialog({
  projectGroupId,
  project,
  onSuccess,
  ...props
}: DeleteProjectGroupMemberDialogProps) {
  const { trigger: deleteProjectGroupMember, isLoading } = useDeleteProjectGroupMember();

  return (
    <DeleteDialog
      {...props}
      title={`案件グループから案件を除外します`}
      confirmText='実行すると、この案件グループ内のクライアントユーザーは対象の案件を閲覧不可となります'
      isLoading={isLoading}
      onSubmit={async () => {
        if (project === undefined) {
          return Promise.reject('Project is undefined');
        }
        try {
          const response = await deleteProjectGroupMember({
            urlParameter: {
              projectGroupId: projectGroupId,
              projectId: project.id,
            },
          });
          if (response) {
            onSuccess?.(response);
          }
        } catch (e) {
          return Promise.reject(e);
        }
      }}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象の案件</Typography>
            <Typography variant='body1'>案件名: {project?.name}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteProjectGroupMemberDialog };
