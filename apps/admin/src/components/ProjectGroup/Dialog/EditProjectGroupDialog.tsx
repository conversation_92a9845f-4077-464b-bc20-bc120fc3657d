import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  Stack,
  TextField,
  DialogActions,
  Button,
} from '@mui/material';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useUpdateProjectGroup } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ProjectGroup } from '@/types';

function useEditProjectGroupDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { EditProjectGroupDialog, props, open };
}

const schema = yup.object().shape({
  name: yup.string().required('案件グループ名を入力してください'),
  detail: yup.string().nullable(),
});
type EditProjectGroupDialogProps = {
  onSuccess?: (projectGroup: ProjectGroup) => void;
  onCancel?: () => void;
  projectGroup: ProjectGroup;
} & HooksDialogProps;

function EditProjectGroupDialog({
  openState,
  close,
  onSuccess,
  onCancel,
  projectGroup,
}: EditProjectGroupDialogProps) {
  const {
    trigger: editProjectGroup,
    isLoading: isCreating,
    reset: swrReset,
  } = useUpdateProjectGroup();

  const {
    control,
    reset: formReset,
    formState: { isValid, errors, isDirty },
    handleSubmit,
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      name: projectGroup.name,
      detail: projectGroup.detail,
    },
  });

  function handleCancel() {
    formReset();
    onCancel?.();
    close();
  }

  useEffect(() => {
    formReset(projectGroup);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectGroup]);

  const handleOnFormSubmit = handleSubmit(async (data) => {
    const response = await editProjectGroup({
      urlParameter: {
        projectGroupId: projectGroup.id,
      },
      body: {
        name: data.name,
        detail: data.detail,
      },
    });
    if (response) {
      onSuccess?.(response.projectGroup);
      close();
      swrReset();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>案件グループ編集</DialogTitle>
      <DialogContent>
        <DialogContentText>案件グループ名を入力してください</DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='案件グループ名(必須)'
                error={!!errors.name}
                helperText={errors.name?.message}
              />
            )}
          />
          <Controller
            name='detail'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='詳細説明'
                variant='outlined'
                fullWidth
                error={!!errors.detail}
                helperText={errors.detail?.message}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          loading={isCreating}
          variant='contained'
          disabled={!isValid || !isDirty}
          onClick={handleOnFormSubmit}
        >
          編集
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useEditProjectGroupDialog, EditProjectGroupDialog };
