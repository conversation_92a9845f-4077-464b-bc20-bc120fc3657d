import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  DialogTitle,
  DialogContent,
  DialogContentText,
  Stack,
  TextField,
  DialogActions,
  Button,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useCreateProjectGroup } from '@/api';
import { EcoisDialog } from '@/components/Dialog';
import { HooksDialogProps, useDialogState } from '@/hooks';
import { ProjectGroup } from '@/types';

function useCreateProjectGroupDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { CreateProjectGroupDialog, props, open };
}

const schema = yup.object().shape({
  name: yup.string().required('案件グループ名を入力してください'),
  detail: yup.string().nullable(),
});
type CreateProjectGroupDialogProps = {
  onSuccess?: (projectGroup: ProjectGroup) => void;
  onCancel?: () => void;
} & HooksDialogProps;

function CreateProjectGroupDialog({
  openState,
  close,
  onSuccess,
  onCancel,
}: CreateProjectGroupDialogProps) {
  const {
    trigger: createProjectGroup,
    isLoading: isCreating,
    reset: swrReset,
  } = useCreateProjectGroup();

  const {
    control,
    reset: formReset,
    formState: { isValid, errors },
    handleSubmit,
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      detail: '',
    },
  });

  function handleCancel() {
    formReset();
    onCancel?.();
    close();
  }

  const handleOnFormSubmit = handleSubmit(async (data) => {
    const response = await createProjectGroup({
      body: {
        name: data.name,
        detail: data.detail, //nullも含める
      },
    });
    if (response) {
      onSuccess?.(response.projectGroup);
      close();
      swrReset();
    }
  });

  return (
    <EcoisDialog open={openState} close={close} fullWidth maxWidth='xs'>
      <DialogTitle>案件グループ作成</DialogTitle>
      <DialogContent>
        <DialogContentText>案件グループ名を入力してください</DialogContentText>
        <Stack py={2} spacing={2}>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='案件グループ名(必須)'
                error={!!errors.name}
                helperText={errors.name?.message}
              />
            )}
          />
          <Controller
            name='detail'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label='詳細説明'
                variant='outlined'
                fullWidth
                error={!!errors.detail}
                helperText={errors.detail?.message}
              />
            )}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' onClick={handleCancel}>
          キャンセル
        </Button>
        <LoadingButton
          loading={isCreating}
          variant='contained'
          disabled={!isValid}
          onClick={handleOnFormSubmit}
        >
          作成
        </LoadingButton>
      </DialogActions>
    </EcoisDialog>
  );
}
export { useCreateProjectGroupDialog, CreateProjectGroupDialog };
