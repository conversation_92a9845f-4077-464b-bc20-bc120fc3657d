import { useUpsertProjectGroupMembers } from '@/api';
import { UpsertProjectGroupMembersResponse } from '@/api/types';
import { ProjectListDialog, ProjectListDialogProps } from '@/components/ProjectListDialog';
import { useDialogState } from '@/hooks';

function useCreateProjectGroupMemberDialog() {
  const { open, openState, close } = useDialogState();

  const props = {
    openState,
    close,
  };

  return { CreateProjectGroupMemberDialog, props, open };
}
type CreateProjectGroupMemberDialogProps = {
  onSuccess?: (response: UpsertProjectGroupMembersResponse) => void;
  projectGroupId: number;
};

function CreateProjectGroupMemberDialog({
  projectGroupId,
  onSuccess,
  ...props
}: CreateProjectGroupMemberDialogProps & ProjectListDialogProps) {
  const { trigger } = useUpsertProjectGroupMembers();

  async function createProjectGroupMember(selectedProjectIds: number[]) {
    try {
      const response = await trigger({
        urlParameter: { projectGroupId },
        body: {
          projectIds: selectedProjectIds,
        },
      });
      if (response) {
        onSuccess?.(response);
        props.close();
      }
    } catch (error) {
      console.log(error);
    }
  }

  return <ProjectListDialog {...props} onSubmit={createProjectGroupMember} />;
}

export { useCreateProjectGroupMemberDialog };
