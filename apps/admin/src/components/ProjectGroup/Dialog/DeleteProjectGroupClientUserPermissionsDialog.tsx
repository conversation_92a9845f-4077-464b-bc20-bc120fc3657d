import { Box, Paper, Typography } from '@mui/material';
import { useState } from 'react';
import { useDeleteProjectGroupClientUserPermission } from '@/api';
import { DeleteProjectGroupClientUserPermissionResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { ProjectGroup, ProjectGroupClientUser } from '@/types';

type DeleteProjectGroupClientUserPermissionDialogProps = {
  projectGroupId: ProjectGroup['id'];
  clientUser: ProjectGroupClientUser;
  close: () => void;
  openState: boolean;
  onSuccess?: (response: DeleteProjectGroupClientUserPermissionResponse) => void;
};
function useDeleteProjectGroupClientUserPermissionDialog() {
  const { open: openDialog, close, openState } = useDialogState();

  const [clientUser, setProject] = useState<ProjectGroupClientUser>({} as ProjectGroupClientUser);

  function open(clientUser: ProjectGroupClientUser) {
    if (clientUser === undefined) {
      throw new Error('Project is undefined');
    }
    setProject(clientUser);
    openDialog();
  }

  const props = {
    clientUser,
    openState,
    close,
  };

  return {
    DeleteProjectGroupClientUserPermissionDialog,
    open,
    props,
  };
}

function DeleteProjectGroupClientUserPermissionDialog({
  projectGroupId,
  clientUser,
  onSuccess,
  ...props
}: DeleteProjectGroupClientUserPermissionDialogProps) {
  const { trigger: deleteClientUserPermission, isLoading } =
    useDeleteProjectGroupClientUserPermission();

  return (
    <DeleteDialog
      {...props}
      title={`案件グループから案件を除外します`}
      confirmText='実行すると、この案件グループ内のクライアントユーザーは対象の案件を閲覧不可となります'
      isLoading={isLoading}
      onSubmit={async () => {
        if (clientUser === undefined) {
          return Promise.reject('Project is undefined');
        }
        try {
          const response = await deleteClientUserPermission({
            urlParameter: {
              projectGroupId: projectGroupId,
              clientUserId: clientUser.id,
            },
          });
          if (response) {
            onSuccess?.(response);
          }
        } catch (e) {
          return Promise.reject(e);
        }
      }}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象のユーザー</Typography>
            <Typography variant='body1'>{clientUser?.username}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteProjectGroupClientUserPermissionDialog };
