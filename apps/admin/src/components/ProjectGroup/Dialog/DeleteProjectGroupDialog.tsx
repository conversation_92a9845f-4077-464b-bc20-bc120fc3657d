import { Box, Paper, Typography } from '@mui/material';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDeleteProjectGroup } from '@/api';
import { DeleteProjectGroupResponse } from '@/api/types';
import { DeleteDialog } from '@/components/Dialog';
import { useDialogState } from '@/hooks';
import { ProjectGroup } from '@/types';

type DeleteProjectGroupDialogProps = {
  projectGroup: ProjectGroup;
  close: () => void;
  openState: boolean;
  onSuccess?: (response: DeleteProjectGroupResponse) => void;
};
function useDeleteProjectGroupDialog() {
  const { open: openDialog, close, openState } = useDialogState();

  const [projectGroup, setProject] = useState<ProjectGroup>({} as ProjectGroup);

  function open(projectGroup: ProjectGroup) {
    if (projectGroup === undefined) {
      throw new Error('Project is undefined');
    }
    setProject(projectGroup);
    openDialog();
  }

  const props = {
    projectGroup,
    openState,
    close,
  };

  return {
    DeleteProjectGroupDialog,
    open,
    props,
  };
}

function DeleteProjectGroupDialog({
  projectGroup,
  onSuccess,
  ...props
}: DeleteProjectGroupDialogProps) {
  const navigate = useNavigate();
  const { trigger: deleteProjectGroup, isLoading } = useDeleteProjectGroup();

  return (
    <DeleteDialog
      {...props}
      title={`案件グループから案件を除外します`}
      confirmText='実行すると、この案件グループ内のクライアントユーザーは対象の案件を閲覧不可となります'
      isLoading={isLoading}
      onSubmit={async () => {
        if (projectGroup === undefined) {
          return Promise.reject('Project is undefined');
        }
        try {
          const response = await deleteProjectGroup({
            urlParameter: {
              projectGroupId: projectGroup.id,
            },
          });
          if (response) {
            onSuccess?.(response);
            navigate('/projectgroups');
          }
        } catch (e) {
          return Promise.reject(e);
        }
      }}
    >
      <Box my={2}>
        <Paper variant={'outlined'}>
          <Box p={1}>
            <Typography variant='body2'>対象の案件グループ</Typography>
            <Typography variant='body1'>{projectGroup?.name}</Typography>
          </Box>
        </Paper>
      </Box>
    </DeleteDialog>
  );
}
export { useDeleteProjectGroupDialog };
