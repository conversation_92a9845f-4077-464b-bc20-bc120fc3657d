import { PageHeader } from '../PageHeader';
import { useProjectGroup } from './Provider';

function ProjectGroupScreenHeader() {
  const { projectGroup } = useProjectGroup();

  return (
    <PageHeader
      enableCaptionMode
      contents={[
        {
          label: '案件グループ',
          href: '/projectgroups',
        },
        {
          label: projectGroup?.name || '',
          href: `/projectgroups/${projectGroup?.id}/detail`,
        },
      ]}
    />
  );
}
export { ProjectGroupScreenHeader };
