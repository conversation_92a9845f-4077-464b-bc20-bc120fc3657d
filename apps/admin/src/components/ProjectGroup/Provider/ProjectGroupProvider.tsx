import {
  PropsWithChildren,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from 'react';
import { useReadProjectGroup } from '@/api';
import { RedirectToNotFound } from '@/components/Routing';
import { ProjectGroup } from '@/types';

type ProjectGroupContext = {
  projectGroup?: ProjectGroup;
  isLoading?: boolean;
  error?: Error;
  fetchProjectGroup: (projectGroupId: ProjectGroup['id']) => void;
} | null;
const ProjectGroupContext = createContext<ProjectGroupContext>(null);

type ProjectGroupProviderProps = PropsWithChildren<{
  initialProjectGroupId: ProjectGroup['id'];
}>;
function ProjectGroupProvider({ initialProjectGroupId, children }: ProjectGroupProviderProps) {
  const { data, isLoading, trigger, error } = useReadProjectGroup();

  const fetchProjectGroup = useCallback((projectGroupId: ProjectGroup['id']) => {
    trigger({
      urlParameter: {
        projectGroupId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchProjectGroup(initialProjectGroupId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialProjectGroupId]);

  const value = useMemo(
    () => ({
      projectGroup: data?.projectGroup,
      isLoading,
      error,
      fetchProjectGroup,
    }),
    [data, error, fetchProjectGroup, isLoading],
  );

  if (error) {
    return <RedirectToNotFound error={error} />;
  }

  return <ProjectGroupContext.Provider value={value}>{children}</ProjectGroupContext.Provider>;
}

function useProjectGroup() {
  const context = useContext(ProjectGroupContext);

  if (!context) {
    throw new Error('useClientUser must be used within a ClientUserProvider');
  }
  return context;
}

export { ProjectGroupProvider, useProjectGroup };
