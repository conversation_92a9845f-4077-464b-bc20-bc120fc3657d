import { useMemo, useState, useEffect, ChangeEvent } from 'react';
import { useDebounce } from 'react-use';

function useSimpleSearch<T = any>({
  defaultValues,
  filterFunction,
  debounceMs = 100,
}: {
  defaultValues?: T[];
  filterFunction: (data: T, searchText: string) => boolean;
  debounceMs?: number;
}) {
  const memoizedDefaultValues = useMemo(() => defaultValues ?? [], [defaultValues]);
  const memoizedFilterFunction = useMemo(() => filterFunction, [filterFunction]);

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState<T[]>(defaultValues ?? []);
  const [state, setState] = useState<'pending' | 'ready'>('pending');

  useEffect(() => {
    setFilteredData(memoizedDefaultValues);
  }, [memoizedDefaultValues]);

  const [isReady] = useDebounce(
    () => {
      setFilteredData(
        memoizedDefaultValues.filter((data) => {
          return memoizedFilterFunction(data, searchText);
        }),
      );
    },
    debounceMs,
    [searchText, defaultValues],
  );

  // 入力があったらすぐpendingにする
  useEffect(() => {
    setState('pending');
  }, [searchText]);

  const isFinished = isReady();

  useEffect(() => {
    if (isFinished) {
      setState('ready');
    } else {
      setState('pending');
    }
  }, [isFinished]);

  function control(event: ChangeEvent<HTMLInputElement>) {
    setSearchText(event.target.value);
  }

  function reset() {
    setSearchText('');
  }

  const register = {
    control,
    searchText,
    reset,
  };

  return { data: filteredData, state, register };
}

export { useSimpleSearch };
