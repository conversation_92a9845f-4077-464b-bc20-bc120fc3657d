import { Close } from '@mui/icons-material';
import { TextFieldProps, TextField, InputAdornment, IconButton } from '@mui/material';
import { ChangeEvent, useState } from 'react';

function SimpleSearchField({
  control,
  searchText,
  reset,
  ...props
}: TextFieldProps & {
  control: (event: ChangeEvent<HTMLInputElement>) => void;
  searchText: string;
  reset: () => void;
}) {
  const [formValue, setFormValue] = useState('');

  function handleOnChange(event: ChangeEvent<HTMLInputElement>) {
    control(event);
    setFormValue(event.target.value);
    props.onChange?.(event);
  }

  function handleIconOnClick() {
    setFormValue('');
    reset();
  }

  return (
    <TextField
      {...props}
      value={formValue}
      onChange={handleOnChange}
      InputProps={
        searchText.length > 0
          ? {
              endAdornment: (
                <InputAdornment position='end'>
                  <IconButton onClick={handleIconOnClick}>
                    <Close />
                  </IconButton>
                </InputAdornment>
              ),
            }
          : {}
      }
    />
  );
}

export { SimpleSearchField };
