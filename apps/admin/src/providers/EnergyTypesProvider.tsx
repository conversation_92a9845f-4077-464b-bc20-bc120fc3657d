import { PropsWithChildren, createContext, useContext } from 'react';
import { useEffectOnce } from 'react-use';
import { useListEnergyTypes } from '@/api/useListEnergyTypes';
import { EnergyType } from '@/types';

const EnergyTypesContext = createContext<EnergyTypesContextProps>({} as EnergyTypesContextProps);

type EnergyTypesContextProps = EnergyType[];

function useEnergyTypes() {
  const context = useContext(EnergyTypesContext);
  if (!context) {
    throw new Error('useEnergyTypes must be used within an EnergyTypesProvider');
  }
  return context;
}

function EnergyTypesProvider({ children }: PropsWithChildren) {
  const { trigger, data } = useListEnergyTypes();

  useEffectOnce(() => {
    trigger();
  });

  // CreatedAt, UpdatedAtを削除
  const value: EnergyTypesContextProps =
    data?.energyTypes.map((energyType) => ({
      id: energyType.id,
      name: energyType.name,
      unit: energyType.unit,
      color: energyType.color,
    })) || [];

  return <EnergyTypesContext.Provider value={value}>{children}</EnergyTypesContext.Provider>;
}

export { EnergyTypesProvider, useEnergyTypes };
