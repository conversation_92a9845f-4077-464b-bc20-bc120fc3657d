import { PropsWithChildren, createContext, useContext, useMemo } from 'react';
import { RedirectToNotFound } from '@/components/Routing';
import { Project } from '@/types';

const ProjectContext = createContext<ProjectContextProps>({} as ProjectContextProps);

type ProjectContextProps = {
  project: Project | undefined;
  isLoading: boolean;
  error?: any;
  fetch: (projectId: Project['id']) => void;
};

function useProject() {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
}

function ProjectProvider({
  children,
  project,
  isLoading,
  error,
  fetchFn,
}: PropsWithChildren<{
  project: Project | undefined;
  isLoading: boolean;
  error: any;
  fetchFn: (projectId: Project['id']) => void;
}>) {
  const value = useMemo(
    () => ({
      project,
      isLoading,
      error,
      fetch: fetchFn,
    }),
    [project, isLoading, error, fetchFn],
  );

  if (error) {
    return <RedirectToNotFound error={error} />;
  }

  return <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>;
}

export { ProjectProvider, useProject };
