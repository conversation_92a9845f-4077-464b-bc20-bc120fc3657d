import { <PERSON>, Button, Chip, Stack, Typography } from '@mui/material';
import { useAdminUserAvailableDialog } from '@/components/AdminUser/AdminUserAvailableDialog';
import { useAdminUserDeleteDialog } from '@/components/AdminUser/AdminUserDeleteDialog';
import { useAdminUserPasswordChangeDialog } from '@/components/AdminUser/AdminUserPasswordChangeDialog';
import { InfoCard, InfoCardContent, InfoCardHeader } from '@/components/InfoCard';
import { AdminUser } from '@/types';

function AdminUserSettingScreen({
  adminUser,
  onChangeAvailable,
}: {
  adminUser: AdminUser | undefined;
  onChangeAvailable: (adminUser: AdminUser) => void;
}) {
  function handleSubmit(adminUser: AdminUser) {
    onChangeAvailable(adminUser);
  }

  const {
    AdminUserAvailableDialog,
    props: availableDialogProps,
    open: openAvailableDialog,
  } = useAdminUserAvailableDialog();

  const {
    AdminUserPasswordChangeDialog,
    props: PasswordChangeDialogProps,
    open: openPasswordChangeDialog,
  } = useAdminUserPasswordChangeDialog();

  const {
    AdminUserDeleteDialog,
    props: deleteDialogProps,
    open: openDeleteDialog,
  } = useAdminUserDeleteDialog();

  return (
    <>
      <Stack spacing={4}>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                disableElevation
                onClick={() => {
                  openAvailableDialog();
                }}
              >
                編集
              </Button>
            }
          >
            アドミンユーザー有効状態
          </InfoCardHeader>
          <InfoCardContent>
            {adminUser?.isAvailable ? (
              <Chip label='有効' color='primary' />
            ) : (
              <Chip label='無効' color='error' />
            )}
          </InfoCardContent>
        </InfoCard>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                disableElevation
                onClick={() => {
                  openPasswordChangeDialog();
                }}
              >
                変更
              </Button>
            }
          >
            パスワード変更
          </InfoCardHeader>
          <InfoCardContent>
            <Box>
              <Typography variant='body2'>
                パスワードを変更します。変更すると以前のパスワードではログインできなくなります。
              </Typography>
            </Box>
          </InfoCardContent>
        </InfoCard>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                color='error'
                disableElevation
                onClick={() => {
                  openDeleteDialog();
                }}
              >
                削除
              </Button>
            }
          >
            アドミンユーザーを削除
          </InfoCardHeader>
          <InfoCardContent>
            <Typography variant='body2' color={'error'}>
              アドミンユーザーを削除します。データは復元できないので、注意してください。
            </Typography>
          </InfoCardContent>
        </InfoCard>
      </Stack>

      {adminUser && (
        <>
          <AdminUserAvailableDialog
            {...availableDialogProps}
            adminUser={adminUser}
            onSubmit={handleSubmit}
          />
          <AdminUserPasswordChangeDialog {...PasswordChangeDialogProps} adminUser={adminUser} />
          <AdminUserDeleteDialog {...deleteDialogProps} adminUser={adminUser} />
        </>
      )}
    </>
  );
}

export { AdminUserSettingScreen };
