import { Box, Typography, Stack } from '@mui/material';
import { ContentTitle } from '@/components/ContentTitle';
import { ReductionSetting, SummaryContentsSetting } from '@/components/SummarySetting';
import { Project } from '@/types';

function ProjectSummarySettingScreen({ projectId }: { projectId: Project['id'] }) {
  return (
    <>
      <ContentTitle id='title'>
        <ContentTitle.Label>サマリー設定</ContentTitle.Label>
      </ContentTitle>
      <Box>
        <Typography>クライアントアプリケーションのサマリーページの設定を行います。</Typography>
      </Box>
      <Stack spacing={2} my={2}>
        <ReductionSetting projectId={projectId} />
        <SummaryContentsSetting />
      </Stack>
    </>
  );
}
export { ProjectSummarySettingScreen };
