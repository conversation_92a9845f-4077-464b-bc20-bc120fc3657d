import { LoadingButton } from '@mui/lab';
import { Box, Button, CircularProgress, Typography } from '@mui/material';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useListUtilities, useUpdateUtility } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { ReductionSettingEdit } from '@/components/SummarySetting';
import { Project, Utility } from '@/types';

type ProjectSummarySettingUtilitiesEditScreenProps = { projectId: Project['id'] };
function ProjectSummarySettingUtilitiesEditScreen({
  projectId,
}: ProjectSummarySettingUtilitiesEditScreenProps) {
  const {
    trigger: fetchUtilitiesTrigger,
    data: fetchResponse,
    isLoading: isFetchUtilitiesLoading,
  } = useListUtilities();

  const { trigger: updateUtility } = useUpdateUtility();
  const navigate = useNavigate();

  useEffectOnce(() => {
    fetchUtilities();
  });

  function fetchUtilities() {
    fetchUtilitiesTrigger({
      queryParameter: {
        projectIds: [projectId],
      },
    });
  }

  const [utilitiesForSubmit, setUtilitiesForSubmit] = useState<Utility[]>();

  function handleOnChange(utilities: Utility[]) {
    setUtilitiesForSubmit(utilities);
  }

  function handleCancel() {
    window.history.back();
  }

  const [isSubmitting, setIsSubmitting] = useState(false);

  async function handleOnSubmit() {
    /**
     * Utility Update Flow
     * 1. Update all utilities order to big number, increment from biggest order
     * 2. Update order
     *
     * Thus, this update all utilities twice.
     * I adopted this flow because
     *  - utilities data size is between 1 ~ 10
     *  - payload size is small
     *  */

    if (!utilitiesForSubmit || !fetchResponse?.utilities) return;

    const original = fetchResponse.utilities;

    /**
     *  Update All items order to big number, increment from biggest order
     *  - get biggest order
     *  - increment from biggest order
     * */
    const biggestOrder = original.reduce((acc, current) => {
      return acc > current.order ? acc : current.order;
    }, 0);

    /**
     * To Avoid duplicated order, increment utilities order to biggest order + 1 temporarily
     *
     */
    const preflightUtilities = original.map((utility, index) => {
      return {
        ...utility,
        order: biggestOrder + index + 1,
      };
    });

    try {
      setIsSubmitting(true);
      const preflight = await Promise.allSettled(
        preflightUtilities.map((u) =>
          updateUtility({ urlParameter: { utilityId: u.id }, body: { order: u.order } }),
        ),
      );

      const response = await Promise.allSettled(
        utilitiesForSubmit.map((u) =>
          updateUtility({
            urlParameter: { utilityId: u.id },
            body: {
              nodeId: u.nodeId,
              order: u.order,
              isEnableSummary: u.isEnableSummary,
            },
          }),
        ),
      );

      if (response || preflight) {
        navigate(`/projects/${projectId}/summary`);
      }
      setIsSubmitting(false);
    } catch (e) {
      // TODO: Error handling
      console.log(e);
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <ContentTitle id='title'>
        <ContentTitle.Label>サマリー設定 / 速報値(削減量)の設定</ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button variant='outlined' onClick={handleCancel}>
            キャンセル
          </Button>
          <LoadingButton variant={'contained'} onClick={handleOnSubmit} loading={isSubmitting}>
            確定
          </LoadingButton>
        </ContentTitle.Buttons>
      </ContentTitle>

      <Box mb={2}>
        <Typography>
          速報値として表示する削減量のデータを選択することができます。さらに、表示するユーティリティの種類も選択可能です。
        </Typography>
      </Box>
      {isFetchUtilitiesLoading && <CircularProgress />}
      {fetchResponse && (
        <ReductionSettingEdit
          utilities={fetchResponse?.utilities}
          projectId={projectId}
          onChange={handleOnChange}
        />
      )}
    </>
  );
}
export { ProjectSummarySettingUtilitiesEditScreen };
