import { <PERSON><PERSON>, <PERSON>, TableCell, Typography } from '@mui/material';
import { useEffectOnce } from 'react-use';
import { useListProjectGroupClientUserPermissions } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import {
  useCreateProjectGroupClientUserPermissionsDialog,
  useDeleteProjectGroupClientUserPermissionDialog,
} from '@/components/ProjectGroup';
import { ControlCellContainer, EcoisSimpleDataTable } from '@/components/Table';
import { ProjectGroupClientUser } from '@/types';

type ProjectGroupClientUserPermissionsScreenProps = { projectGroupId: number };
function ProjectGroupClientUserPermissionsScreen({
  projectGroupId,
}: ProjectGroupClientUserPermissionsScreenProps) {
  const {
    trigger: listProjectGroupMember,
    data: projectGroupClientUserPermissions,
    isLoading,
  } = useListProjectGroupClientUserPermissions();

  function fetchProjectGroupClientUserPermissions() {
    listProjectGroupMember({ urlParameter: { projectGroupId } });
  }

  useEffectOnce(() => {
    fetchProjectGroupClientUserPermissions();
  });

  const {
    CreateProjectGroupClientUserPermissionsDialog,
    props: createDialogProps,
    open: openCreateDialog,
  } = useCreateProjectGroupClientUserPermissionsDialog();

  function handleCreateButtonClick() {
    openCreateDialog();
  }

  const {
    DeleteProjectGroupClientUserPermissionDialog,
    open: openDeleteDialog,
    props: deleteDialogProps,
  } = useDeleteProjectGroupClientUserPermissionDialog();

  function handleDeleteClick(clientUser: ProjectGroupClientUser) {
    return () => openDeleteDialog(clientUser);
  }

  function handleDeleteOnSuccess() {
    fetchProjectGroupClientUserPermissions();
  }

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>
          <Typography component='span'>グループに所属しているクライアントユーザー</Typography>
        </ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button variant='contained' disableElevation onClick={handleCreateButtonClick}>
            ユーザーを追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>
      <EcoisSimpleDataTable
        data={projectGroupClientUserPermissions?.clientUsers}
        tableHeadRow={
          <>
            <TableCell>ID</TableCell>
            <TableCell>ユーザーネーム</TableCell>
            <TableCell>氏名</TableCell>
            <TableCell>Email</TableCell>
            <TableCell>有効状態</TableCell>
          </>
        }
        tableBodyRow={(clientUser) => (
          <>
            <TableCell sx={{ whiteSpace: 'nowrap', width: 0 }}>{clientUser.id}</TableCell>
            <TableCell
              sx={{
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 0,
                width: '30%',
                overflow: 'hidden',
              }}
            >
              {clientUser.name}
            </TableCell>
            <TableCell>{clientUser.username}</TableCell>
            <TableCell>{clientUser.email}</TableCell>
            <TableCell>{clientUser.isAvailable ? '有効' : '無効'}</TableCell>
          </>
        )}
        controlCell={(clientUser) => (
          <ControlCellContainer>
            <Button LinkComponent={Link} href={`/clientusers/${clientUser.id}/detail`}>
              詳細
            </Button>
            <Button onClick={handleDeleteClick(clientUser)}>グループから除く</Button>
          </ControlCellContainer>
        )}
        isEmpty={projectGroupClientUserPermissions?.clientUsers?.length === 0}
        isProgress={isLoading}
      />
      <CreateProjectGroupClientUserPermissionsDialog
        {...createDialogProps}
        onSuccess={fetchProjectGroupClientUserPermissions}
        projectGroupId={projectGroupId}
      />
      {projectGroupClientUserPermissions?.clientUsers && (
        <DeleteProjectGroupClientUserPermissionDialog
          projectGroupId={projectGroupId}
          onSuccess={handleDeleteOnSuccess}
          {...deleteDialogProps}
        />
      )}
    </>
  );
}
export { ProjectGroupClientUserPermissionsScreen };
