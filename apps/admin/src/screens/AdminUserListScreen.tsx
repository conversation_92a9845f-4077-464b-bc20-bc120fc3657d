import {
  Box,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Button,
  Stack,
  LinearProgress,
} from '@mui/material';
import { useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useListAdminUsers } from '@/api';
import { useAdminUserAddDialog } from '@/components/AdminUser/AdminUserAddDialog';
import { PageHeader } from '@/components/PageHeader';
import { SimpleSearchField, useSimpleSearch } from '@/components/SimpleSearch';
import { EcoisTableContainer } from '@/components/Table';
import { DatalistLayout } from '@/layout';
import { AdminUser } from '@/types';

const iconButtonVisibilityToggleStyle = {
  '& .MuiIconButton-root': {
    visibility: 'hidden',
  },
  '&:hover .MuiIconButton-root': {
    visibility: 'visible',
  },
};

// TODO コンポート分割を行う
function AdminUserListScreen() {
  const navigate = useNavigate();

  const {
    data: adminUsers,
    isLoading: adminUsersIsLoading,
    trigger: adminUsersTrigger,
  } = useListAdminUsers();

  const {
    AdminUserAddDialog,
    open: openAddDialog,
    props: addDialogProps,
  } = useAdminUserAddDialog();

  const location = useLocation();
  const isAddLocation = location.pathname.includes('/adminusers/add');

  useEffect(() => {
    if (isAddLocation) {
      openAddDialog();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddLocation]);

  useEffectOnce(() => {
    fetchAdminUsers();
  });

  function fetchAdminUsers() {
    adminUsersTrigger({});
  }

  const {
    data: filteredList,
    register,
    state: searchState,
  } = useSimpleSearch<AdminUser>({
    defaultValues: adminUsers?.adminUsers,
    filterFunction: (adminUser, searchText) => {
      return (
        adminUser?.username?.includes(searchText) ||
        adminUser?.email?.includes(searchText) ||
        adminUser?.name?.includes(searchText)
      );
    },
  });

  const state = useMemo(
    () => ({
      isEmptyData: adminUsers?.adminUsers.length === 0,
      isTableLoading: adminUsersIsLoading || searchState === 'pending',
      isSearchResultEmpty:
        adminUsers?.adminUsers && filteredList.length === 0 && searchState === 'ready',
    }),
    [adminUsers, adminUsersIsLoading, filteredList, searchState],
  );

  return (
    <>
      <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
        <PageHeader contents={[{ label: 'アドミンユーザー' }]} />
        <Box>
          <Button
            variant={'contained'}
            onClick={() => {
              openAddDialog();
            }}
          >
            新規作成
          </Button>
        </Box>
      </Stack>
      {state.isEmptyData ? (
        <Typography>登録されているアドミンユーザーは0件です。</Typography>
      ) : (
        <DatalistLayout>
          <DatalistLayout.ControlIsland>
            <SimpleSearchField
              {...register}
              name='adminuser-search'
              label='ユーザーネーム,Email,名前で検索'
              fullWidth
            />
          </DatalistLayout.ControlIsland>
          <DatalistLayout.DataIsland>
            <EcoisTableContainer>
              {state.isTableLoading && (
                <Box position={'absolute'} width={'100%'} zIndex={99999}>
                  <LinearProgress />
                </Box>
              )}
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '10%' }}>ID</TableCell>
                    <TableCell>氏名</TableCell>
                    <TableCell>ユーザーネーム</TableCell>
                    <TableCell>メールアドレス</TableCell>
                    <TableCell>有効状態</TableCell>
                    <TableCell>権限</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {state.isSearchResultEmpty && (
                    <TableRow>
                      <TableCell colSpan={5}>案件が見つかりませんでした。</TableCell>
                    </TableRow>
                  )}
                  {filteredList.map((user) => (
                    <TableRow
                      hover
                      key={user.id}
                      sx={{
                        textDecoration: 'none',
                        cursor: 'pointer',
                        ...iconButtonVisibilityToggleStyle,
                      }}
                      onClick={() => {
                        navigate(`/adminusers/${user.id}/detail`);
                      }}
                    >
                      <TableCell sx={{ width: '10%' }}>{user.id}</TableCell>
                      <TableCell sx={{ width: '15%' }}>{user.name ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '15%' }}>{user.username ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '20%' }}>{user.email ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '10%' }}>
                        {user.isAvailable ? '有効' : '無効'}
                      </TableCell>
                      <TableCell sx={{ width: '10%' }}>{user.role}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </EcoisTableContainer>
          </DatalistLayout.DataIsland>
        </DatalistLayout>
      )}
      <AdminUserAddDialog
        {...addDialogProps}
        onCancel={() => {
          navigate('/adminusers', { replace: true });
        }}
        onClose={(adminUser) => {
          navigate(
            `/adminusers/${adminUser?.id}/detail`,
            isAddLocation ? { replace: true } : undefined,
          );
        }}
      />
    </>
  );
}

export { AdminUserListScreen };
