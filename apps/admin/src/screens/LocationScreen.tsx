import { Paper, Box } from '@mui/material';
import { DatapointColumn } from '../components/Datapoint/DatapointColumn';
import { LocationColumn } from '../components/Location/LocationColumn';
import { UnitColumn } from '../components/Unit/UnitColumn';
import { MultiColumnView } from '@/components/ColumnList/context/MultiColumViewContext';
import { PageHeader } from '@/components/PageHeader';
import { useViewportTopToBottom } from '@/hooks';

function LocationScreen() {
  const height = useViewportTopToBottom('header');

  return (
    <>
      <Box id='header'>
        <PageHeader
          contents={[
            {
              label: 'ロケーション',
            },
          ]}
        />
      </Box>
      <Paper
        sx={{
          height: (theme) => `calc(100vh - ${height}px - ${theme.spacing(2)})`,
        }}
      >
        <MultiColumnView>
          <LocationColumn mode='edit' />
          <UnitColumn mode='edit' />
          <DatapointColumn mode='edit' checkboxSelection />
        </MultiColumnView>
      </Paper>
    </>
  );
}

export { LocationScreen };
