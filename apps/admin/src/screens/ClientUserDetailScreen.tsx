import { Box, Button } from '@mui/material';
import { ClientUserDetailInfo } from '@/components/ClientUser/ClientUserDetailInfo';
import { useClientUserEditDialog } from '@/components/ClientUser/ClientUserEditDialog';
import { InfoCard, InfoCardContent, InfoCardHeader } from '@/components/InfoCard';
import { ClientUser } from '@/types';

function ClientUserDetailScreen({
  clientUser,
  isLoading,
  error,
  onEdit,
}: {
  clientUser: ClientUser | undefined;
  isLoading: boolean;
  error?: any;
  onEdit: (clientUser: ClientUser) => void;
}) {
  const {
    ClientUserEditDialog,
    open: openEditDialog,
    props: editDialogProps,
  } = useClientUserEditDialog();

  return (
    <Box maxWidth={'sm'}>
      <InfoCard>
        <InfoCardHeader
          actions={
            <Button
              disabled={isLoading || !!error || clientUser === undefined}
              variant={'contained'}
              onClick={() => {
                openEditDialog();
              }}
              disableElevation
            >
              編集
            </Button>
          }
        >
          クライアントユーザー基本情報
        </InfoCardHeader>
        <InfoCardContent>
          <ClientUserDetailInfo
            clientUser={clientUser}
            isLoading={isLoading}
            error={error}
            onAddTextButtonClick={openEditDialog}
          />
        </InfoCardContent>
      </InfoCard>

      {clientUser && (
        <ClientUserEditDialog {...editDialogProps} clientUser={clientUser} onSubmit={onEdit} />
      )}
    </Box>
  );
}

export { ClientUserDetailScreen };
