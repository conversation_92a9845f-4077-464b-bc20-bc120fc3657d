import { Tab<PERSON>ontex<PERSON>, <PERSON>b<PERSON><PERSON>, Tab<PERSON>anel } from '@mui/lab';
import { Box, Tab } from '@mui/material';
import { SyntheticEvent, useEffect, useState } from 'react';
import { useListClientUserPermissions } from '@/api';
import {
  ClientUserPermissionProjectGroups,
  ClientUserPermissionProjects,
} from '@/components/ClientPermission';

import { ClientUser } from '@/types';

type ClientUserPermissionScreenProps = {
  clientUserId: ClientUser['id'] | undefined;
};

function ClientUserPermissionScreen({ clientUserId }: ClientUserPermissionScreenProps) {
  const { trigger, data: clientUserPermissions } = useListClientUserPermissions();

  function fetchPermission(clientUserId: ClientUser['id']) {
    return trigger({
      urlParameter: {
        clientUserId,
      },
    });
  }

  useEffect(() => {
    clientUserId !== undefined && fetchPermission(clientUserId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientUserId]);

  //
  //  For Tab
  //
  const [tabIndex, setTabIndex] = useState('1');

  function handleTabClick(_: SyntheticEvent, newValue: string) {
    setTabIndex(newValue);
  }

  return (
    <>
      <Box width={'100%'}>
        <TabContext value={tabIndex}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <TabList onChange={handleTabClick}>
              <Tab label='案件' value='1' />
              <Tab label='案件グループ' value='2' />
            </TabList>
          </Box>
          <TabPanel value='1' sx={{ padding: 0, paddingTop: 2 }}>
            {clientUserId !== undefined && (
              <ClientUserPermissionProjects
                clientUserId={clientUserId}
                clientUserProjects={clientUserPermissions?.clientUserPermissions?.projects}
                onAddSuccess={() => {
                  fetchPermission(clientUserId);
                }}
                onRemoveSuccess={() => {
                  fetchPermission(clientUserId);
                }}
              />
            )}
          </TabPanel>
          <TabPanel value='2' sx={{ padding: 0, paddingTop: 2 }}>
            {clientUserId !== undefined && clientUserPermissions?.clientUserPermissions && (
              <ClientUserPermissionProjectGroups
                clientUserId={clientUserId}
                projectGroups={clientUserPermissions.clientUserPermissions.projectGroups}
                onAddSuccess={() => {
                  fetchPermission(clientUserId);
                }}
                onDeleteSuccess={() => {
                  fetchPermission(clientUserId);
                }}
              />
            )}
          </TabPanel>
        </TabContext>
      </Box>
    </>
  );
}

export { ClientUserPermissionScreen };
