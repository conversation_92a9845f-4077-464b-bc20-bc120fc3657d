import { <PERSON><PERSON>, <PERSON>, TableCell, Typography } from '@mui/material';
import { useEffectOnce } from 'react-use';
import { AvailableIcon, NotAvailableIcon } from '../components/Project/IsAvailableIcons';
import { useReadProjectGroup } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import {
  useCreateProjectGroupMemberDialog,
  useDeleteProjectGroupMemberDialog,
} from '@/components/ProjectGroup';
import { ControlCellContainer, EcoisSimpleDataTable } from '@/components/Table';
import { ProjectOverview } from '@/types';

type ProjectGroupMembersScreenProps = { projectGroupId: number };
function ProjectGroupMembersScreen({ projectGroupId }: ProjectGroupMembersScreenProps) {
  const { trigger: readProjectGroup, data: projectGroupRes, isLoading } = useReadProjectGroup();

  function fetchProjectGroupMembers() {
    readProjectGroup({ urlParameter: { projectGroupId } });
  }

  useEffectOnce(() => {
    fetchProjectGroupMembers();
  });

  const {
    CreateProjectGroupMemberDialog,
    props: createDialogProps,
    open: openCreateDialog,
  } = useCreateProjectGroupMemberDialog();

  function handleCreateButtonClick() {
    openCreateDialog();
  }

  const {
    DeleteProjectGroupMemberDialog,
    open: openDeleteDialog,
    props: deleteDialogProps,
  } = useDeleteProjectGroupMemberDialog();

  function handleDeleteClick(project: ProjectOverview) {
    return () => openDeleteDialog(project);
  }

  function handleDeleteOnSuccess() {
    fetchProjectGroupMembers();
  }

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>
          <Typography component='span'>グループに所属している案件</Typography>
        </ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button variant='contained' disableElevation onClick={handleCreateButtonClick}>
            案件を追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>

      <EcoisSimpleDataTable
        data={projectGroupRes?.projectGroup?.projects}
        tableHeadRow={
          <>
            <TableCell>ID</TableCell>
            <TableCell>案件名</TableCell>
          </>
        }
        keyProp={'id'}
        tableBodyRow={(project) => (
          <>
            <TableCell sx={{ whiteSpace: 'nowrap', width: 0 }}>{project.id}</TableCell>
            <TableCell sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {project.isAvailable ? <AvailableIcon /> : <NotAvailableIcon />}
              {project.name}
            </TableCell>
          </>
        )}
        controlCell={(project) => (
          <ControlCellContainer>
            <Button LinkComponent={Link} href={`/projects/${project.id}/detail`}>
              詳細
            </Button>
            <Button onClick={handleDeleteClick(project)}>グループから除く</Button>
          </ControlCellContainer>
        )}
        isEmpty={projectGroupRes?.projectGroup?.projects.length === 0 || !projectGroupRes}
        isProgress={isLoading}
      />
      <CreateProjectGroupMemberDialog
        {...createDialogProps}
        clientUserProjects={[]}
        onSuccess={fetchProjectGroupMembers}
        projectGroupId={projectGroupId}
      />
      {projectGroupRes?.projectGroup && (
        <DeleteProjectGroupMemberDialog
          projectGroupId={projectGroupId}
          onSuccess={handleDeleteOnSuccess}
          {...deleteDialogProps}
        />
      )}
    </>
  );
}
export { ProjectGroupMembersScreen };
