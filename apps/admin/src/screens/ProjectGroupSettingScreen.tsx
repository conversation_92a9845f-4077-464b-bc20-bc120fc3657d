import { Stack, Button, Typography } from '@mui/material';
import { InfoCard, InfoCardHeader, InfoCardContent } from '@/components/InfoCard';
import { useDeleteProjectGroupDialog } from '@/components/ProjectGroup';
import { ProjectGroup } from '@/types';

function ProjectGroupSettingScreen({ projectGroup }: { projectGroup: ProjectGroup | undefined }) {
  const { DeleteProjectGroupDialog, open, props } = useDeleteProjectGroupDialog();

  return (
    <>
      <Stack spacing={4} maxWidth={'sm'}>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                color='error'
                disableElevation
                onClick={() => {
                  if (projectGroup) {
                    open(projectGroup);
                  }
                }}
              >
                削除
              </Button>
            }
          >
            案件グループを削除
          </InfoCardHeader>
          <InfoCardContent>
            <Typography variant='body2' color={'error'}>
              案件グループを削除します。削除後はこの案件グループに紐づいているクライアントユーザーは、案件にアクセスできなくなることに注意してください。
            </Typography>
          </InfoCardContent>
        </InfoCard>
      </Stack>

      {projectGroup && <DeleteProjectGroupDialog {...props} />}
    </>
  );
}

export { ProjectGroupSettingScreen };
