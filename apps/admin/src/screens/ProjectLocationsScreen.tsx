import { Box, Typography } from '@mui/material';
import { useState } from 'react';
import { useEffectOnce } from 'react-use';
import { useDeleteProjectLocation, useListProjectLocations } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { useDeleteDialog } from '@/components/Dialog/DeleteDialog';
import { ProjectLocationsAddButton } from '@/components/ProjectLocations/ProjectLocationsAddButton';
import { useProjectLocationsAddDialog } from '@/components/ProjectLocations/ProjectLocationsAddDialog';
import { ProjectLocationContents } from '@/components/ProjectLocations/ProjectLocationsContents';
import { ProjectLocationsList } from '@/components/ProjectLocations/ProjectLocationsList';
import { Location } from '@/types';

function ProjectLocationsScreen({ projectId }: { projectId: number }) {
  const {
    data: projectLocations,
    error: projectLocationsError,
    isLoading: projectLocationsIsLoading,
    trigger: projectLocationsTrigger,
  } = useListProjectLocations();

  const { ProjectLocationsAddDialog, open, props: AddDialogProps } = useProjectLocationsAddDialog();
  const { DeleteDialog, props: DeleteDialogProps, open: openDeleteDialog } = useDeleteDialog();
  const [checkedLocation, setCheckedLocation] = useState<Location | undefined>(undefined);

  const { trigger: deleteProjectLocationTrigger, isLoading: isDeleting } =
    useDeleteProjectLocation();

  function fetchProjectLocationList(projectId: number) {
    projectLocationsTrigger({
      urlParameter: {
        projectId: Number(projectId),
      },
    });
  }

  async function deleteProjectLocation() {
    try {
      if (checkedLocation) {
        await deleteProjectLocationTrigger({
          urlParameter: {
            projectId: projectId,
            locationId: checkedLocation.id,
          },
        });
      }
      fetchProjectLocationList(projectId);
    } catch (e) {
      Promise.reject(e);
    }
  }

  useEffectOnce(() => {
    fetchProjectLocationList(projectId);
  });

  function handleOnDeleteClick(location: Location) {
    setCheckedLocation(location);
    openDeleteDialog();
  }

  function handleOnAddSubmit() {
    fetchProjectLocationList(projectId);
  }

  return (
    <>
      <ContentTitle id='title'>
        <ContentTitle.Label>ロケーション設定</ContentTitle.Label>
        <ContentTitle.Buttons>
          <ProjectLocationsAddButton onClick={open} disabled={projectLocationsIsLoading} />
        </ContentTitle.Buttons>
      </ContentTitle>
      <ProjectLocationContents
        data={projectLocations?.projectLocations}
        error={projectLocationsError}
        isLoading={projectLocationsIsLoading}
      >
        <ProjectLocationsList
          data={projectLocations?.projectLocations}
          onDeleteClick={handleOnDeleteClick}
        />
      </ProjectLocationContents>
      <ProjectLocationsAddDialog
        onSubmit={handleOnAddSubmit}
        projectId={projectId}
        {...AddDialogProps}
      />
      <DeleteDialog
        {...DeleteDialogProps}
        title={`「${checkedLocation?.name}」 と案件の関連付けを解除しますか？`}
        confirmText='関連付けを解除すると、ロケーションに紐づく案件の情報は削除されます。'
        labelText='解除する'
        buttonText='解除'
        isLoading={isDeleting}
        onSubmit={deleteProjectLocation}
      >
        <Box py={2}>
          <Typography variant='body2' color={'attention'}>
            ※グラフ表示設定でロケーションのデータポイントが設定されている場合は関連を解除できません。先にデータポイントとノードの紐づきを解除してください
          </Typography>
        </Box>
      </DeleteDialog>
    </>
  );
}

export { ProjectLocationsScreen };
