import { Stack } from '@mui/material';
import { ContentTitle } from '@/components/ContentTitle';
import {
  ProjectAvailabilityPanel,
  ProjectKenesPanel,
  ProjectOverviewPanel,
  useProjectEditDialog,
} from '@/components/Project';
import { useProjectAvailabilityEditDialog } from '@/components/Project/Dialog/useProjectAvailabilityEditDialog';
import { useProjectKenesEditDialog } from '@/components/Project/Dialog/useProjectKenesEditDialog';
import { Project } from '@/types';

function ProjectDetailScreen({
  project,
  isLoading,
  onSubmit,
}: {
  project: Project | undefined;
  isLoading: boolean;
  onSubmit: (project: Project) => void;
  error: Error | undefined;
}) {
  // EditDialog
  const { ProjectEditDialog, open, props } = useProjectEditDialog();
  const {
    ProjectKenesEditDialog,
    open: openKenesEditDialog,
    props: kenesEditDialogProps,
  } = useProjectKenesEditDialog();
  const {
    ProjectAvailabilityEditDialog,
    open: openAvailabilityEditDialog,
    props: availabilityEditDialogProps,
  } = useProjectAvailabilityEditDialog();

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>基本情報</ContentTitle.Label>
      </ContentTitle>
      <Stack spacing={2}>
        <ProjectOverviewPanel
          project={project}
          isLoading={isLoading}
          onEditButtonClick={open}
          onAddTextButtonClick={open}
        />
        <ProjectKenesPanel
          project={project}
          isLoading={isLoading}
          onKenesEditButtonClick={openKenesEditDialog}
        />
        <ProjectAvailabilityPanel
          project={project}
          isLoading={isLoading}
          onAvailabilityChangeClick={openAvailabilityEditDialog}
        />
      </Stack>
      {project && <ProjectEditDialog {...props} project={project} onSubmit={onSubmit} />}
      {project && (
        <ProjectKenesEditDialog {...kenesEditDialogProps} project={project} onSubmit={onSubmit} />
      )}
      {project && (
        <ProjectAvailabilityEditDialog
          {...availabilityEditDialogProps}
          project={project}
          onSubmit={onSubmit}
        />
      )}
    </>
  );
}

export { ProjectDetailScreen };
