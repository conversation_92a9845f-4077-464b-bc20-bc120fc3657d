import { <PERSON>, Button, <PERSON>arProgress, <PERSON>, Stack, Typography } from '@mui/material';

import { useEffect, useMemo, useRef, useState } from 'react';
import { Tree, TreeApi } from 'react-arborist';
import { useEffectOnce } from 'react-use';
import { useListProjectLocations, useListNodesTree } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { NodeEnergyType } from '@/components/GraphSetting';
import { useTreeData } from '@/components/GraphSetting/hooks/useTreeData';
import { TreeNodeComponent } from '@/components/GraphSetting/Node';
import { NodeDatapoints } from '@/components/GraphSetting/NodeDatapoints';
import { NodeEditor } from '@/components/GraphSetting/NodeEditor';
import { NodeFiler } from '@/components/GraphSetting/NodeFiler';
import { NodeName } from '@/components/GraphSetting/NodeName';
import { useViewportTopToBottoms } from '@/hooks/useViewportTopToBottom';
import { TreeNodeWithHash, Project } from '@/types';

function GraphSettingViewScreen({ projectId }: { projectId: Project['id'] }) {
  const memoisedIds = useMemo(() => ['contentBox', 'titleDivider'], []);
  const [contentBoxHeight, titleDivider] = useViewportTopToBottoms(memoisedIds);

  const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null);
  const { data, isLoading, trigger } = useListNodesTree();

  const { treeData, setValue } = useTreeData([]);

  const treeRef = useRef<TreeApi<TreeNodeWithHash> | null>(null);

  useEffect(() => {
    trigger({
      urlParameter: {
        projectId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  useEffect(() => {
    setValue(data ? data.nodesTree : []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const selectedNode = useMemo(() => {
    const searchNodeById = (nodes: TreeNodeWithHash[], id: number): TreeNodeWithHash | null => {
      for (const node of nodes) {
        if (node.id === id) {
          return node;
        }
        const result = searchNodeById(node.children, id);
        if (result) {
          return result;
        }
      }
      return null;
    };

    return selectedNodeId ? searchNodeById(treeData, selectedNodeId) : null;
  }, [treeData, selectedNodeId]);

  const { trigger: fetchProjectLocations, data: projectLocationResponse } =
    useListProjectLocations();

  useEffectOnce(() => {
    fetchProjectLocations({
      urlParameter: {
        projectId,
      },
    });
  });

  const locationNotFound = useMemo(() => {
    return projectLocationResponse?.projectLocations.length === 0;
  }, [projectLocationResponse]);

  return (
    <>
      <ContentTitle>
        <ContentTitle.Label>グラフ設定</ContentTitle.Label>
        {!locationNotFound && (
          <ContentTitle.Buttons>
            <Button LinkComponent={Link} href='./edit' variant={'contained'} disableElevation>
              編集モードへ
            </Button>
          </ContentTitle.Buttons>
        )}
      </ContentTitle>
      {locationNotFound ? (
        <Stack spacing={2}>
          <Typography>
            グラフ設定を行うには、案件とロケーションを紐づける必要があります。
          </Typography>
          <Box>
            <Button
              LinkComponent={Link}
              href={'/projects/' + projectId + '/location'}
              variant='contained'
            >
              案件とロケーションを紐づける
            </Button>
          </Box>
        </Stack>
      ) : (
        <NodeFiler>
          <NodeFiler.Left>
            <NodeFiler.ColumnTitle>
              <NodeFiler.TitleLabel>ノード</NodeFiler.TitleLabel>
            </NodeFiler.ColumnTitle>
            <Box position={'relative'}>
              {isLoading && (
                <Box position={'absolute'} width={'100%'}>
                  <LinearProgress />
                </Box>
              )}
              {treeData.length > 0 && (
                <Tree
                  ref={treeRef}
                  data={treeData}
                  idAccessor={'hash'}
                  rowHeight={32}
                  width={'100%'}
                  height={contentBoxHeight - titleDivider}
                  onActivate={(node) => {
                    setSelectedNodeId(node.data.id);
                  }}
                  onSelect={(node) => {
                    if (node.length === 0) {
                      setSelectedNodeId(null);
                    }
                  }}
                  disableDrag
                  openByDefault={false}
                  disableMultiSelection
                >
                  {TreeNodeComponent}
                </Tree>
              )}
            </Box>
          </NodeFiler.Left>
          <NodeFiler.Right>
            <NodeFiler.ColumnTitle>
              <NodeFiler.TitleLabel>ノード詳細</NodeFiler.TitleLabel>
            </NodeFiler.ColumnTitle>
            <NodeEditor>
              {treeData.length === 0 ? (
                <Typography>
                  登録されているノードは0件です。
                  <br />
                  編集モードから新しいノードが追加できます。
                </Typography>
              ) : selectedNodeId ? (
                <>
                  <NodeName node={selectedNode} />
                  {selectedNode && <NodeEnergyType node={selectedNode} />}
                  <NodeDatapoints node={selectedNode} />
                  {/* <NodeColor node={selectedNode} /> */}
                </>
              ) : (
                <Typography>ノードが選択されていません</Typography>
              )}
            </NodeEditor>
          </NodeFiler.Right>
        </NodeFiler>
      )}
    </>
  );
}

export { GraphSettingViewScreen };
