import { Add, <PERSON><PERSON>own<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Edit, Delete, Close } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Al<PERSON>,
  Box,
  Button,
  ButtonGroup,
  Chip,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  LinearProgress,
  Stack,
  Typography,
  styled,
} from '@mui/material';

import { useEffect, useMemo, useRef, useState } from 'react';
import { Tree, TreeApi } from 'react-arborist';
import { useNavigate } from 'react-router-dom';
import type { MoveHandler } from '../../../../node_modules/react-arborist/dist/types/handlers';
import { useCreateNode, useDeleteNode, useListNodesTree, useUpdateNode } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { EcoisDialog } from '@/components/Dialog';
import {
  NodeEditor,
  NodeFiler,
  NodeNameField,
  TreeNodeComponent,
  NodeDatapointsField,
  NodeEnergyTypeField,
} from '@/components/GraphSetting';
import { toTreeNodeWithHash } from '@/components/GraphSetting/helper/addHash';
import {
  calculateNewOrderByDirection,
  calculateNewOrderByIndex,
  countItems,
} from '@/components/GraphSetting/helper/TreeMoveFunction';
import { useTreeData } from '@/components/GraphSetting/hooks/useTreeData';
import { useViewportTopToBottoms } from '@/hooks/useViewportTopToBottom';
import { TreeNodeWithHash, Project } from '@/types';

const StyledButtonGroup = styled(ButtonGroup)`
  & .MuiButtonGroup-grouped:not(:last-of-type) {
    border-right: none;
  }
`;

// ノードの最大深度
const MAX_NODE_DEPTH = 10;

function GraphSettingEditScreen({
  project,
  depthLimit = MAX_NODE_DEPTH,
}: {
  project: Project;
  depthLimit?: number;
}) {
  const projectId = project.id;

  // 最大の時はアラートを非表示に
  const showDepthLimitAlert = depthLimit !== MAX_NODE_DEPTH;

  const memoisedIds = useMemo(() => ['contentBox', 'titleDivider'], []);
  const [contentBoxHeight, titleDivider] = useViewportTopToBottoms(memoisedIds);

  const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null);
  const { data, isLoading, trigger } = useListNodesTree();
  const { trigger: createNodeTrigger } = useCreateNode();
  const { trigger: updateNodeTrigger, isLoading: isUpdateLoading } = useUpdateNode();
  const { trigger: deleteNodeTrigger, isLoading: isDeleteLoading } = useDeleteNode();

  const { treeData, updateNode, deleteNode, moveUpward, moveDownward, addNode, dndNode, setValue } =
    useTreeData([]);

  const navigate = useNavigate();

  const treeRef = useRef<TreeApi<TreeNodeWithHash> | null>(null);

  useEffect(() => {
    trigger({
      urlParameter: {
        projectId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  useEffect(() => {
    setValue(data ? data.nodesTree : []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const selectedNode = useMemo(() => {
    const searchNodeById = (nodes: TreeNodeWithHash[], id: number): TreeNodeWithHash | null => {
      for (const node of nodes) {
        if (node.id === id) {
          return node;
        }
        const result = searchNodeById(node.children, id);
        if (result) {
          return result;
        }
      }
      return null;
    };

    return selectedNodeId ? searchNodeById(treeData, selectedNodeId) : null;
  }, [treeData, selectedNodeId]);

  // useEffect(() => {
  //   console.log('selectedNode:', selectedNode);
  // }, [selectedNode]);

  const disableControls = useMemo(() => {
    return selectedNode ? selectedNode.relativeOrderPath.length >= depthLimit : false;
  }, [selectedNode, depthLimit]);

  const [open, setOpen] = useState(false);
  function handleOpen() {
    setOpen(true);
  }
  function handleClose() {
    setOpen(false);
  }

  const [openEditAlert, setOpenEditAlert] = useState(true);

  return (
    <>
      <ContentTitle alignItems={'flex-start'}>
        <ContentTitle.Label>
          <Stack direction={'row'} alignItems={'center'} spacing={1}>
            <Box>グラフ設定</Box>
            <Chip
              label='編集モード'
              color={'primary'}
              variant='outlined'
              size='small'
              icon={<Edit fontSize='small' />}
            />
          </Stack>
          <Box
            mt={1.5}
            mb={1}
            sx={{
              display: openEditAlert ? 'block' : 'none',
            }}
          >
            <Alert
              severity={'info'}
              action={
                <IconButton
                  aria-label='close'
                  color='inherit'
                  size='small'
                  onClick={() => {
                    setOpenEditAlert(false);
                  }}
                >
                  <Close fontSize='inherit' />
                </IconButton>
              }
            >
              <Stack>
                {showDepthLimitAlert && (
                  <Typography variant='caption' color={'text.secondary'}>
                    ノード最大階層は<strong>{depthLimit}</strong>階層です
                  </Typography>
                )}
                <Typography variant='caption' color={'text.secondary'}>
                  グラフノードの追加・編集を行う際､<strong>サーバーに即時</strong>に保存されます
                </Typography>
                <Typography variant='caption' color={'text.secondary'}>
                  その他の情報は<strong>保存ボタンをクリック時</strong>に保存されます
                </Typography>
              </Stack>
            </Alert>
          </Box>
        </ContentTitle.Label>
        <ContentTitle.Buttons>
          <Button
            onClick={() => {
              navigate('../', { relative: 'path' });
            }}
            variant='contained'
            disableElevation
          >
            編集を終了
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>
      <NodeFiler>
        <NodeFiler.Left>
          <NodeFiler.ColumnTitle>
            <NodeFiler.TitleLabel>ノード</NodeFiler.TitleLabel>
            <NodeFiler.TitleButtons>
              <StyledButtonGroup variant={'text'} size='small' color={'inherit'}>
                {selectedNode && (
                  <>
                    {/* 上へ移動ボタン */}
                    <Button
                      color={'inherit'}
                      onClick={async () => {
                        const newOrder = calculateNewOrderByDirection(
                          treeData,
                          selectedNode.id,
                          'up',
                        );
                        if (newOrder) {
                          const newNode = {
                            ...selectedNode,
                            order: newOrder,
                          };
                          await updateNodeTrigger({
                            urlParameter: {
                              nodeId: selectedNode.id,
                            },
                            body: {
                              order: newOrder,
                            },
                          });
                          updateNode(newNode);
                          moveUpward(selectedNode.id);
                        }
                      }}
                    >
                      <ArrowUpward />
                    </Button>
                    {/* 下へ移動ボタン */}
                    <Button
                      color={'inherit'}
                      onClick={async () => {
                        const newOrder = calculateNewOrderByDirection(
                          treeData,
                          selectedNode.id,
                          'down',
                        );
                        if (newOrder) {
                          const newNode = {
                            ...selectedNode,
                            order: newOrder,
                          };
                          await updateNodeTrigger({
                            urlParameter: {
                              nodeId: selectedNode.id,
                            },
                            body: {
                              order: newOrder,
                            },
                          });
                          updateNode(newNode);
                          moveDownward(selectedNode.id);
                        }
                      }}
                    >
                      <ArrowDownward />
                    </Button>
                  </>
                )}
                {/* ノード追加ボタン */}
                {/* TODO 追加できない理由のポップアップを追加 */}
                <Button
                  disabled={disableControls}
                  color='primary'
                  onClick={async () => {
                    const result = await createNodeTrigger({
                      body: {
                        name: `新規ノード${countItems(treeData) + 1}`,
                        projectId: projectId,
                        energyTypeId: 1,
                        ...(selectedNodeId && { parentId: selectedNodeId }),
                      },
                    });
                    if (result) {
                      const addedTreeNodeWithHash = toTreeNodeWithHash(result.node);
                      addNode(selectedNodeId, addedTreeNodeWithHash, treeRef.current!);

                      // フォーカスして選択されている状態にする
                      treeRef.current?.select(addedTreeNodeWithHash.hash);
                      setSelectedNodeId(addedTreeNodeWithHash.id);

                      // TreeNodes Revalidation;
                      trigger({
                        urlParameter: {
                          projectId,
                        },
                      });
                    }
                  }}
                >
                  <Add />
                </Button>
              </StyledButtonGroup>
            </NodeFiler.TitleButtons>
          </NodeFiler.ColumnTitle>
          <Box position={'relative'}>
            {(isLoading || isUpdateLoading) && (
              <Box position={'absolute'} width={'100%'}>
                <LinearProgress />
              </Box>
            )}
            {treeData.length > 0 && (
              <Tree
                ref={treeRef}
                data={treeData}
                onMove={async (dragEvent) => {
                  /* 
                  - Aドラッグされたノードの親ノードのIDとorderを更新
                  - Bツリーに反映する

                  - A
                    - リクエスト用データの作成
                      - 親ノードIDの取得
                      - orderの計算
                    - リクエスト

                  - B
                    - ツリー更新関数に計算したオーダーなどを渡す使用に変更
                  */

                  // ノードの最大深度を超えている場合は処理を中断
                  if (isExceedingDepthLimit(dragEvent, depthLimit)) {
                    return;
                  }

                  const { parentId, dragNodes, index } = dragEvent;
                  const dragNode = dragNodes[0].data;

                  const newOrder = calculateNewOrderByIndex(treeData, index, Number(parentId));
                  const newNode: TreeNodeWithHash = {
                    ...dragNode,
                    parentId: typeof parentId === 'string' ? Number(parentId) : parentId,
                    order: newOrder ? newOrder : dragNode.order,
                  };
                  const response = await updateNodeTrigger({
                    urlParameter: {
                      nodeId: dragNode.id,
                    },
                    body: newNode,
                  });

                  if (response?.node) {
                    const responseWithHash = {
                      ...response?.node,
                      hash: `${response?.node.id}`,
                      children: newNode.children,
                    };
                    dndNode(dragEvent, responseWithHash);
                  }
                }}
                idAccessor={'hash'}
                rowHeight={32}
                width={'100%'}
                height={contentBoxHeight - titleDivider}
                onActivate={(node) => {
                  setSelectedNodeId(node.data.id);
                }}
                onSelect={(node) => {
                  if (node.length === 0) {
                    setSelectedNodeId(null);
                  }
                }}
                openByDefault={false}
                disableMultiSelection
              >
                {TreeNodeComponent}
              </Tree>
            )}
          </Box>
        </NodeFiler.Left>
        <NodeFiler.Right>
          <NodeFiler.ColumnTitle>
            <NodeFiler.TitleLabel>ノード詳細</NodeFiler.TitleLabel>
            <NodeFiler.TitleButtons pr={1}>
              {selectedNode && (
                <>
                  <Button
                    startIcon={<Delete />}
                    color='error'
                    variant='outlined'
                    size='small'
                    id='basic-menu'
                    onClick={handleOpen}
                  >
                    削除
                  </Button>
                </>
              )}
            </NodeFiler.TitleButtons>
          </NodeFiler.ColumnTitle>
          <NodeEditor>
            {treeData.length === 0 ? (
              <Typography>登録されているノードは0件です。</Typography>
            ) : selectedNodeId ? (
              <>
                <NodeNameField node={selectedNode} onSubmit={updateNode} />
                {/*  TODO ノードのエネルギータイプを選択ロジック追加 都度保存ロジックなど実装 */}
                {/* <EnergyTypeSelect sx={{ maxWidth: '60%' }} size='small' /> */}
                <NodeEnergyTypeField node={selectedNode} onSubmit={updateNode} />
                <NodeDatapointsField
                  node={selectedNode}
                  onSubmit={updateNode}
                  projectId={projectId}
                />
              </>
            ) : (
              <Typography>ノードが選択されていません</Typography>
            )}
          </NodeEditor>
        </NodeFiler.Right>
      </NodeFiler>
      <EcoisDialog
        close={() => {
          setOpen(false);
        }}
        open={open}
        onClose={(_, reason) => {
          if ('backdropClick' === reason) {
            handleClose();
          }
        }}
      >
        <DialogTitle color={'error'}>ノード削除</DialogTitle>
        <DialogContent>
          {selectedNode && selectedNode.children.length > 0
            ? '子ノードを持つ場合は削除できませんので、先に子ノードを取り除いてください'
            : '削除すると元に戻せません。本当に削除してよろしいですか？'}
        </DialogContent>
        <DialogActions>
          <Button
            color={'inherit'}
            onClick={() => {
              handleClose();
            }}
          >
            キャンセル
          </Button>
          <LoadingButton
            loading={isDeleteLoading}
            color={'error'}
            variant={'contained'}
            onClick={async () => {
              if (selectedNode) {
                const response = await deleteNodeTrigger({
                  urlParameter: {
                    nodeId: selectedNode.id,
                  },
                });
                if (response?.deleted) {
                  deleteNode(selectedNode.id);
                  setSelectedNodeId(null);
                }
              }
              handleClose();
            }}
            disabled={selectedNode ? selectedNode.children.length > 0 : false}
          >
            削除する
          </LoadingButton>
        </DialogActions>
      </EcoisDialog>
    </>
  );
}

type DragEvent = Parameters<MoveHandler<TreeNodeWithHash>>[0];
function isExceedingDepthLimit(dragEvent: DragEvent, depthLimit: number): boolean {
  const dragNodeChildrenCount = dragEvent.dragNodes[0].data.children.length;
  const parentNodeDepth = dragEvent.parentNode?.data.relativeOrderPath.length ?? 0;
  const expectedDepth = dragNodeChildrenCount + parentNodeDepth + 1;

  return expectedDepth > depthLimit;
}

export { GraphSettingEditScreen };
