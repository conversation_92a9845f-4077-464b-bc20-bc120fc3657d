import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import {
  Box,
  Button,
  TableCell,
  Link,
  Typography,
  TextField,
  Stack,
  Checkbox,
  FormControlLabel,
  FormControl,
  OutlinedInput,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { isDeepEqual } from '@mui/x-data-grid/internals';
import { useEffect, useMemo, useReducer, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDebounce } from 'react-use';
import { AvailableIcon, NotAvailableIcon } from '../components/Project/IsAvailableIcons';
import { StatusMarkDot } from '@/components/Datapoint/StatusMark';
import { PageHeader } from '@/components/PageHeader';
import { useProjectAddDialog } from '@/components/Project';
import { useProjects } from '@/components/ProjectList';
import { EcoisSimpleDataTable } from '@/components/Table';
import { useDialogNavigation } from '@/hooks';
import { DatalistLayout } from '@/layout';
import { Project } from '@/types';

type SearchState = {
  id: string;
  name: string;
  backlogIssueKey: string;
  locationAddress: string;
  locationNull: boolean;
  isKenesIntegrationAvailable: 'all' | 'available' | 'unavailable';
  isAvailable: 'available' | 'all' | 'unavailable'; // 追加
};

// 初期状態
const initialState: SearchState = {
  id: '',
  name: '',
  backlogIssueKey: '',
  locationAddress: '',
  locationNull: false,
  isKenesIntegrationAvailable: 'all',
  isAvailable: 'available', // 追加
};

type SearchAction =
  | { type: 'SET_SEARCH'; field: keyof SearchState; value: string | boolean }
  | { type: 'CLEAR_SEARCH' };

// Reducerの実装
function searchReducer(state: SearchState, action: SearchAction): SearchState {
  switch (action.type) {
    case 'SET_SEARCH':
      return {
        ...state,
        [action.field]: action.value,
      };
    case 'CLEAR_SEARCH':
      return initialState; // 初期状態にリセット
    default:
      return state;
  }
}

//
// フィルタリングロジック関数
//
const filterProjects = (projects: Project[], search: SearchState): Project[] => {
  const {
    id,
    name,
    backlogIssueKey,
    locationAddress,
    locationNull,
    isKenesIntegrationAvailable,
    isAvailable,
  } = search;

  return projects.filter((project) => {
    const wordId = id.split(' ');
    const wordName = name.split(' ');
    const wordBacklogIssueKey = backlogIssueKey.split(' ');
    const wordLocationAddress = locationAddress.split(' ');
    /* 
    id : 完全一致検索
    name : 部分一致検索
    backlogIssueKey : 部分一致検索 
    locationAddress : 部分一致検索 
    locationNull : ロケーションがないかどうか
    */
    const conditions = {
      id: id === '' || wordId.every((word) => project.id.toString() === word),
      name: name === '' || wordName.every((word) => project.name.includes(word)),
      backlogIssueKey:
        backlogIssueKey === '' ||
        wordBacklogIssueKey.some((word) => project.backlogIssueKey?.includes(word)),
      locationAddress:
        locationAddress === '' ||
        wordLocationAddress.every((word) =>
          project.locations.some((location) => location.address.includes(word)),
        ),
      locationNull: locationNull ? project.locations.length === 0 : true,
      isKenesIntegrationAvailable:
        isKenesIntegrationAvailable === 'all' ||
        (isKenesIntegrationAvailable === 'available' && project.isKenesIntegrationAvailable) ||
        (isKenesIntegrationAvailable === 'unavailable' && !project.isKenesIntegrationAvailable),
      isAvailable:
        isAvailable === 'all' ||
        (isAvailable === 'available' && project.isAvailable) ||
        (isAvailable === 'unavailable' && !project.isAvailable),
    };

    return (
      conditions.id &&
      conditions.name &&
      conditions.backlogIssueKey &&
      conditions.locationAddress &&
      conditions.locationNull &&
      conditions.isKenesIntegrationAvailable &&
      conditions.isAvailable
    );
  });
};
/**
 *
 *
 * 案件一覧画面
 *
 *
 * */
function ProjectListScreen() {
  // リストの取得
  const { data, isLoading } = useProjects();

  // For searching
  const [search, dispatch] = useReducer(searchReducer, initialState);
  const [isPristine, setIsPristine] = useState(true);
  const [searchStatus, setSearchStatus] = useState<'pending' | 'ready'>('ready');
  const [debouncedSearch, setDebouncedSearch] = useState<SearchState>(search);
  const [ready] = useDebounce(
    () => {
      setDebouncedSearch(search);
      setIsPristine(false);
    },
    500,
    [search],
  );

  const isFinished = ready();

  useEffect(() => {
    if (isFinished) {
      setSearchStatus('ready');
    } else {
      !isPristine && setSearchStatus('pending');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFinished]);

  // フィルタリング
  const filteredProjects = useMemo(
    () => filterProjects(data?.projects ?? [], debouncedSearch),
    [data, debouncedSearch],
  );

  // Clear location text search
  function handleClearLocationTextSearch() {
    dispatch({ type: 'SET_SEARCH', field: 'locationAddress', value: '' });
  }

  //
  // ダイアログの表示
  //
  const { ProjectAddDialog, open: openAddDialog, props: addDialogProps } = useProjectAddDialog();

  //
  // イベントハンドリング
  //

  const { cancelNavigate, submitNavigate } = useDialogNavigation({
    dialogTriggerUrl: '/projects/add',
    handleAccess: () => {
      openAddDialog();
    },
  });

  const navigate = useNavigate();
  function handleTableRowClick(project: Project) {
    return () => {
      navigate(`/projects/${project.id}/detail`);
    };
  }

  function handleFormCancel() {
    cancelNavigate('/projects');
  }

  function handleFormSubmit(project: Project) {
    submitNavigate(`/projects/${project?.id}/detail`);
  }

  // テーブルの状態
  const state = useMemo(
    () => ({
      isEmptyData: data?.projects.length === 0,
      isTableLoading: isLoading || searchStatus === 'pending',
      isSearchResultEmpty:
        !!data?.projects && filteredProjects.length === 0 && searchStatus === 'ready',
    }),
    [data?.projects, filteredProjects.length, isLoading, searchStatus],
  );

  const isAvalableMenuItems = {
    available: {
      icon: <AvailableIcon />,
      label: '有効',
    },
    all: {
      icon: (
        <Box
          sx={{
            width: 24,
            height: 24,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <FormatListBulletedIcon sx={{ fontSize: '16px' }} />
        </Box>
      ),
      label: 'すべて',
    },
    unavailable: {
      icon: <NotAvailableIcon />,
      label: '無効',
    },
  };

  return (
    <Box display={'flex'} flexDirection={'column'} flexGrow={1}>
      <Box id='title' display='flex' alignItems='center' justifyContent='space-between'>
        <PageHeader
          contents={[
            {
              label: '案件',
            },
          ]}
        />
        <Button LinkComponent={Link} variant='contained' onClick={openAddDialog}>
          案件作成
        </Button>
      </Box>

      {state.isEmptyData ? (
        <Typography>登録されている案件は0件です。</Typography>
      ) : (
        <DatalistLayout>
          <DatalistLayout.ControlIsland>
            <>
              <Stack
                mb={1}
                direction={'row'}
                justifyContent={{ xs: 'flex-start', lg: 'space-between' }}
                alignItems={'center'}
              >
                <Box>
                  <Typography variant='body1'>絞り込み検索</Typography>
                </Box>
                <Box>
                  <Button
                    disabled={isDeepEqual(search, initialState)}
                    onClick={() => {
                      dispatch({ type: 'CLEAR_SEARCH' });
                    }}
                  >
                    条件をクリア
                  </Button>
                </Box>
              </Stack>
              <Stack
                spacing={{
                  xs: 1,
                  lg: 3,
                }}
                direction={{ lg: 'column', xs: 'row' }}
              >
                <TextField
                  size='small'
                  label='案件ID'
                  placeholder='1 2 3 4 ...'
                  name='id'
                  value={search.id}
                  onChange={(e) =>
                    dispatch({ type: 'SET_SEARCH', field: 'id', value: e.target.value })
                  }
                />
                <TextField
                  label='案件名'
                  size='small'
                  name='name'
                  placeholder='案件名A 案件名B ...'
                  value={search.name}
                  onChange={(e) =>
                    dispatch({ type: 'SET_SEARCH', field: 'name', value: e.target.value })
                  }
                />
                <FormControl size='small'>
                  <InputLabel htmlFor='backlogIssueKey'>Backlog課題</InputLabel>
                  <OutlinedInput
                    label='Backlog課題'
                    name='backlogIssueKey'
                    placeholder='1 2 3 4 ...'
                    value={search.backlogIssueKey}
                    onChange={(e) => {
                      dispatch({
                        type: 'SET_SEARCH',
                        field: 'backlogIssueKey',
                        value: e.target.value,
                      });
                    }}
                  />
                </FormControl>
                <FormControl size='small'>
                  <InputLabel htmlFor='locationAddress'>ロケーションアドレス</InputLabel>
                  <OutlinedInput
                    label='ロケーションアドレス'
                    name='locationAddress'
                    placeholder='ABCD1234 XYZ 6789...'
                    value={search.locationAddress}
                    onChange={(e) => {
                      dispatch({
                        type: 'SET_SEARCH',
                        field: 'locationNull',
                        value: false,
                      }); // ロケーションなしのチェックを外す
                      dispatch({
                        type: 'SET_SEARCH',
                        field: 'locationAddress',
                        value: e.target.value,
                      });
                    }}
                  />
                </FormControl>
                <FormControl size='small'>
                  <InputLabel id='isKenesIntegrationAvailable'>KENES連携</InputLabel>
                  <Select
                    label='isKenesIntegrationAvailable'
                    size='small'
                    value={search.isKenesIntegrationAvailable}
                    defaultValue={'all'}
                    onChange={(e) =>
                      dispatch({
                        type: 'SET_SEARCH',
                        field: 'isKenesIntegrationAvailable',
                        value: e.target.value,
                      })
                    }
                  >
                    <MenuItem value='all'>すべて</MenuItem>
                    <MenuItem value='available'>連携中</MenuItem>
                    <MenuItem value='unavailable'>未連携</MenuItem>
                  </Select>
                </FormControl>
                <FormControl size='small'>
                  <InputLabel id='isAvailable'>有効状態</InputLabel>
                  <Select
                    sx={{ display: 'flex', alignItems: 'center' }}
                    label='isAvailable'
                    size='small'
                    value={search.isAvailable}
                    defaultValue={'available'}
                    onChange={(e) =>
                      dispatch({
                        type: 'SET_SEARCH',
                        field: 'isAvailable',
                        value: e.target.value,
                      })
                    }
                    //アイコンとテキストがズレるので、renderValueを使ってカスタム表示
                    renderValue={(value) => (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {isAvalableMenuItems[value as keyof typeof isAvalableMenuItems].icon}
                        {isAvalableMenuItems[value as keyof typeof isAvalableMenuItems].label}
                      </Box>
                    )}
                  >
                    {Object.entries(isAvalableMenuItems).map(([key, item]) => (
                      <MenuItem
                        key={key}
                        value={key}
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        {item.icon}
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
              <Box
                pl={{
                  xs: 0.5,
                }}
                pt={{
                  xs: 1,
                  lg: 2,
                }}
                mb={{ xs: 2 }}
              >
                <FormControlLabel
                  componentsProps={{
                    typography: {
                      variant: 'body2',
                    },
                  }}
                  control={
                    <Checkbox
                      size='small'
                      checked={search.locationNull}
                      onChange={(e) => {
                        handleClearLocationTextSearch();
                        dispatch({
                          type: 'SET_SEARCH',
                          field: 'locationNull',
                          value: e.target.checked,
                        });
                      }}
                    />
                  }
                  label='ロケーションなしの案件のみ表示'
                />
              </Box>
            </>
          </DatalistLayout.ControlIsland>
          <DatalistLayout.DataIsland>
            <Stack py={1} mb={1}>
              <Typography variant={'caption'} textAlign={'end'}>
                {`${filteredProjects.length}件表示中 / 全${data?.projects.length}件`}
              </Typography>
            </Stack>
            <EcoisSimpleDataTable
              data={filteredProjects}
              tableHeadRow={
                <>
                  <TableCell sx={{ width: '5%', whiteSpace: 'nowrap' }}>案件ID</TableCell>
                  <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>案件名</TableCell>
                  <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>Backlog課題</TableCell>
                  <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>
                    ロケーションアドレス
                  </TableCell>
                  <TableCell sx={{ width: '5%', whiteSpace: 'nowrap' }}>KENES連携</TableCell>
                </>
              }
              tableBodyRow={(project) => (
                <>
                  <TableCell>{project.id}</TableCell>
                  <TableCell sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {project.isAvailable ? <AvailableIcon /> : <NotAvailableIcon />}
                    {project.name}
                  </TableCell>
                  <TableCell
                    sx={{
                      whiteSpace: 'pre-wrap',
                      lineBreak: 'anywhere',
                    }}
                  >
                    {project.backlogIssueKey ? (
                      <Link
                        href={`https://picoada.backlog.jp/view/${project.backlogIssueKey}`}
                        target='_blank'
                        rel='noreferrer noopener'
                      >
                        {project.backlogIssueKey}
                      </Link>
                    ) : (
                      'なし'
                    )}
                  </TableCell>
                  <TableCell
                    sx={{
                      whiteSpace: 'pre-wrap',
                      lineBreak: 'anywhere',
                    }}
                  >
                    {project.locations.length > 0
                      ? project.locations.map((location) => location.address).join(',')
                      : 'なし'}
                  </TableCell>
                  <TableCell>
                    <Stack direction={'row'} alignItems={'center'} spacing={1}>
                      <StatusMarkDot isAvailable={project.isKenesIntegrationAvailable || false} />
                      <Typography variant={'body2'}>
                        {project.isKenesIntegrationAvailable ? '連携中' : '未連携'}
                      </Typography>
                    </Stack>
                  </TableCell>
                </>
              )}
              rowProps={(project) => ({
                onClick: handleTableRowClick(project),
              })}
              isEmpty={state.isSearchResultEmpty}
              isProgress={state.isTableLoading}
            />
          </DatalistLayout.DataIsland>
        </DatalistLayout>
      )}
      <ProjectAddDialog
        {...addDialogProps}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
      />
    </Box>
  );
}

export { ProjectListScreen };
