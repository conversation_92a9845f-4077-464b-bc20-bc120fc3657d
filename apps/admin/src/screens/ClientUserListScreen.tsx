import {
  Box,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Button,
  Stack,
  LinearProgress,
} from '@mui/material';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useListClientUsers } from '@/api';
import { useClientUserAddDialog } from '@/components/ClientUser/ClientUserAddDialog';
import { PageHeader } from '@/components/PageHeader';
import { SimpleSearchField, useSimpleSearch } from '@/components/SimpleSearch';
import { EcoisTableContainer } from '@/components/Table';
import { useDialogNavigation } from '@/hooks';
import { DatalistLayout } from '@/layout';
import { ClientUser } from '@/types';

const iconButtonVisibilityToggleStyle = {
  '& .MuiIconButton-root': {
    visibility: 'hidden',
  },
  '&:hover .MuiIconButton-root': {
    visibility: 'visible',
  },
};

// TODO コンポート分割を行う
function ClientUserListScreen() {
  const navigate = useNavigate();

  const {
    data: clientUsers,
    isLoading: clientUsersIsLoading,
    trigger: clientUsersTrigger,
  } = useListClientUsers();

  const {
    ClientUserAddDialog,
    open: openAddDialog,
    props: addDialogProps,
  } = useClientUserAddDialog();

  const { submitNavigate, cancelNavigate } = useDialogNavigation({
    dialogTriggerUrl: '/clientusers/add',
    handleAccess: () => {
      openAddDialog();
    },
  });

  useEffectOnce(() => {
    fetchClientUsers();
  });

  function fetchClientUsers() {
    clientUsersTrigger({});
  }

  const {
    data: filteredList,
    register,
    state: searchState,
  } = useSimpleSearch<ClientUser>({
    defaultValues: clientUsers?.clientUsers,
    filterFunction: (clientUser, searchText) => {
      return (
        clientUser?.username?.includes(searchText) ||
        clientUser?.email?.includes(searchText) ||
        clientUser?.name?.includes(searchText)
      );
    },
  });

  const state = useMemo(
    () => ({
      isEmptyData: clientUsers?.clientUsers.length === 0,
      isTableLoading: clientUsersIsLoading || searchState === 'pending',
      isSearchResultEmpty:
        clientUsers?.clientUsers && filteredList.length === 0 && searchState === 'ready',
    }),
    [clientUsers, clientUsersIsLoading, filteredList, searchState],
  );

  return (
    <>
      <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
        <PageHeader contents={[{ label: 'クライアントユーザー' }]} />
        <Box>
          <Button
            variant={'contained'}
            onClick={() => {
              openAddDialog();
            }}
          >
            新規作成
          </Button>
        </Box>
      </Stack>
      {state.isEmptyData ? (
        <Typography>登録されているクライアントユーザーは0件です。</Typography>
      ) : (
        <DatalistLayout>
          <DatalistLayout.ControlIsland>
            <SimpleSearchField
              {...register}
              name='clientuser-search'
              label='ユーザーネーム,名前,Emailで検索'
              fullWidth
            />
          </DatalistLayout.ControlIsland>
          <DatalistLayout.DataIsland>
            <EcoisTableContainer>
              {state.isTableLoading && (
                <Box position={'absolute'} width={'100%'} zIndex={99999}>
                  <LinearProgress />
                </Box>
              )}
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '10%' }}>ID</TableCell>
                    <TableCell>氏名</TableCell>
                    <TableCell>ユーザーネーム</TableCell>
                    <TableCell>メールアドレス</TableCell>
                    <TableCell>有効状態</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {state.isSearchResultEmpty && (
                    <TableRow>
                      <TableCell colSpan={5}>案件が見つかりませんでした。</TableCell>
                    </TableRow>
                  )}
                  {filteredList.map((user) => (
                    <TableRow
                      hover
                      key={user.id}
                      sx={{
                        textDecoration: 'none',
                        ...iconButtonVisibilityToggleStyle,
                      }}
                      onClick={() => {
                        navigate(`/clientusers/${user.id}/detail`);
                      }}
                    >
                      <TableCell sx={{ width: '10%' }}>{user.id}</TableCell>
                      <TableCell sx={{ width: '15%' }}>{user.name ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '15%' }}>{user.username ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '20%' }}>{user.email ?? '未設定'}</TableCell>
                      <TableCell sx={{ width: '10%' }}>
                        {user.isAvailable ? '有効' : '無効'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </EcoisTableContainer>
          </DatalistLayout.DataIsland>
        </DatalistLayout>
      )}

      <ClientUserAddDialog
        {...addDialogProps}
        onCancel={() => {
          cancelNavigate('/clientusers');
        }}
        onClose={(clientUser) => {
          submitNavigate(`/clientusers/${clientUser.id}/detail`);
        }}
      />
    </>
  );
}

export { ClientUserListScreen };
