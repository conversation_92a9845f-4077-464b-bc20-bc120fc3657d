import { Button } from '@mui/material';
import { AdminUserDetailInfo } from '@/components/AdminUser/AdminUserDetailInfo';
import { useAdminUserEditDialog } from '@/components/AdminUser/AdminUserEditDialog';
import { InfoCard, InfoCardContent, InfoCardHeader } from '@/components/InfoCard';
import { AdminUser } from '@/types';

function AdminUserDetailScreen({
  adminUser,
  isLoading,
  error,
  onEdit,
}: {
  adminUser: AdminUser | undefined;
  isLoading: boolean;
  error?: any;
  onEdit: (adminUser: AdminUser) => void;
}) {
  const {
    AdminUserEditDialog,
    open: openEditDialog,
    props: editDialogProps,
  } = useAdminUserEditDialog();

  return (
    <>
      <InfoCard>
        <InfoCardHeader
          actions={
            <Button
              disabled={isLoading || !!error || adminUser === undefined}
              variant={'contained'}
              onClick={() => {
                openEditDialog();
              }}
              disableElevation
            >
              編集
            </Button>
          }
        >
          アドミンユーザー基本情報
        </InfoCardHeader>
        <InfoCardContent>
          <AdminUserDetailInfo
            adminUser={adminUser}
            isLoading={isLoading}
            error={error}
            onAddTextButtonClick={openEditDialog}
          />
        </InfoCardContent>
      </InfoCard>

      {adminUser && (
        <AdminUserEditDialog {...editDialogProps} adminUser={adminUser} onSubmit={onEdit} />
      )}
    </>
  );
}

export { AdminUserDetailScreen };
