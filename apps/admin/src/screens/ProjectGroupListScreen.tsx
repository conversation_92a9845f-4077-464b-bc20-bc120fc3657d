import { Table<PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useListProjectGroups } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { PageHeader } from '@/components/PageHeader';
import { useCreateProjectGroupDialog } from '@/components/ProjectGroup';
import { EcoisSimpleDataTable } from '@/components/Table';
import { Column } from '@/components/Table/EcoisSimpleDataTable';
import { useDialogNavigation } from '@/hooks';
import { useSortableData } from '@/hooks/useSortableData';
import { ProjectGroup } from '@/types';

function ProjectGroupListScreen() {
  const navigate = useNavigate();
  const { trigger, data: projectGroupsResponse, isLoading } = useListProjectGroups();

  function fetchProjectGroups() {
    trigger({
      queryParameter: {},
    });
  }

  useEffectOnce(() => {
    fetchProjectGroups();
  });

  const {
    CreateProjectGroupDialog,
    open: openCreateProjectGroupDialog,
    props: createProjectGroupDialogProps,
  } = useCreateProjectGroupDialog();

  const { cancelNavigate, submitNavigate } = useDialogNavigation({
    dialogTriggerUrl: '/projectgroups/add',
    handleAccess: () => {
      openCreateProjectGroupDialog();
    },
  });

  function handleOnCreateSuccess(projectGroup: ProjectGroup) {
    // Close dialog
    submitNavigate(`/projectgroups/${projectGroup.id}/detail`);
  }

  function handleOnCancel() {
    cancelNavigate(`/projectgroups`);
  }

  //sort table
  const { sortedData, order, orderBy, onRequestSort } = useSortableData(
    projectGroupsResponse?.projectGroups ?? [],
    'id', // デフォルトソートキー
  );
  const columns: Column<ProjectGroup>[] = [
    { key: 'name', label: '案件グループ名', minWidth: 100, sortable: true },
    { key: 'detail', label: '説明', minWidth: 100, sortable: true },
  ];

  // function handleDetailBtnClick(projectGroup: ProjectGroup) {
  //   return (e: SyntheticEvent
  // ) => {
  //     e.stopPropagation();
  //     navigate(`/projectgroups/${projectGroup.id}`);
  //   };
  // }

  // function handleDeleteBtnClick() {}

  return (
    <>
      <ContentTitle>
        <PageHeader
          contents={[
            {
              label: '案件グループ',
            },
          ]}
        />
        <ContentTitle.Buttons>
          <Button
            variant='contained'
            disableElevation
            onClick={() => {
              openCreateProjectGroupDialog();
            }}
          >
            追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>
      <EcoisSimpleDataTable
        data={sortedData}
        columns={columns}
        order={order}
        orderBy={orderBy}
        onRequestSort={onRequestSort}
        tableBodyRow={(projectGroup) => (
          <>
            <TableCell sx={{ whiteSpace: 'nowrap', width: 0 }}>{projectGroup.name}</TableCell>
            <TableCell
              sx={{
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 0,
                overflow: 'hidden',
              }}
            >
              {projectGroup.detail}
            </TableCell>
          </>
        )}
        rowProps={(projectGroup) => ({
          onClick: () => {
            navigate(`/projectgroups/${projectGroup.id}/detail`);
          },
        })}
        controlCell={(_projectGroup) => (
          <Stack direction='row' display={'inline-flex'} height={'100%'} alignItems={'center'}>
            {/* <Button size='small' onClick={handleDetailBtnClick(projectGroup)}>
              詳細
            </Button> */}
            {/* <Button size='small'>ユーザー追加</Button> */}
            {/* <Button size='small'>案件追加</Button> */}
            {/* <Button size='small'>削除</Button> */}
          </Stack>
        )}
        isEmpty={projectGroupsResponse?.projectGroups?.length === 0}
        isProgress={isLoading}
      />
      <CreateProjectGroupDialog
        {...createProjectGroupDialogProps}
        onSuccess={handleOnCreateSuccess}
        onCancel={handleOnCancel}
      />
    </>
  );
}
export { ProjectGroupListScreen };
