import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useListProjectGroups } from '@/api';
import { ContentTitle } from '@/components/ContentTitle';
import { PageHeader } from '@/components/PageHeader';
import { useCreateProjectGroupDialog } from '@/components/ProjectGroup';
import { EcoisSimpleDataTable } from '@/components/Table';
import { useDialogNavigation } from '@/hooks';
import { ProjectGroup } from '@/types';

function ProjectGroupListScreen() {
  const navigate = useNavigate();
  const { trigger, data: projectGroupsResponse, isLoading } = useListProjectGroups();

  function fetchProjectGroups() {
    trigger({
      queryParameter: {},
    });
  }

  useEffectOnce(() => {
    fetchProjectGroups();
  });

  const {
    CreateProjectGroupDialog,
    open: openCreateProjectGroupDialog,
    props: createProjectGroupDialogProps,
  } = useCreateProjectGroupDialog();

  const { cancelNavigate, submitNavigate } = useDialogNavigation({
    dialogTriggerUrl: '/projectgroups/add',
    handleAccess: () => {
      openCreateProjectGroupDialog();
    },
  });

  function handleOnCreateSuccess(projectGroup: ProjectGroup) {
    // Close dialog
    submitNavigate(`/projectgroups/${projectGroup.id}/detail`);
  }

  function handleOnCancel() {
    cancelNavigate(`/projectgroups`);
  }

  // function handleDetailBtnClick(projectGroup: ProjectGroup) {
  //   return (e: SyntheticEvent) => {
  //     e.stopPropagation();
  //     navigate(`/projectgroups/${projectGroup.id}`);
  //   };
  // }

  // function handleDeleteBtnClick() {}

  return (
    <>
      <ContentTitle>
        <PageHeader
          contents={[
            {
              label: '案件グループ',
            },
          ]}
        />
        <ContentTitle.Buttons>
          <Button
            variant='contained'
            disableElevation
            onClick={() => {
              openCreateProjectGroupDialog();
            }}
          >
            追加
          </Button>
        </ContentTitle.Buttons>
      </ContentTitle>
      <EcoisSimpleDataTable
        data={projectGroupsResponse?.projectGroups}
        tableHeadRow={
          <>
            <TableCell
              sx={{
                whiteSpace: 'nowrap',
              }}
            >
              案件グループ名
            </TableCell>
            <TableCell
              sx={{
                whiteSpace: 'nowrap',
              }}
            >
              説明
            </TableCell>
          </>
        }
        tableBodyRow={(projectGroup) => (
          <>
            <TableCell sx={{ whiteSpace: 'nowrap', width: 0 }}>{projectGroup.name}</TableCell>
            <TableCell
              sx={{
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 0,
                overflow: 'hidden',
              }}
            >
              {projectGroup.detail}
            </TableCell>
          </>
        )}
        rowProps={(projectGroup) => ({
          onClick: () => {
            navigate(`/projectgroups/${projectGroup.id}/detail`);
          },
        })}
        controlCell={(_projectGroup) => (
          <Stack direction='row' display={'inline-flex'} height={'100%'} alignItems={'center'}>
            {/* <Button size='small' onClick={handleDetailBtnClick(projectGroup)}>
              詳細
            </Button> */}
            {/* <Button size='small'>ユーザー追加</Button> */}
            {/* <Button size='small'>案件追加</Button> */}
            {/* <Button size='small'>削除</Button> */}
          </Stack>
        )}
        isEmpty={projectGroupsResponse?.projectGroups?.length === 0}
        isProgress={isLoading}
      />
      <CreateProjectGroupDialog
        {...createProjectGroupDialogProps}
        onSuccess={handleOnCreateSuccess}
        onCancel={handleOnCancel}
      />
    </>
  );
}
export { ProjectGroupListScreen };
