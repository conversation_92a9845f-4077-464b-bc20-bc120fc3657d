import { Box, Button, CircularProgress, Stack } from '@mui/material';
import { Label, Value } from '@/components/Display';
import { InfoCard, InfoCardContent, InfoCardHeader } from '@/components/InfoCard';
import { useEditProjectGroupDialog } from '@/components/ProjectGroup';
import { useProjectGroup } from '@/components/ProjectGroup/Provider';

function ProjectGroupDetailScreen({ projectGroupId }: { projectGroupId: number }) {
  const { projectGroup, isLoading, error, fetchProjectGroup } = useProjectGroup();

  const {
    EditProjectGroupDialog,
    open: openEditDialog,
    props: editDialogProps,
  } = useEditProjectGroupDialog();

  function handleEditButtonClick() {
    openEditDialog();
  }

  function handleDialogSuccess() {
    fetchProjectGroup(projectGroupId);
  }

  return (
    <>
      <InfoCard>
        <InfoCardHeader
          actions={
            <Button
              disabled={isLoading || !!error || projectGroup === undefined}
              variant={'contained'}
              onClick={handleEditButtonClick}
              disableElevation
            >
              編集
            </Button>
          }
        >
          案件グループ詳細
        </InfoCardHeader>
        <InfoCardContent>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <Stack spacing={2}>
              <Box>
                <Label>グループ名</Label>
                <Value>{projectGroup?.name}</Value>
              </Box>
              <Box>
                <Label>説明</Label>
                <Value>{projectGroup?.detail}</Value>
              </Box>
            </Stack>
          )}
        </InfoCardContent>
      </InfoCard>
      {projectGroup && (
        <EditProjectGroupDialog
          projectGroup={projectGroup}
          {...editDialogProps}
          onSuccess={handleDialogSuccess}
        />
      )}
    </>
  );
}

export { ProjectGroupDetailScreen };
