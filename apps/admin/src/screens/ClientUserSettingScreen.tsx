import { <PERSON>, Button, Chip, Stack, Typography } from '@mui/material';
import { useClientUserAvailableDialog } from '@/components/ClientUser/ClientUserAvailableDialog';
import { useClientUserDeleteDialog } from '@/components/ClientUser/ClientUserDeleteDialog';
import { useClientUserPasswordChangeDialog } from '@/components/ClientUser/ClientUserPasswordChangeDialog';
import { InfoCard, InfoCardContent, InfoCardHeader } from '@/components/InfoCard';
import { ClientUser } from '@/types';

function ClientUserSettingScreen({
  clientUser,
  onUserAvailableChange,
}: {
  clientUser: ClientUser | undefined;
  onUserAvailableChange: (clientUser: ClientUser) => void;
}) {
  const {
    ClientUserAvailableDialog,
    props: availableDialogProps,
    open: openAvailableDialog,
  } = useClientUserAvailableDialog();

  const {
    ClientUserPasswordChangeDialog,
    props: PasswordChangeDialogProps,
    open: openPasswordChangeDialog,
  } = useClientUserPasswordChangeDialog();

  const {
    ClientUserDeleteDialog,
    props: deleteDialogProps,
    openDeleteDialog,
  } = useClientUserDeleteDialog();

  return (
    <>
      <Stack spacing={4} maxWidth={'sm'}>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                disableElevation
                onClick={() => {
                  openAvailableDialog();
                }}
              >
                編集
              </Button>
            }
          >
            クライアントユーザー有効状態
          </InfoCardHeader>
          <InfoCardContent>
            {clientUser?.isAvailable ? (
              <Chip label='有効' color='primary' />
            ) : (
              <Chip label='無効' color='error' />
            )}
          </InfoCardContent>
        </InfoCard>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                disableElevation
                onClick={() => {
                  openPasswordChangeDialog();
                }}
              >
                変更
              </Button>
            }
          >
            パスワード変更
          </InfoCardHeader>
          <InfoCardContent>
            <Box>
              <Typography variant='body2'>
                パスワードを変更します。変更すると以前のパスワードではログインできなくなります。
              </Typography>
            </Box>
          </InfoCardContent>
        </InfoCard>
        <InfoCard>
          <InfoCardHeader
            actions={
              <Button
                variant={'contained'}
                color='error'
                disableElevation
                onClick={() => {
                  openDeleteDialog();
                }}
              >
                削除
              </Button>
            }
          >
            クライアントユーザーを削除
          </InfoCardHeader>
          <InfoCardContent>
            <Typography variant='body2' color={'error'}>
              クライアントユーザーを削除します。データは復元できないので、注意してください。
            </Typography>
          </InfoCardContent>
        </InfoCard>
      </Stack>

      {clientUser && (
        <>
          <ClientUserAvailableDialog
            {...availableDialogProps}
            clientUser={clientUser}
            onSubmit={onUserAvailableChange}
          />
          <ClientUserPasswordChangeDialog {...PasswordChangeDialogProps} clientUser={clientUser} />
          <ClientUserDeleteDialog {...deleteDialogProps} clientUser={clientUser} />
        </>
      )}
    </>
  );
}

export { ClientUserSettingScreen };
