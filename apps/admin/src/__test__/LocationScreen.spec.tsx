// TEST for LocationScreen
import { render, screen, waitForElementToBeRemoved } from '@testing-library/react';
import { ErrorProvider } from 'lib/ErrorAlert';
import { LocationScreen } from '@/screens/LocationScreen';

describe('LocationScreen', () => {
  it('適切に描画されていること', async () => {
    render(
      <ErrorAlertProvider>
        <LocationScreen />
      </ErrorAlertProvider>,
    );
    const loading = screen.getByText('ロケーションのデータがありません');
    await waitForElementToBeRemoved(loading);
    expect(loading).not.toBeInTheDocument();

    // 選択してください(ユニット)

    // 選択してください(データポイント)
  });
});

// List Location
// Show detail of Location
// Add Location

// List Units
// Show detail of Units
// Add Units

// List Datapoints
// Show detail of Datapoints
// Add Datapoints
