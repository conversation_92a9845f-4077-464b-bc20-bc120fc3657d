import { Box, BoxProps, Divider, Grid } from '@mui/material';
import { PropsWithChildren } from 'react';
import { useLayoutConstants } from '../Constants';
import { SubMenu, SubMenuItem } from '@/components/SubMenu';

function SideBarLayout({ children }: PropsWithChildren) {
  return (
    <Box display={'flex'} flex={1}>
      {children}
    </Box>
  );
}

function Menu({ children }: PropsWithChildren) {
  return <Box>{children}</Box>;
}

function Contents({ children, ...props }: PropsWithChildren<BoxProps>) {
  const { component } = useLayoutConstants();

  return (
    <Box
      flex={1}
      pl={2}
      display={'flex'}
      flexDirection={'column'}
      width={`calc(100% - ${component.subMenu.width}px)`}
      {...props}
    >
      {children}
    </Box>
  );
}

type MenuItemsProps = {
  items: { to: string; title: string }[];
};
function MenuItems({ items }: MenuItemsProps) {
  return (
    <Grid container>
      <Grid item>
        <SubMenu>
          {items.map((item) => (
            <SubMenuItem key={item.to} to={item.to} primary={item.title} />
          ))}
        </SubMenu>
      </Grid>
      <Divider orientation='vertical' flexItem />
    </Grid>
  );
}

// TODO projectMenuやAdminMenuを共通化するコンポートを作成する

SideBarLayout.Menu = Menu;
SideBarLayout.MenuItems = MenuItems;
SideBarLayout.Contents = Contents;

export { SideBarLayout };
