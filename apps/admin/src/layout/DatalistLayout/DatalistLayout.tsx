import { Grid, GridProps } from '@mui/material';
import { PropsWithChildren } from 'react';

function DatalistLayout({ children, ...props }: PropsWithChildren<GridProps>) {
  return (
    <Grid container spacing={2} {...props}>
      {children}
    </Grid>
  );
}
function ControlIsland({ children }: PropsWithChildren) {
  return (
    <Grid item xs={12} lg={3}>
      {children}
    </Grid>
  );
}

function DataIsland({ children, ...props }: PropsWithChildren<GridProps>) {
  return (
    <Grid item xs={12} lg={9} {...props}>
      {children}
    </Grid>
  );
}

DatalistLayout.ControlIsland = ControlIsland;
DatalistLayout.DataIsland = DataIsland;

export { DatalistLayout };
