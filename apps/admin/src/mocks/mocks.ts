// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { faker } from '@faker-js/faker/locale/ja';
import { Location, ProjectLocation } from '@/types';

/********************************
 *
 *   Project Locations
 *
 ********************************/

export const fakeLocations = (length = 20) =>
  Array.from({ length }).map<Location>((_, index) => ({
    id: index + 1,
    name: 'ロケーション' + (index + 1),
    address: 'アドレス' + (index + 1),
    backlogIssueKey: 'Backlog課題' + (index + 1),
  }));

export const fakeProjectLocations = Array.from({ length: 10 }).map<ProjectLocation>((_, index) => ({
  projectId: faker.datatype.number(),
  locationId: fakeLocations()[index].id,
  location: fakeLocations()[index],
}));
