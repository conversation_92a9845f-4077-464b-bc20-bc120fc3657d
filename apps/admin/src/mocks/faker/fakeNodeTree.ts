// eslint-disable-next-line
// @ts-nocheck

import { faker } from '@faker-js/faker/locale/ja';
import { TreeNode } from '../../types';

const colors: ('red' | 'blue' | 'green' | 'yellow')[] = ['red', 'blue', 'green', 'yellow'];
function fakeNode(): TreeNode {
  return {
    id: faker.datatype.number(),
    name: faker.datatype.string(),
    color: colors[faker.datatype.number({ min: 0, max: 3 })],
    order: faker.datatype.number(),
    parentId: faker.datatype.number(),
    projectId: faker.datatype.number(),
    datapoints: Array(faker.datatype.number({ min: 1, max: 3 }))
      .fill(0)
      .map(() => ({
        id: faker.datatype.number(),
        name: faker.datatype.string(),
        coefficient: faker.datatype.number({ min: 1, max: 3 }),
      })),
    children: [],
  };
}

function addChildren(node: TreeNode, depth: number) {
  if (depth === 0) {
    return;
  }
  const children = Array(faker.datatype.number({ min: 1, max: 3 }))
    .fill(0)
    .map(() => {
      return {
        ...fakeNode(),
      };
    });
  node.children = children;
  children.forEach((child) => {
    addChildren(child, depth - 1);
  });
}

const fakeNodeTree = sortByOrder(
  Array(5)
    .fill(0)
    .map(() => {
      const node = fakeNode();
      addChildren(node, faker.datatype.number({ min: 1, max: 3 }));
      return node;
    }),
);

function sortByOrder(nodes: TreeNode[]): TreeNode[] {
  // Base case: empty nodes array
  if (nodes.length === 0) {
    return [];
  }

  // Create a new array of sorted nodes based on the 'order' property, without modifying the original array
  const sortedNodes = nodes.slice().sort((a, b) => a.order - b.order);

  // Recursively sort the children of each node, creating new TreeNode objects to avoid modifying the original nodes
  const sortedNodesWithSortedChildren = sortedNodes.map((node) => {
    const sortedChildren = sortByOrder(node.children);
    return {
      ...node,
      children: sortedChildren,
    };
  });

  return sortedNodesWithSortedChildren;
}

export { fakeNodeTree };
