import { faker } from '@faker-js/faker';

const utilityContractTermMock = {
  //   utilityIds: [1, 2, 3],
  utilityIds: [1, 2, 3, 4, 5, 6, 7, 8, 9],
  //   contractTermIds: [1, 2, 3],
  contractTermIds: [1, 2, 3, 4, 5, 6, 7, 8, 9],
  values: () =>
    Array(12)
      .fill(0)
      .map((_, i) => ({
        month: i + 1,
        value: faker.datatype.number({ min: 100, max: 10000 }),
      })),
};

export default utilityContractTermMock;
