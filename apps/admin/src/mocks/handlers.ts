// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { faker } from '@faker-js/faker/locale/ja';
import { DefaultBodyType, PathParams, rest } from 'msw';
import { fakeNodeTree } from './faker/fakeNodeTree';
import utilityContractTermMock from './faker/utilityContractTermMock';
import { fakeLocations, fakeProjectLocations } from './mocks';
import {
  CreateAdminTokenResponseBody,
  CreateDatapointRequestBody,
  CreateDatapointResponse,
  CreateLocationRequestBody,
  CreateLocationResponse,
  CreateNodeRequestBody,
  CreateProjectRequestBody,
  CreateProjectResponse,
  CreateUnitRequestBody,
  CreateUnitResponse,
  DeleteDatapointResponse,
  DeleteLocationResponse,
  DeleteProjectLocationResponse,
  DeleteProjectResponse,
  DeleteUnitResponse,
  ListDatapointsResponse,
  ListLocationsResponse,
  ListProjectLocationsResponse,
  ListProjectsResponse,
  ListUnitsResponse,
  ReadAdminUserProfileResponse,
  ReadProjectResponse,
  ReplaceNodeDatapointRequestBody,
  ReplaceNodeDatapointResponse,
  UpdateDatapointRequestBody,
  UpdateDatapointResponse,
  UpdateLocationRequestBody,
  UpdateLocationResponse,
  UpdateNodeRequestBody,
  UpdateNodeResponse,
  UpdateProjectAvailableRequestBody,
  UpdateProjectAvailableResponse,
  UpsertProjectLocationsRequestBody,
  UpsertProjectLocationsResponse,
  UpdateProjectRequestBody,
  UpdateProjectResponse,
  UpdateUnitRequestBody,
  UpdateUnitResponse,
  ListNodesTreeResponse,
  ListProjectGroupsResponse,
  CreateProjectGroupRequestBody,
  CreateProjectGroupResponse,
  ReadProjectGroupResponse,
  UpdateProjectGroupRequestBody,
  UpdateProjectGroupResponse,
  ListProjectGroupMembersResponse,
  DeleteProjectGroupMemberResponse,
  DeleteProjectGroupMemberURLParameter,
  ListClientUsersQueryParameter,
  ListClientUsersResponse,
  ListProjectGroupClientUserPermissionsResponse,
  UpsertProjectGroupClientUserPermissionRequestBody,
  UpsertProjectGroupClientUserPermissionResponse,
  DeleteProjectGroupClientUserPermissionURLParameter,
  DeleteProjectGroupClientUserPermissionResponse,
  DeleteProjectGroupResponse,
  CreateContractTermRequestBody,
  CreateContractTermResponse,
  ListContractTermsResponse,
  ReadContractTermResponse,
  UpdateContractTermResponse,
  UpdateContractTermRequestBody,
  DeleteContractTermResponse,
  CreateUtilityRequestBody,
  CreateUtilityResponse,
  ListUtilitiesResponse,
  ReadUtilityResponse,
  UpdateUtilityRequestBody,
  UpdateUtilityResponse,
  DeleteUtilityResponse,
  ListReductionTargetDataResponse,
  ReplaceReductionTargetDataRequestBody,
  ReplaceReductionTargetDataResponse,
  ListStandardDataResponse,
  ReplaceStandardDataRequestBody,
  ReplaceStandardDataResponse,
  ListUnitcostsResponse,
  ReplaceUnitcostRequestBody,
  ReplaceUnitcostResponse,
  ListClientUserPermissionsResponse,
  ReadClientUserResponse,
  UpsertClientUserPermissionProjectGroupRequestBody,
  UpsertClientUserPermissionProjectGroupResponse,
  DeleteClientUserPermissionProjectGroupURLParameter,
  DeleteClientUserPermissionProjectGroupResponse,
  UpsertProjectGroupMembersRequestBody,
  UpsertProjectGroupMembersResponse,
} from '@/api/types';
import { Location, Unit, Datapoint, Project } from '@/types';

export const handlers = [
  // Handles a POST /login request
  rest.post('/auth', (req, res, ctx) => {
    console.log(req);
    return res(
      ctx.status(200),
      ctx.delay(1000),
      ctx.json({
        id: faker.datatype.uuid(),
        token: faker.datatype.string(10),
      }),
    );
  }),
  // Handles a GET /user request
  rest.get('/user', (_req, res, ctx) => {
    // If authenticated, return a mocked user details
    return res(
      ctx.status(200),
      ctx.json({
        id: faker.datatype.uuid(),
        firstName: faker.name.firstName(),
        lastName: faker.name.lastName(),
        email: faker.internet.email(),
      }),
    );
  }),

  /* 
  
  ListLocations
  
  */
  rest.get<DefaultBodyType, PathParams, ListLocationsResponse>(
    '/admin/locations',
    (_, res, ctx) => {
      const mock = fakeLocations();

      return res(
        ctx.status(200),
        ctx.json({
          locations: mock,
          units: [
            {
              id: faker.datatype.number(),
              locationId: faker.datatype.number(),
              no: faker.datatype.number(),
              name: faker.datatype.string(),
              sourceType: faker.datatype.string(),
              simId: faker.datatype.string(),
            },
          ],
          projects: [
            {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              isAvailable: faker.datatype.boolean(),
            },
          ],
          offset: 1,
          limit: 1,
        } as ListLocationsResponse),
        ctx.delay(500),
      );
    },
  ),

  /* 
  
  CreateLocation
  
  */
  rest.post<CreateLocationRequestBody, PathParams<string>, CreateLocationResponse>(
    '/admin/locations',
    async (req, res, ctx) => {
      const { name, backlogIssueKey } = await req.json<Partial<Location>>();
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          location: {
            id: faker.datatype.number(),
            name: name!,
            address: faker.datatype.string(),
            backlogIssueKey: backlogIssueKey ?? '',
          },
        }),
      );
    },
  ),
  /* 
  
  UpdateLocation
  
  */
  rest.patch<UpdateLocationRequestBody, PathParams<string>, UpdateLocationResponse>(
    '/admin/locations/:locationId',
    async (req, res, ctx) => {
      const { name, address, backlogIssueKey } = await req.json<Partial<Location>>();
      const locationId = req.params.locationId;
      console.log(name, address, backlogIssueKey);
      return res(
        ctx.status(200),
        ctx.json({
          location: {
            id: Number(locationId) || faker.datatype.number(),
            name: name! || faker.datatype.string(),
            address: address! || faker.datatype.string(),
            backlogIssueKey: backlogIssueKey! || faker.datatype.string(),
          },
        }),
      );
    },
  ),
  /* 
  
  DeleteLocation
  
  */
  rest.delete<DefaultBodyType, PathParams<string>, DeleteLocationResponse>(
    '/admin/locations/:locationId',
    async (req, res, ctx) => {
      const locationId = req.params.locationId;
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          locationId: Number(locationId) || faker.datatype.number(),
          deleted: true,
        }),
      );
    },
  ),
  /* 
  
  ListUnits
  
  */
  rest.get<DefaultBodyType, PathParams, ListUnitsResponse>('/admin/units', (req, res, ctx) => {
    const size = req.url.searchParams.get('size');

    const mock = Array(size ? Number(size) : 50)
      .fill(undefined)
      .map<Unit>((_, index) => ({
        id: index + 1,
        locationId: faker.datatype.number(),
        no: index + 1,
        name: 'ユニット' + (index + 1),
        sourceType: 'keyence',
        simId: faker.datatype.string(),
      }));

    return res(
      ctx.status(200),
      ctx.json({
        units: mock,
        datapoints: [
          {
            id: faker.datatype.number(),
            name: faker.datatype.string(),
            address: faker.datatype.string(),
            unitId: faker.datatype.number(),
            terminalId: faker.datatype.number(),
            pulserate: faker.datatype.number(),
            pulserateUnit: 'L',
            interval: faker.datatype.number(),
            calcMethod: faker.datatype.boolean(),
            isAvailable: faker.datatype.boolean(),
            deviceNo: faker.datatype.number(),
          },
        ],
        offset: 1,
        limit: 1,
      } as ListUnitsResponse),
      ctx.delay(500),
    );
  }),
  /* 
  
  CreateUnit
  
  */
  rest.post<CreateUnitRequestBody, PathParams<string>, CreateUnitResponse>(
    '/admin/units',
    async (req, res, ctx) => {
      const { name, no, sourceType, simId, locationId } = await req.json<Partial<Unit>>();
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          unit: {
            id: faker.datatype.number(),
            name: name!,
            no: no!,
            sourceType: sourceType!,
            simId: simId!,
            locationId: locationId!,
          },
        }),
      );
    },
  ),
  /* 
  
  UpdateUnit
  
  */
  rest.patch<UpdateUnitRequestBody, PathParams<string>, UpdateUnitResponse>(
    '/admin/units/:unitId',
    async (req, res, ctx) => {
      const { name, no, sourceType, simId, locationId } = await req.json<Partial<Unit>>();
      const unitId = req.params.unitId;
      return res(
        ctx.status(200),
        ctx.json({
          unit: {
            id: Number(unitId) || faker.datatype.number(),
            name: name! || faker.datatype.string(),
            no: no! || faker.datatype.number(),
            sourceType: sourceType! || faker.datatype.string(),
            simId: simId! || faker.datatype.string(),
            locationId: locationId! || faker.datatype.number(),
          },
        }),
      );
    },
  ),
  /* 
  
  DeleteUnit
  
  */
  rest.delete<DefaultBodyType, PathParams<string>, DeleteUnitResponse>(
    '/admin/units/:unitId',
    async (req, res, ctx) => {
      const unitId = req.params.unitId;
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          unitId: Number(unitId) || faker.datatype.number(),
          deleted: true,
        }),
      );
    },
  ),
  /* 
  
  ListDatapoints
  
  */
  rest.get<DefaultBodyType, PathParams, ListDatapointsResponse>(
    '/admin/datapoints',
    (req, res, ctx) => {
      const size = req.url.searchParams.get('size');

      const mock = Array(size ? Number(size) : 50)
        .fill(undefined)
        .map<Datapoint>((_, index) => ({
          id: index + 1,
          unitId: faker.datatype.number(),
          name: 'データポイント' + (index + 1),
          address: faker.datatype.string(),
          terminalId: faker.datatype.number(),
          pulserate: faker.datatype.number(),
          pulserateUnit: 'L',
          interval: faker.datatype.number(),
          calcMethod: faker.datatype.boolean(),
          isAvailable: faker.datatype.boolean(),
          deviceNo: faker.datatype.number(),
        }));

      return res(
        ctx.status(200),
        ctx.json({ datapoints: mock, offset: 1, limit: 1 }),
        ctx.delay(500),
      );
    },
  ),

  /* 
  
  CreateDatapoint
  
  */
  rest.post<CreateDatapointRequestBody, PathParams<string>, CreateDatapointResponse>(
    '/admin/datapoints',
    async (req, res, ctx) => {
      const {
        name,
        address,
        unitId,
        terminalId,
        pulserate,
        pulserateUnit,
        interval,
        calcMethod,
        isAvailable,
        deviceNo,
      } = await req.json<Partial<Datapoint>>();
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          datapoint: {
            id: faker.datatype.number(),
            name: name!,
            address: address!,
            unitId: unitId!,
            terminalId: terminalId!,
            pulserate: pulserate!,
            pulserateUnit: pulserateUnit!,
            interval: interval!,
            calcMethod: calcMethod!,
            isAvailable: isAvailable!,
            deviceNo: deviceNo!,
          },
        }),
      );
    },
  ),
  /* 
  
  UpdateDatapoint
  
  */
  rest.patch<UpdateDatapointRequestBody, PathParams<string>, UpdateDatapointResponse>(
    '/admin/datapoints/:datapointId',
    async (req, res, ctx) => {
      const {
        name,
        address,
        unitId,
        terminalId,
        pulserate,
        pulserateUnit,
        interval,
        calcMethod,
        isAvailable,
        deviceNo,
      } = await req.json<Partial<Datapoint>>();
      const datapointId = req.params.datapointId;
      return res(
        ctx.status(200),
        ctx.json({
          datapoint: {
            id: Number(datapointId) || faker.datatype.number(),
            name: name! || faker.datatype.string(),
            address: address! || faker.datatype.string(),
            unitId: unitId! || faker.datatype.number(),
            terminalId: terminalId! || faker.datatype.number(),
            pulserate: pulserate! || faker.datatype.number(),
            pulserateUnit: pulserateUnit! || faker.datatype.string(),
            interval: interval! || faker.datatype.number(),
            calcMethod: calcMethod! || faker.datatype.boolean(),
            isAvailable: isAvailable! || faker.datatype.boolean(),
            deviceNo: deviceNo! || faker.datatype.number(),
          },
        }),
      );
    },
  ),
  /* 
  
  DeleteDatapoint
  
  */
  rest.delete<DefaultBodyType, PathParams<string>, DeleteDatapointResponse>(
    '/admin/datapoints/:datapointId',
    async (req, res, ctx) => {
      const datapointId = req.params.datapointId;
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          datapointId: Number(datapointId) || faker.datatype.number(),
          deleted: true,
        }),
      );
    },
  ),
  /* 
  
  ListProjects
  
  */
  rest.get<DefaultBodyType, PathParams, ListProjectsResponse>(
    '/admin/projects',
    (_req, res, ctx) => {
      const mock = Array(50)
        .fill(undefined)
        .map<Project>((_, index) => ({
          id: index + 1,
          name: 'プロジェクト' + (index + 1),
          isAvailable: faker.datatype.boolean(),
          postcode: faker.datatype.string(),
          address: faker.datatype.string(),
          latitude: faker.datatype.number(),
          longitude: faker.datatype.number(),
          backlogIssueKey: faker.datatype.string(),
          isEnableSummaryScore: faker.datatype.boolean(),
          isEnableSummaryDetail: faker.datatype.boolean(),
          isEnableSummaryTotal: faker.datatype.boolean(),
          locations: [
            {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              address: faker.datatype.string(),
              backlogIssueKey: faker.datatype.string(),
            },
          ],
          projectGroups: [
            {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
            },
          ],
        }));

      return res(
        ctx.status(200),
        ctx.json({ projects: mock, offset: 1, limit: 1 }),
        ctx.delay(500),
      );
    },
  ),

  /* 
  
  ReadProject
  
  */
  rest.get<DefaultBodyType, PathParams, ReadProjectResponse>(
    '/admin/projects/:projectId',
    (req, res, ctx) => {
      const projectId = req.params.projectId;

      return res(
        ctx.status(200),
        ctx.json({
          project: {
            id: Number(projectId) || faker.datatype.number(),
            name: 'プロジェクト' + projectId,
            isAvailable: faker.datatype.boolean(),
            postcode: faker.datatype.string(),
            address: faker.datatype.string(),
            latitude: faker.datatype.number(),
            longitude: faker.datatype.number(),
            backlogIssueKey: faker.datatype.string(),
            isEnableSummaryScore: faker.datatype.boolean(),
            isEnableSummaryDetail: faker.datatype.boolean(),
            isEnableSummaryTotal: faker.datatype.boolean(),
            locations: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                address: faker.datatype.string(),
                backlogIssueKey: faker.datatype.string(),
              },
            ],
            projectGroups: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
              },
            ],
          },
        }),
        ctx.delay(500),
      );
    },
  ),

  /* 
  
  CreateProject
  
  */
  rest.post<CreateProjectRequestBody, PathParams<string>, CreateProjectResponse>(
    '/admin/projects',
    async (req, res, ctx) => {
      const { name } = await req.json<Partial<Project>>();
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          project: {
            id: faker.datatype.number(),
            name: name!,
            isAvailable: faker.datatype.boolean(),
            postcode: faker.datatype.string(),
            address: faker.datatype.string(),
            latitude: faker.datatype.number(),
            longitude: faker.datatype.number(),
            backlogIssueKey: faker.datatype.string(),
            isEnableSummaryScore: faker.datatype.boolean(),
            isEnableSummaryDetail: faker.datatype.boolean(),
            isEnableSummaryTotal: faker.datatype.boolean(),
            locations: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                address: faker.datatype.string(),
                backlogIssueKey: faker.datatype.string(),
              },
            ],
            projectGroups: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
              },
            ],
          },
        }),
      );
    },
  ),
  /* 
  
  UpdateProject
  
  */
  rest.patch<UpdateProjectRequestBody, PathParams<string>, UpdateProjectResponse>(
    '/admin/projects/:projectId',
    async (req, res, ctx) => {
      const projectId = req.params.projectId;
      return res(
        ctx.status(200),
        ctx.json({
          project: {
            id: Number(projectId) || faker.datatype.number(),
            name: faker.datatype.string(),
            isAvailable: faker.datatype.boolean(),
            postcode: faker.datatype.string(),
            address: faker.datatype.string(),
            latitude: faker.datatype.number(),
            longitude: faker.datatype.number(),
            backlogIssueKey: faker.datatype.string(),
            isEnableSummaryScore: faker.datatype.boolean(),
            isEnableSummaryDetail: faker.datatype.boolean(),
            isEnableSummaryTotal: faker.datatype.boolean(),
            locations: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                address: faker.datatype.string(),
                backlogIssueKey: faker.datatype.string(),
              },
            ],
            projectGroups: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
              },
            ],
          },
        }),
      );
    },
  ),
  /*  
  
  UpdateProjectAvailable
  
  */
  rest.patch<UpdateProjectAvailableRequestBody, PathParams<string>, UpdateProjectAvailableResponse>(
    '/admin/projects/:projectId/available',
    async (req, res, ctx) => {
      const projectId = req.params.projectId;
      return res(
        ctx.status(200),
        ctx.json({
          project: {
            id: Number(projectId) || faker.datatype.number(),
            name: faker.datatype.string(),
            isAvailable: faker.datatype.boolean(),
            postcode: faker.datatype.string(),
            address: faker.datatype.string(),
            latitude: faker.datatype.number(),
            longitude: faker.datatype.number(),
            backlogIssueKey: faker.datatype.string(),
            isEnableSummaryScore: faker.datatype.boolean(),
            isEnableSummaryDetail: faker.datatype.boolean(),
            isEnableSummaryTotal: faker.datatype.boolean(),
            locations: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                address: faker.datatype.string(),
                backlogIssueKey: faker.datatype.string(),
              },
            ],
            projectGroups: [
              {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
              },
            ],
          },
        }),
      );
    },
  ),
  /* 
  
  DeleteProject
  
  */
  rest.delete<DefaultBodyType, PathParams<string>, DeleteProjectResponse>(
    '/admin/projects/:projectId',
    async (req, res, ctx) => {
      const projectId = req.params.projectId;
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectId: Number(projectId) || faker.datatype.number(),
          deleted: true,
        }),
      );
    },
  ),
  /* 
  
  
  ListProjectLocations
  
  
  */
  rest.get<DefaultBodyType, PathParams, ListProjectLocationsResponse>(
    '/admin/projects/:projectId/locations',
    (req, res, ctx) => {
      const projectId = req.params.projectId;

      return res(
        ctx.status(200),
        ctx.json({
          projectId: Number(projectId) || faker.datatype.number(),
          locations: fakeLocations(),
          projectLocations: fakeProjectLocations,
        }),
        ctx.delay(500),
      );
    },
  ),
  /* 
  
  
  UpdateProjectLocations
  
  
  */
  rest.patch<UpsertProjectLocationsRequestBody, PathParams<string>, UpsertProjectLocationsResponse>(
    '/admin/projects/:projectId/locations',
    async (req, res, ctx) => {
      const projectId = req.params.projectId;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectId: Number(projectId) || faker.datatype.number(),
          locations: fakeLocations(),
          projectLocations: fakeProjectLocations,
        }),
      );
    },
  ),

  /* 
  
  
  DeleteProjectLocation
  
  
  */
  rest.delete<DefaultBodyType, PathParams<string>, DeleteProjectLocationResponse>(
    '/admin/projects/:projectId/locations/:locationId',
    async (req, res, ctx) => {
      const projectId = req.params.projectId;
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectId: Number(projectId) || faker.datatype.number(),
          locationId: Number(req.params.locationId) || faker.datatype.number(),
          deleted: true,
        }),
      );
    },
  ),

  /* 
  
  
  ListListNodes
  
   
  */
  rest.get<DefaultBodyType, PathParams<string>, ListNodesTreeResponse>(
    '/admin/projects/:projectId/nodes',
    (_, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          nodesTree: fakeNodeTree,
        }),
      );
    },
  ),
  /* 
  

  CreateNode
  

  */
  rest.post<CreateNodeRequestBody>('/admin/nodes', async (req, res, ctx) => {
    const requestNode = await req.json<CreateNodeRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        node: {
          id: faker.datatype.number(),
          ...requestNode,
          datapoints: [],
        },
      }),
    );
  }),
  /* 
  

  UpdateNode
  

  */
  rest.patch<UpdateNodeRequestBody, PathParams<string>, UpdateNodeResponse>(
    '/admin/nodes/:nodeId',
    async (req, res, ctx) => {
      const requestNode = await req.json<UpdateNodeRequestBody>();
      const nodeId = req.params.nodeId;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          node: {
            ...requestNode,
            id: Number(nodeId),
            projectId: faker.datatype.number(),
            name: requestNode.name ? requestNode.name : faker.datatype.string(),
            color: requestNode.color ? requestNode.color : 'blue',
            order: requestNode.order ? requestNode.order : 1,
            parentId: requestNode.parentId ? requestNode.parentId : 1,
          },
        }),
      );
    },
  ),
  /* 
  

  ReplaceNodeDatapoints
  

  */
  rest.put<ReplaceNodeDatapointRequestBody, PathParams<string>, ReplaceNodeDatapointResponse>(
    '/admin/nodes/:nodeId/datapoints',
    async (req, res, ctx) => {
      const requestNode = await req.json<ReplaceNodeDatapointRequestBody>();
      const nodeId = req.params.nodeId;
      const requestBody = requestNode.datapoints;
      const datapoints: Datapoint[] = requestNode.datapoints.map((datapoint) => ({
        id: datapoint.id ? datapoint.id : faker.datatype.number(),
        name: faker.datatype.string(),
        address: faker.datatype.string(),
        unitId: faker.datatype.number(),
        terminalId: faker.datatype.number(),
        pulserate: faker.datatype.number(),
        pulserateUnit: 'L',
        interval: faker.datatype.number(),
        calcMethod: faker.datatype.boolean(),
        isAvailable: faker.datatype.boolean(),
        deviceNo: faker.datatype.number(),
      }));

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          nodeDatapoints: requestBody.map((requestDatapoint) => {
            return {
              nodeId: Number(nodeId),
              datapointId: requestDatapoint.id,
              coefficient: requestDatapoint.coefficient,
              datapoint: datapoints.find(
                (datapoint) => datapoint.id === requestDatapoint.id,
              ) as Datapoint,
            };
          }),
        }),
      );
    },
  ),
  /* 
  

  Auth / アドミン認証
  CreateAdminToken
  

  */
  rest.post<DefaultBodyType, PathParams<string>, CreateAdminTokenResponseBody>(
    '/admin/auth/token',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          access_token: faker.datatype.string(),
          token_type: 'bearer',
          refresh_token: faker.datatype.string(),
        }),
      );
    },
  ),
  /* 
  

  Refresh token / リフレッシュトークン
  CreateAdminToken
  
  

  */
  rest.post<DefaultBodyType, PathParams<string>, CreateAdminTokenResponseBody>(
    '/admin/auth/token/refresh',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          access_token: faker.datatype.string(),
          token_type: 'bearer',
          refresh_token: faker.datatype.string(),
        }),
      );
    },
  ),
  /* 
  

  Profile / アドミンユーザプロフィール
  ReadAdminUserProfile
  
  

  */
  rest.get<DefaultBodyType, PathParams<string>, ReadAdminUserProfileResponse>(
    '/admin/users/me',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          adminUser: {
            createdAt: '2023-05-15T03:04:12.774Z',
            updatedAt: '2023-05-15T03:04:12.774Z',
            id: 0,
            username: 'username',
            isAvailable: true,
            email: '<EMAIL>',
            name: faker.name.firstName() + ' ' + faker.name.lastName(),
            company: 'company',
            department: 'department',
            position: 'position',
          },
        }),
      );
    },
  ),
  /* 
  
  ListClientUsers / クライアントユーザ

  
  */
  rest.get<
    DefaultBodyType,
    PathParams<keyof ListClientUsersQueryParameter>,
    ListClientUsersResponse
  >('/admin/client_users', async (_req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        clientUsers: [
          {
            createdAt: '2023-05-15T03:04:12.774Z',
            updatedAt: '2023-05-15T03:04:12.774Z',
            id: 0,
            username: 'username',
            isAvailable: true,
            email: '<EMAIL>',
            name: faker.name.firstName() + ' ' + faker.name.lastName(),
            company: 'company',
            department: 'department',
            position: 'position',
          },
        ],
        offset: 0,
        limit: 1,
      }),
    );
  }),

  /* 
  
  
  Project Groups / プロジェクトグループ
  
  
  */
  rest.get<DefaultBodyType, PathParams<string>, ListProjectGroupsResponse>(
    '/admin/project_groups',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectGroups: [
            {
              id: 1,
              name: '1',
              deleteProtection: true,
              detail: 'プロジェクトグループ1の詳細',
              projects: [
                {
                  id: 1,
                  name: 'デモ案件1',
                  isAvailable: true,
                },
              ],
              createdAt: '2023-05-15T03:04:12.774Z',
              updatedAt: '2023-05-15T03:04:12.774Z',
            },
            {
              id: 2,
              name: '2',
              deleteProtection: true,
              projects: [
                {
                  id: 1,
                  name: 'デモ案件1',
                  isAvailable: true,
                },
              ],
              detail: 'プロジェクトグループ2の詳細',
              createdAt: '2023-05-15T03:04:12.774Z',
              updatedAt: '2023-05-15T03:04:12.774Z',
            },
            {
              id: 3,
              name: '3',
              deleteProtection: true,
              projects: [
                {
                  id: 1,
                  name: 'デモ案件1',
                  isAvailable: true,
                },
              ],
              detail: 'プロジェクトグループ3の詳細',
              createdAt: '2023-05-15T03:04:12.774Z',
              updatedAt: '2023-05-15T03:04:12.774Z',
            },
          ],
          offset: 0,
          limit: -1,
        }),
      );
    },
  ),

  /* 
  
  CreateProjectGroup
  
  */
  rest.post<CreateProjectGroupRequestBody, PathParams<string>, CreateProjectGroupResponse>(
    '/admin/project_groups',
    async (req, res, ctx) => {
      const {
        name,
        deleteProtection = true,
        detail = '',
      } = await req.json<CreateProjectGroupRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectGroup: {
            id: 1,
            name,
            deleteProtection,
            detail,
            createdAt: '2023-05-15T03:04:12.774Z',
            updatedAt: '2023-05-15T03:04:12.774Z',
            projects: [],
          },
        }),
      );
    },
  ),
  /* 
  
  ReadProjectGroup
  
  */
  rest.get<DefaultBodyType, PathParams<'projectGroupId'>, ReadProjectGroupResponse>(
    '/admin/project_groups/:projectGroupId',
    async (req, res, ctx) => {
      const { projectGroupId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectGroup: {
            id: Number(projectGroupId),
            name: `プロジェクトグループ${Number(projectGroupId)}`,
            deleteProtection: true,
            detail: `プロジェクトグループ${Number(projectGroupId)}の詳細`,
            projects: [
              {
                id: 1,
                name: `デモ案件1 belongs with 案件グループ${projectGroupId}`,
                isAvailable: true,
              },
              {
                id: 2,
                name: `デモ案件2 belongs with 案件グループ${projectGroupId}`,
                isAvailable: true,
              },
              {
                id: 3,
                name: `デモ案件3 belongs with 案件グループ${projectGroupId}`,
                isAvailable: true,
              },
            ],
            createdAt: '2023-05-15T03:04:12.774Z',
            updatedAt: '2023-05-15T03:04:12.774Z',
          },
        }),
      );
    },
  ),

  /* 
  
  UpdateProjectGroup
  
  */
  rest.patch<
    UpdateProjectGroupRequestBody,
    PathParams<'projectGroupId'>,
    UpdateProjectGroupResponse
  >('/admin/project_groups/:projectGroupId', async (req, res, ctx) => {
    const { projectGroupId } = req.params;
    const { name, deleteProtection, detail } = await req.json<UpdateProjectGroupRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        projectGroup: {
          id: Number(projectGroupId),
          name: name ?? `プロジェクトグループ${Number(projectGroupId)}`,
          detail: detail ?? `プロジェクトグループ${Number(projectGroupId)}の詳細`,
          deleteProtection: deleteProtection ?? true,
          createdAt: '2023-05-15T03:04:12.774Z',
          updatedAt: '2023-05-15T03:04:12.774Z',
          projects: [
            {
              id: 1,
              name: `デモ案件1 belongs with 案件グループ${projectGroupId}`,
              isAvailable: true,
            },
            {
              id: 2,
              name: `デモ案件2 belongs with 案件グループ${projectGroupId}`,
              isAvailable: true,
            },
            {
              id: 3,
              name: `デモ案件3 belongs with 案件グループ${projectGroupId}`,
              isAvailable: true,
            },
          ],
        },
      }),
    );
  }),

  /* 
  
  DeleteProjectGroup
  
  */
  rest.delete<DefaultBodyType, PathParams<'projectGroupId'>, DeleteProjectGroupResponse>(
    '/admin/project_groups/:projectGroupId',
    async (req, res, ctx) => {
      const { projectGroupId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectGroupId: Number(projectGroupId),
          deleted: true,
        }),
      );
    },
  ),

  /* 


  ListProjectGroupsMembers / プロジェクトグループメンバー(案件)リスト
  
  
  */
  rest.get<DefaultBodyType, PathParams<'projectGroupId'>, ListProjectGroupMembersResponse>(
    '/admin/project_groups/:projectGroupId/members',
    async (req, res, ctx) => {
      const { projectGroupId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          projectGroupMembers: [
            {
              projectGroupId: Number(projectGroupId),
              projectId: 1,
              project: {
                id: 1,
                name: `デモ案件1 belongs with 案件グループ${projectGroupId}`,
                isEnableSummaryScore: true,
                isEnableSummaryDetail: true,
                isEnableSummaryTotal: true,
                isAvailable: true,
              },
            },
            {
              projectGroupId: Number(projectGroupId),
              projectId: 2,
              project: {
                id: 2,
                name: `デモ案件2 belongs with 案件グループ${projectGroupId}`,
                isEnableSummaryScore: true,
                isEnableSummaryDetail: true,
                isEnableSummaryTotal: true,
                isAvailable: true,
              },
            },
            {
              projectGroupId: Number(projectGroupId),
              projectId: 3,
              project: {
                id: 3,
                name: `デモ案件3 belongs with 案件グループ${projectGroupId}`,
                isEnableSummaryScore: true,
                isEnableSummaryDetail: true,
                isEnableSummaryTotal: true,
                isAvailable: true,
              },
            },
          ],
        }),
      );
    },
  ),

  /* 
  
  CreateProjectGroupMember
  
  */
  rest.post<
    UpsertProjectGroupMembersRequestBody,
    PathParams<'projectGroupId'>,
    UpsertProjectGroupMembersResponse
  >('/admin/project_groups/:projectGroupId/members', async (req, res, ctx) => {
    const { projectGroupId } = req.params;
    const { projectIds } = await req.json<UpsertProjectGroupMembersRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        projectGroupMembers: projectIds.map((projectId) => ({
          projectGroupId: Number(projectGroupId),
          projectId,
          project: {
            id: projectId,
            name: `デモ案件 + ${projectId} belongs with 案件グループ${projectGroupId}`,
            isEnableSummaryScore: true,
            isEnableSummaryDetail: true,
            isEnableSummaryTotal: true,
            isAvailable: true,
          },
        })),
      }),
    );
  }),

  /* 
  
  DeleteProjectGroupMember
  
  */
  rest.delete<
    DefaultBodyType,
    PathParams<keyof DeleteProjectGroupMemberURLParameter>,
    DeleteProjectGroupMemberResponse
  >('/admin/project_groups/:projectGroupId/members/:projectId', async (req, res, ctx) => {
    const { projectGroupId, projectId } = req.params;

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        projectGroupId: Number(projectGroupId),
        projectId: Number(projectId),
        deleted: true,
      }),
    );
  }),
  /* 


  ReadProjectGroupsClientUserPermissions / プロジェクトグループクライアントユーザーリスト
  
  
  */
  rest.get<
    DefaultBodyType,
    PathParams<'projectGroupId'>,
    ListProjectGroupClientUserPermissionsResponse
  >('/admin/project_groups/:projectGroupId/client_users', async (req, res, ctx) => {
    const { projectGroupId } = req.params;

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        clientUsers: [
          {
            id: 1,
            name: `クライアントユーザー 1 in 案件グループ${projectGroupId}`,
            username: `client_user_1`,
            isAvailable: true,
            email: faker.internet.email(),
          },
          {
            id: 2,
            name: `クライアントユーザー 2 in 案件グループ${projectGroupId}`,
            username: `client_user_2`,
            isAvailable: true,
            email: faker.internet.email(),
          },
          {
            id: 3,
            name: `クライアントユーザー 3 in 案件グループ${projectGroupId}`,
            username: `client_user_3`,
            isAvailable: true,
            email: faker.internet.email(),
          },
        ],
      }),
    );
  }),

  /* 
  
  UpsertProjectGroupClientUserPermission
  
  */
  rest.post<
    UpsertProjectGroupClientUserPermissionRequestBody,
    PathParams<'projectGroupId'>,
    UpsertProjectGroupClientUserPermissionResponse
  >('/admin/project_groups/:projectGroupId/client_users', async (req, res, ctx) => {
    const { projectGroupId } = req.params;
    const { clientUserIds } = await req.json<UpsertProjectGroupClientUserPermissionRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        clientUsers: clientUserIds.map((clientUserId) => ({
          id: clientUserId,
          name: `クライアントユーザー ${clientUserId} in 案件グループ${projectGroupId}`,
          username: `client_user_${clientUserId}}`,
          isAvailable: true,
          company: '株式会社デモ',
          email: '',
          department: '営業部',
          position: '部長',
        })),
      }),
    );
  }),

  /* 
  
  DeleteProjectGroupClientUserPermission
  
  */
  rest.delete<
    DefaultBodyType,
    PathParams<keyof DeleteProjectGroupClientUserPermissionURLParameter>,
    DeleteProjectGroupClientUserPermissionResponse
  >('/admin/project_groups/:projectGroupId/client_users/:clientUserId', async (req, res, ctx) => {
    const { projectGroupId, clientUserId } = req.params;

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        projectGroupId: Number(projectGroupId),
        clientUserId: Number(clientUserId),
        deleted: true,
      }),
    );
  }),

  /* 
  
  CreateContractTerm
  
  */

  rest.post<CreateContractTermRequestBody, PathParams, CreateContractTermResponse>(
    '/admin/contract_terms',
    async (req, res, ctx) => {
      const { projectId, startAt, endAt } = await req.json<CreateContractTermRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          contractTerm: {
            id: 1,
            projectId,
            startAt,
            endAt,
          },
        }),
      );
    },
  ),

  /* 
   
  ListContractTerm
  
  */
  rest.get<DefaultBodyType, PathParams<'projectIds'>, ListContractTermsResponse>(
    '/admin/contract_terms',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          contractTerms: utilityContractTermMock.utilityIds.map((utilityId) => ({
            id: utilityId,
            projectId: 1,
            startAt: '2021-01-01T06:07:32.469Z',
            endAt: '2021-12-31T06:07:32.469Z',
          })),
        }),
      );
    },
  ),

  /* 
  
  ReadContractTerm
  
  */
  rest.get<DefaultBodyType, PathParams<'contractTermId'>, ReadContractTermResponse>(
    '/admin/contract_terms/:contractTermId',
    async (req, res, ctx) => {
      const { contractTermId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          contractTerm: {
            id: Number(contractTermId),
            projectId: 1,
            startAt: '2021-01-01T06:07:32.469Z',
            endAt: '2021-12-31T06:07:32.469Z',
          },
        }),
      );
    },
  ),

  /* 
   
  UpdateContractTerm
  
  */
  rest.patch<
    UpdateContractTermRequestBody,
    PathParams<'contractTermId'>,
    UpdateContractTermResponse
  >('/admin/contract_terms/:contractTermId', async (req, res, ctx) => {
    const { contractTermId } = req.params;
    const { startAt, endAt } = await req.json<UpdateContractTermRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        contractTerm: {
          id: Number(contractTermId),
          projectId: 1,
          startAt,
          endAt,
        },
      }),
    );
  }),

  /* 
   
  DeleteContractTerm 
  
  */
  rest.delete<DefaultBodyType, PathParams<'contractTermId'>, DeleteContractTermResponse>(
    '/admin/contract_terms/:contractTermId',
    async (req, res, ctx) => {
      const { contractTermId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          contractTermId: Number(contractTermId),
          deleted: true,
        }),
      );
    },
  ),

  /*  
  
  CreateUtility
  
  */
  rest.post<CreateUtilityRequestBody, PathParams, CreateUtilityResponse>(
    '/admin/utilities',
    async (req, res, ctx) => {
      const {
        name,
        projectId,
        nodeId = 1,
        order = 1,
        isEnableSummary = true,
      } = await req.json<CreateUtilityRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          utility: {
            id: 1,
            name,
            projectId,
            nodeId,
            isEnableSummary,
            order,
          },
        }),
      );
    },
  ),

  /* 
   
  ListUtilities
  
  */

  rest.get<DefaultBodyType, PathParams<'projectIds'>, ListUtilitiesResponse>(
    '/admin/utilities',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          utilities: utilityContractTermMock.utilityIds.map((utilityId) => ({
            id: utilityId,
            name: 'Utility ' + utilityId,
            projectId: 1,
            isEnableSummary: true,
            nodeId: 1,
            order: 1,
          })),
        }),
      );
    },
  ),

  /* 
  
  ReadUtility
  
  */
  rest.get<DefaultBodyType, PathParams<'utilityId'>, ReadUtilityResponse>(
    '/admin/utilities/:utilityId',
    async (req, res, ctx) => {
      const { utilityId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          utility: {
            id: Number(utilityId),
            name: 'Utility 1',
            projectId: 1,
            isEnableSummary: true,
            nodeId: 1,
            order: 1,
          },
        }),
      );
    },
  ),

  /* 
   
  UpdateUtility
  
  */
  rest.patch<UpdateUtilityRequestBody, PathParams<'utilityId'>, UpdateUtilityResponse>(
    '/admin/utilities/:utilityId',
    async (req, res, ctx) => {
      const { utilityId } = req.params;
      const {
        name = 'test',
        projectId = 1,
        nodeId = 1,
        isEnableSummary = true,
        order = 1,
      } = await req.json<UpdateUtilityRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          utility: {
            id: Number(utilityId),
            name,
            projectId,
            nodeId,
            isEnableSummary,
            order,
          },
        }),
      );
    },
  ),

  /* 
   
  DeleteUtility 
  
  */
  rest.delete<DefaultBodyType, PathParams<'utilityId'>, DeleteUtilityResponse>(
    '/admin/utilities/:utilityId',
    async (req, res, ctx) => {
      const { utilityId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          utilityId: Number(utilityId),
          deleted: true,
        }),
      );
    },
  ),

  /* 
   
  ListReductionTargetData

  */
  rest.get<DefaultBodyType, PathParams<'projectIds'>, ListReductionTargetDataResponse>(
    '/admin/reduction_target_data',
    async (_req, res, ctx) => {
      const { utilityIds, contractTermIds, values } = utilityContractTermMock;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          reductionTargetData: utilityIds
            .map((utilityId) =>
              contractTermIds.map((contractTermId) => ({
                utilityId,
                contractTermId,
                values: values(),
                projectId: 1,
              })),
            )
            .flat(),
        }),
      );
    },
  ),

  /* 
   
  ReplaceReductionTargetData
  
  */
  rest.put<ReplaceReductionTargetDataRequestBody, PathParams, ReplaceReductionTargetDataResponse>(
    '/admin/reduction_target_data',
    async (req, res, ctx) => {
      const { utilityId, contractTermId } = await req.json<ReplaceReductionTargetDataRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          reductionTargetData: [
            {
              utilityId,
              contractTermId,
              values: Array(12).map((_, i) => ({
                month: i + 1,
                value: 10,
              })),
              projectId: 1,
            },
          ],
        }),
      );
    },
  ),

  /*   
  
  ListStandardData
  
  */
  rest.get<DefaultBodyType, PathParams<'projectIds'>, ListStandardDataResponse>(
    '/admin/standard_data',
    async (_req, res, ctx) => {
      const { utilityIds, contractTermIds, values } = utilityContractTermMock;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          standardData: utilityIds
            .map((utilityId) =>
              contractTermIds.map((contractTermId) => ({
                utilityId,
                contractTermId,
                values: values(),
                projectId: 1,
              })),
            )
            .flat(),
        }),
      );
    },
  ),

  /* 
   
  ReplaceStandardData
  
  */

  rest.put<ReplaceStandardDataRequestBody, PathParams, ReplaceStandardDataResponse>(
    '/admin/standard_data',
    async (req, res, ctx) => {
      const { utilityId, contractTermId } = await req.json<ReplaceStandardDataRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          standardData: [
            {
              utilityId,
              contractTermId,
              values: Array(12).map((_, i) => ({
                month: i + 1,
                value: 10,
              })),
              projectId: 1,
            },
          ],
        }),
      );
    },
  ),

  /* 
 
  ListUnitcosts 
 
  */
  rest.get<DefaultBodyType, PathParams<'projectIds'>, ListUnitcostsResponse>(
    '/admin/unitcosts',
    async (_req, res, ctx) => {
      const { utilityIds, contractTermIds } = utilityContractTermMock;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          unitcosts: utilityIds
            .map((utilityId) =>
              contractTermIds.map((contractTermId) => ({
                utilityId,
                contractTermId,
                value: Math.floor(Math.random() * 10000),
                projectId: 1,
              })),
            )
            .flat(),
        }),
      );
    },
  ),

  /* 
 
  ReplaceUnitcosts  
 
  */
  rest.put<ReplaceUnitcostRequestBody, PathParams, ReplaceUnitcostResponse>(
    '/admin/unitcosts',
    async (req, res, ctx) => {
      const { utilityId, contractTermId, value = 1 } = await req.json<ReplaceUnitcostRequestBody>();

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          unitcost: {
            utilityId,
            contractTermId,
            value,
            projectId: 1,
          },
        }),
      );
    },
  ),
  /* 
  
  ReadClientUser
  
  */
  rest.get<DefaultBodyType, PathParams<'clientUserId'>, ReadClientUserResponse>(
    '/admin/client_users/:clientUserId',
    async (req, res, ctx) => {
      const { clientUserId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          clientUser: {
            id: Number(clientUserId),
            name: 'テストクライアントユーザー',
            email: '<EMAIL>',
            username: 'testClientUser',
            company: 'テスト会社',
            department: 'テスト部門',
            position: 'テスター',
            isAvailable: true,
          },
        }),
      );
    },
  ),

  /* 
 
 ListClientUserPermissions

  */
  rest.get<DefaultBodyType, PathParams<'clientUserId'>, ListClientUserPermissionsResponse>(
    '/admin/client_users/:clientUserId/permissions',
    async (_req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          clientUserPermissions: {
            projects: [
              {
                id: 1,
                name: 'testProject',
                isAvailable: true,
              },
            ],
            projectGroups: [
              {
                id: 1,
                name: 'testProjectGroup',
                detail: 'testProject GroupDetail',
              },
            ],
          },
        }),
      );
    },
  ),
  /* 
  
  UpsertClientUserPermissionProjectGroup
  
  */
  rest.patch<
    UpsertClientUserPermissionProjectGroupRequestBody,
    PathParams<'clientUserId'>,
    UpsertClientUserPermissionProjectGroupResponse
  >('/admin/client_users/:clientUserId/permissions/project_groups', async (req, res, ctx) => {
    const { clientUserId } = req.params;
    const { projectGroupIds } = await req.json<UpsertClientUserPermissionProjectGroupRequestBody>();

    return res(
      ctx.status(200),
      ctx.delay(500),
      ctx.json({
        clientUserPermissionProjectGroups: projectGroupIds.map((projectGroupId) => ({
          clientId: Number(clientUserId),
          projectGroupId: projectGroupId,
          projectGroup: {
            id: projectGroupId,
            name: 'testProjectGroup ' + projectGroupId,
            detail: 'testProject GroupDetail ' + projectGroupId,
          },
        })),
      }),
    );
  }),

  /* 
  
  DeleteClientUserPermissionProjectGroup
  
  */
  rest.delete<
    DefaultBodyType,
    PathParams<keyof DeleteClientUserPermissionProjectGroupURLParameter>,
    DeleteClientUserPermissionProjectGroupResponse
  >(
    '/admin/client_users/:clientUserId/permissions/project_groups/:projectGroupId',
    async (req, res, ctx) => {
      const { clientUserId, projectGroupId } = req.params;

      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          clientId: Number(clientUserId),
          projectGroupId: Number(projectGroupId),
          deleted: true,
        }),
      );
    },
  ),
];
