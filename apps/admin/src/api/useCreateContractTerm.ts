import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateContractTermRequestBody, CreateContractTermResponse } from '@/api/types';

function useCreateContractTerm() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateContractTermResponse,
    { body: CreateContractTermRequestBody }
  >(Endpoints.CreateContractTerm, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateContractTerm };
