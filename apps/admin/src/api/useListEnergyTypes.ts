import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListEnergyTypesResponse } from '@/api/types';

function useListEnergyTypes() {
  const { data, error, isMutating, trigger, ...props } =
    useEcoisSWRMutation<ListEnergyTypesResponse>(Endpoints.ListEnergyTypes, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListEnergyTypes };
