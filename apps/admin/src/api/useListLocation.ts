import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListLocationsQueryParameter, ListLocationsResponse } from '@/api/types';

function useListLocation() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListLocationsResponse,
    {
      queryParameter?: ListLocationsQueryParameter;
    }
  >(Endpoints.ListLocations, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListLocation };
