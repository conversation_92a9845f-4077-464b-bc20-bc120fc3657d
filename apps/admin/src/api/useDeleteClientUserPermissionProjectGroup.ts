import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  DeleteClientUserPermissionProjectGroupResponse,
  DeleteClientUserPermissionProjectGroupURLParameter,
} from './types';

function useDeleteClientUserPermissionProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteClientUserPermissionProjectGroupResponse,
    {
      urlParameter: DeleteClientUserPermissionProjectGroupURLParameter;
    }
  >(Endpoints.DeleteClientUserPermissionProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteClientUserPermissionProjectGroup };
