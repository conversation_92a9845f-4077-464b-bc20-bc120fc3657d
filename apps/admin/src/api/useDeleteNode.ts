import { abstractFetcher, useEcoisSWRMutation, Endpoints } from 'lib/api';
import { DeleteNodeResponse, DeleteNodeURLParameter } from './types';

function useDeleteNode() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteNodeResponse,
    {
      urlParameter: DeleteNodeURLParameter;
    }
  >(Endpoints.DeleteNode, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteNode };
