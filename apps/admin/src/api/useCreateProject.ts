import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateProjectRequestBody, CreateProjectResponse } from '@/api/types';

function useCreateProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateProjectResponse,
    { body: CreateProjectRequestBody }
  >(Endpoints.CreateProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateProject };
