import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateContractTermRequestBody,
  UpdateContractTermResponse,
  UpdateContractTermURLParameter,
} from './types';

function useUpdateContractTerm() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateContractTermResponse,
    {
      body: UpdateContractTermRequestBody;
      urlParameter: UpdateContractTermURLParameter;
    }
  >(Endpoints.UpdateContractTerm, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateContractTerm };
