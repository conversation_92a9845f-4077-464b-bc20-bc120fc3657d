import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateClientUserAvailableRequestBody,
  UpdateClientUserAvailableResponse,
  UpdateClientUserAvailableURLParameter,
} from './types';

function useUpdateClientUserAvailable() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateClientUserAvailableResponse,
    {
      body: UpdateClientUserAvailableRequestBody;
      urlParameter: UpdateClientUserAvailableURLParameter;
    }
  >(Endpoints.UpdateClientUserAvailable, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateClientUserAvailable };
