import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateDatapointRequestBody,
  UpdateDatapointResponse,
  UpdateDatapointURLParameter,
} from './types';

function useUpdateDatapoint() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateDatapointResponse,
    {
      body: UpdateDatapointRequestBody;
      urlParameter: UpdateDatapointURLParameter;
    }
  >(Endpoints.UpdateDatapoint, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateDatapoint };
