import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListStandardDataQueryParameter, ListStandardDataResponse } from '@/api/types';

function useListStandardData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListStandardDataResponse,
    {
      queryParameter?: ListStandardDataQueryParameter;
    }
  >(Endpoints.ListStandardData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListStandardData };
