import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateClientUserPasswordRequestBody,
  UpdateClientUserPasswordResponse,
  UpdateClientUserPasswordURLParameter,
} from './types';

function useUpdateClientUserPassword() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateClientUserPasswordResponse,
    {
      body: UpdateClientUserPasswordRequestBody;
      urlParameter: UpdateClientUserPasswordURLParameter;
    }
  >(Endpoints.UpdateClientUserPassword, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateClientUserPassword };
