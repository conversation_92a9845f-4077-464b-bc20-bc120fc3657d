import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateProjectRequestBody,
  UpdateProjectResponse,
  UpdateProjectURLParameter,
} from './types';

function useUpdateProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateProjectResponse,
    {
      body: UpdateProjectRequestBody;
      urlParameter: UpdateProjectURLParameter;
    }
  >(Endpoints.UpdateProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateProject };
