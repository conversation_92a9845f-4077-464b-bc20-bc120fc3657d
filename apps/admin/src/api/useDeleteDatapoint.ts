import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteDatapointResponse, DeleteDatapointURLParameter } from './types';

function useDeleteDatapoint() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteDatapointResponse,
    {
      urlParameter: DeleteDatapointURLParameter;
    }
  >(Endpoints.DeleteDatapoint, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteDatapoint };
