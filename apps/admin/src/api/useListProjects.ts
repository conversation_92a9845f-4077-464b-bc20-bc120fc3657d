import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListProjectsQueryParameter, ListProjectsResponse } from '@/api/types';

function useListProjects() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListProjectsResponse,
    {
      queryParameter?: ListProjectsQueryParameter;
    }
  >(Endpoints.ListProjects, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListProjects };
