import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateUtilityRequestBody, CreateUtilityResponse } from '@/api/types';

function useCreateUtility() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateUtilityResponse,
    { body: CreateUtilityRequestBody }
  >(Endpoints.CreateUtility, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateUtility };
