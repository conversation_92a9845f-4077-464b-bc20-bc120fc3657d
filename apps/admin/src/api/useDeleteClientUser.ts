import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteClientUserResponse, DeleteClientUserURLParameter } from './types';

function useDeleteClientUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteClientUserResponse,
    {
      urlParameter: DeleteClientUserURLParameter;
    }
  >(Endpoints.DeleteClientUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteClientUser };
