import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteProjectGroupResponse, DeleteProjectGroupURLParameter } from './types';

function useDeleteProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteProjectGroupResponse,
    {
      urlParameter: DeleteProjectGroupURLParameter;
    }
  >(Endpoints.DeleteProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteProjectGroup };
