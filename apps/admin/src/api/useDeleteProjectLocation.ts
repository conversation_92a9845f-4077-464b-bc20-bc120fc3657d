import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteProjectLocationResponse, DeleteProjectLocationURLParameter } from './types';

function useDeleteProjectLocation() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteProjectLocationResponse,
    {
      urlParameter: DeleteProjectLocationURLParameter;
    }
  >(Endpoints.DeleteProjectLocation, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteProjectLocation };
