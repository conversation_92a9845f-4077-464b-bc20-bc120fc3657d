import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import {
  UpsertProjectGroupClientUserPermissionRequestBody,
  UpsertProjectGroupClientUserPermissionResponse,
  UpsertProjectGroupClientUserPermissionURLParameter,
} from '@/api/types';

function useUpsertProjectGroupClientUserPermission() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpsertProjectGroupClientUserPermissionResponse,
    {
      body: UpsertProjectGroupClientUserPermissionRequestBody;
      urlParameter: UpsertProjectGroupClientUserPermissionURLParameter;
    }
  >(Endpoints.UpsertProjectGroupClientUserPermission, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpsertProjectGroupClientUserPermission };
