import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteUtilityResponse, DeleteUtilityURLParameter } from './types';

function useDeleteUtility() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteUtilityResponse,
    {
      urlParameter: DeleteUtilityURLParameter;
    }
  >(Endpoints.DeleteUtility, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteUtility };
