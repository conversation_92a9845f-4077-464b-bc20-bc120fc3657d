// File: useListUtilities.ts
import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListUtilitiesQueryParameter, ListUtilitiesResponse } from '@/api/types';

function useListUtilities() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListUtilitiesResponse,
    {
      queryParameter?: ListUtilitiesQueryParameter;
    }
  >(Endpoints.ListUtilities, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListUtilities };
