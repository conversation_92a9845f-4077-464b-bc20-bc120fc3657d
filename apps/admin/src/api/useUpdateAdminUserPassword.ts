import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateAdminUserPasswordRequestBody,
  UpdateAdminUserPasswordResponse,
  UpdateAdminUserPasswordURLParameter,
} from './types';

function useUpdateAdminUserPassword() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateAdminUserPasswordResponse,
    {
      body: UpdateAdminUserPasswordRequestBody;
      urlParameter: UpdateAdminUserPasswordURLParameter;
    }
  >(Endpoints.UpdateAdminUserPassword, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateAdminUserPassword };
