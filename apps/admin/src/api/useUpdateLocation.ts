import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateLocationRequestBody,
  UpdateLocationResponse,
  UpdateLocationURLParameter,
} from './types';

function useUpdateLocation() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateLocationResponse,
    {
      body: UpdateLocationRequestBody;
      urlParameter: UpdateLocationURLParameter;
    }
  >(Endpoints.UpdateLocation, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateLocation };
