import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateAdminUserRequestBody, CreateAdminUserResponse } from '@/api/types';

function useCreateAdminUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateAdminUserResponse,
    { body: CreateAdminUserRequestBody }
  >(Endpoints.CreateAdminUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateAdminUser };
