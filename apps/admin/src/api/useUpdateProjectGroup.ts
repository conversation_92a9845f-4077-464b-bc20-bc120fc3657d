import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateProjectGroupRequestBody,
  UpdateProjectGroupResponse,
  UpdateProjectGroupURLParameter,
} from './types';

function useUpdateProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateProjectGroupResponse,
    {
      body: UpdateProjectGroupRequestBody;
      urlParameter: UpdateProjectGroupURLParameter;
    }
  >(Endpoints.UpdateProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateProjectGroup };
