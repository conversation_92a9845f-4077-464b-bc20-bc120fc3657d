import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpsertProjectLocationsRequestBody,
  UpsertProjectLocationsResponse,
  UpsertProjectLocationsURLParameter,
} from './types';

function useUpsertProjectLocations() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpsertProjectLocationsResponse,
    {
      body: UpsertProjectLocationsRequestBody;
      urlParameter: UpsertProjectLocationsURLParameter;
    }
  >(Endpoints.UpsertProjectLocations, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpsertProjectLocations };
