import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteUnitResponse, DeleteUnitURLParameter } from './types';

function useDeleteUnit() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteUnitResponse,
    {
      urlParameter: DeleteUnitURLParameter;
    }
  >(Endpoints.DeleteUnit, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteUnit };
