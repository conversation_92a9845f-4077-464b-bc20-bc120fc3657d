import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteContractTermResponse, DeleteContractTermURLParameter } from './types';

function useDeleteContractTerm() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteContractTermResponse,
    {
      urlParameter: DeleteContractTermURLParameter;
    }
  >(Endpoints.DeleteContractTerm, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteContractTerm };
