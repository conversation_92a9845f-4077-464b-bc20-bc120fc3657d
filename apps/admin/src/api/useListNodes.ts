import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListNodesQueryParameter, ListNodesResponse } from './types';

function useListNodes() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListNodesResponse,
    {
      queryParameter: ListNodesQueryParameter;
    }
  >(Endpoints.ListNodes, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListNodes };
