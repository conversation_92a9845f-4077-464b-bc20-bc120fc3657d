import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { CreateNodeRequestBody, CreateNodeResponse } from './types';

function useCreateNode() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateNodeResponse,
    {
      body: CreateNodeRequestBody;
    }
  >(Endpoints.CreateNode, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateNode };
