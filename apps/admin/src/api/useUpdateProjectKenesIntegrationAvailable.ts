import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateProjectKenesIntegrationAvailableRequestBody,
  UpdateProjectKenesIntegrationAvailableURLParameter,
  UpdateProjectKenesIntegrationAvailableResponse,
} from './types/UpdateProjectKenesIntegrationAvailable';

function useUpdateProjectKenesIntegrationAvailable() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateProjectKenesIntegrationAvailableResponse,
    {
      body: UpdateProjectKenesIntegrationAvailableRequestBody;
      urlParameter: UpdateProjectKenesIntegrationAvailableURLParameter;
    }
  >(Endpoints.UpdateProjectKenesIntegrationAvailable, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateProjectKenesIntegrationAvailable };
