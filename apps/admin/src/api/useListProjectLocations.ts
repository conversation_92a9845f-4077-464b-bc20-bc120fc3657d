import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ListProjectLocationsResponse, ListProjectLocationsURLParameter } from './types';

function useListProjectLocations() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListProjectLocationsResponse,
    {
      urlParameter: ListProjectLocationsURLParameter;
    }
  >(Endpoints.ListProjectLocations, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListProjectLocations };
