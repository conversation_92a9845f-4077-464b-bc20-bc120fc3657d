import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateProjectGroupRequestBody, CreateProjectGroupResponse } from '@/api/types';

function useCreateProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateProjectGroupResponse,
    { body: CreateProjectGroupRequestBody }
  >(Endpoints.CreateProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateProjectGroup };
