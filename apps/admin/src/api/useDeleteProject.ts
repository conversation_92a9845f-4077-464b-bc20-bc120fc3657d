import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteProjectResponse, DeleteProjectURLParameter } from './types';

function useDeleteProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteProjectResponse,
    {
      urlParameter: DeleteProjectURLParameter;
    }
  >(Endpoints.DeleteProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteProject };
