import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListUnitsQueryParameter, ListUnitsResponse } from '@/api/types';

function useListUnits() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListUnitsResponse,
    {
      queryParameter?: ListUnitsQueryParameter;
    }
  >(Endpoints.ListUnits, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListUnits };
