import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadProjectGroupResponse, ReadProjectGroupURLParameter } from './types';

function useReadProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadProjectGroupResponse,
    {
      urlParameter: ReadProjectGroupURLParameter;
    }
  >(Endpoints.ReadProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadProjectGroup };
