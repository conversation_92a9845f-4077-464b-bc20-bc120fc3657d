import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ListProjectGroupsQueryParameter, ListProjectGroupsResponse } from './types';

function useListProjectGroups() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListProjectGroupsResponse,
    {
      queryParameter: ListProjectGroupsQueryParameter;
    }
  >(Endpoints.ListProjectGroups, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListProjectGroups };
