import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteProjectGroupMemberResponse, DeleteProjectGroupMemberURLParameter } from './types';

function useDeleteProjectGroupMember() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteProjectGroupMemberResponse,
    {
      urlParameter: DeleteProjectGroupMemberURLParameter;
    }
  >(Endpoints.DeleteProjectGroupMember, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteProjectGroupMember };
