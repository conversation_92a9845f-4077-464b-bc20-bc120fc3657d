import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateUtilityRequestBody,
  UpdateUtilityResponse,
  UpdateUtilityURLParameter,
} from './types';

function useUpdateUtility() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateUtilityResponse,
    {
      body: UpdateUtilityRequestBody;
      urlParameter: UpdateUtilityURLParameter;
    }
  >(Endpoints.UpdateUtility, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateUtility };
