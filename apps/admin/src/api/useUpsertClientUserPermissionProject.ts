import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpsertClientUserPermissionProjectRequestBody,
  UpsertClientUserPermissionProjectResponse,
  UpsertClientUserPermissionProjectURLParameter,
} from './types';

function useUpsertClientUserPermissionProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpsertClientUserPermissionProjectResponse,
    {
      body: UpsertClientUserPermissionProjectRequestBody;
      urlParameter: UpsertClientUserPermissionProjectURLParameter;
    }
  >(Endpoints.UpsertClientUserPermissionProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpsertClientUserPermissionProject };
