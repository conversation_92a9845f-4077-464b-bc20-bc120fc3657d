import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteLocationResponse, DeleteLocationURLParameter } from './types';

function useDeleteLocation() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteLocationResponse,
    {
      urlParameter: DeleteLocationURLParameter;
    }
  >(Endpoints.DeleteLocation, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteLocation };
