import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ListProjectGroupMembersURLParameter, ListProjectGroupMembersResponse } from './types';

/**
 * @deprecated
 */
function useListProjectGroupMembers() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListProjectGroupMembersResponse,
    {
      urlParameter: ListProjectGroupMembersURLParameter;
    }
  >(Endpoints.ListProjectGroupMembers, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListProjectGroupMembers };
