import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadUtilityResponse, ReadUtilityURLParameter } from './types';

function useReadUtility() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadUtilityResponse,
    {
      urlParameter: ReadUtilityURLParameter;
    }
  >(Endpoints.ReadUtility, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadUtility };
