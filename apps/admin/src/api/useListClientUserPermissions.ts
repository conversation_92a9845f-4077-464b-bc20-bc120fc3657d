import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import {
  ListClientUserPermissionsResponse,
  ListClientUserPermissionsURLParameter,
} from '@/api/types';

function useListClientUserPermissions() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListClientUserPermissionsResponse,
    {
      urlParameter: ListClientUserPermissionsURLParameter;
    }
  >(Endpoints.ListClientUserPermissions, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListClientUserPermissions };
