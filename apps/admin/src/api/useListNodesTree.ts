import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ListNodesTreeResponse, ListNodesTreeURLParameter } from './types';

function useListNodesTree() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListNodesTreeResponse,
    {
      urlParameter: ListNodesTreeURLParameter;
    }
  >(Endpoints.ListNodesTree, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListNodesTree };
