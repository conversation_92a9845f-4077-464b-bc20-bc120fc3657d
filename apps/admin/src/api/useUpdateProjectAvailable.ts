import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateProjectAvailableRequestBody,
  UpdateProjectAvailableResponse,
  UpdateProjectAvailableURLParameter,
} from './types';

function useUpdateProjectAvailable() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateProjectAvailableResponse,
    {
      body: UpdateProjectAvailableRequestBody;
      urlParameter: UpdateProjectAvailableURLParameter;
    }
  >(Endpoints.UpdateProjectAvailable, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateProjectAvailable };
