import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadClientUserResponse, ReadClientUserURLParameter } from './types';

function useReadClientUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadClientUserResponse,
    {
      urlParameter: ReadClientUserURLParameter;
    }
  >(Endpoints.ReadClientUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadClientUser };
