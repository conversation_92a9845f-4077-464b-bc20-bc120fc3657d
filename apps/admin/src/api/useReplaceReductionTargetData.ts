import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReplaceReductionTargetDataRequestBody, ReplaceReductionTargetDataResponse } from './types';

function useReplaceReductionTargetData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReplaceReductionTargetDataResponse,
    {
      body: ReplaceReductionTargetDataRequestBody;
    }
  >(Endpoints.ReplaceReductionTargetData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReplaceReductionTargetData };
