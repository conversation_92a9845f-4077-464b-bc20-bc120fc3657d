import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpsertClientUserPermissionProjectGroupRequestBody,
  UpsertClientUserPermissionProjectGroupResponse,
  UpsertClientUserPermissionProjectGroupURLParameter,
} from './types';

function useUpsertClientUserPermissionProjectGroup() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpsertClientUserPermissionProjectGroupResponse,
    {
      body: UpsertClientUserPermissionProjectGroupRequestBody;
      urlParameter: UpsertClientUserPermissionProjectGroupURLParameter;
    }
  >(Endpoints.UpsertClientUserPermissionProjectGroup, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpsertClientUserPermissionProjectGroup };
