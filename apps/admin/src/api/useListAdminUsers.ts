import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListAdminUsersQueryParameter, ListAdminUsersResponse } from '@/api/types';

function useListAdminUsers() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListAdminUsersResponse,
    {
      queryParameter?: ListAdminUsersQueryParameter;
    }
  >(Endpoints.ListAdminUsers, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListAdminUsers };
