import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReplaceStandardDataRequestBody, ReplaceStandardDataResponse } from './types';

function useReplaceStandardData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReplaceStandardDataResponse,
    {
      body: ReplaceStandardDataRequestBody;
    }
  >(Endpoints.ReplaceStandardData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReplaceStandardData };
