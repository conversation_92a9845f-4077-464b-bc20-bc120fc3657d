import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateLocationRequestBody, CreateLocationResponse } from '@/api/types';

function useCreateLocation() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateLocationResponse,
    { body: CreateLocationRequestBody }
  >(Endpoints.CreateLocation, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateLocation };
