import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateAdminUserRequestBody,
  UpdateAdminUserResponse,
  UpdateAdminUserURLParameter,
} from './types';

function useUpdateAdminUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateAdminUserResponse,
    {
      body: UpdateAdminUserRequestBody;
      urlParameter: UpdateAdminUserURLParameter;
    }
  >(Endpoints.UpdateAdminUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateAdminUser };
