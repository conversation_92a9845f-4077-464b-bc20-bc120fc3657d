import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListClientUsersQueryParameter, ListClientUsersResponse } from '@/api/types';

function useListClientUsers() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListClientUsersResponse,
    {
      queryParameter?: ListClientUsersQueryParameter;
    }
  >(Endpoints.ListClientUsers, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListClientUsers };
