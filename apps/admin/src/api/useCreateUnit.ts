import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateUnitRequestBody, CreateUnitResponse } from '@/api/types';

function useCreateUnit() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateUnitResponse,
    { body: CreateUnitRequestBody }
  >(Endpoints.CreateUnit, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateUnit };
