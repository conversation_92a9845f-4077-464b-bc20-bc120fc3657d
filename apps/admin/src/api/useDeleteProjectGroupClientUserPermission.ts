import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  DeleteProjectGroupClientUserPermissionResponse,
  DeleteProjectGroupClientUserPermissionURLParameter,
} from './types';

function useDeleteProjectGroupClientUserPermission() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteProjectGroupClientUserPermissionResponse,
    {
      urlParameter: DeleteProjectGroupClientUserPermissionURLParameter;
    }
  >(Endpoints.DeleteProjectGroupClientUserPermission, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteProjectGroupClientUserPermission };
