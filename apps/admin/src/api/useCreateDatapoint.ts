import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateDatapointRequestBody, CreateDatapointResponse } from '@/api/types';

function useCreateDatapoint() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateDatapointResponse,
    { body: CreateDatapointRequestBody }
  >(Endpoints.CreateDatapoint, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateDatapoint };
