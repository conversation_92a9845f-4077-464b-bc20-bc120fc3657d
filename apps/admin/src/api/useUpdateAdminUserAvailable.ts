import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateAdminUserAvailableRequestBody,
  UpdateAdminUserAvailableResponse,
  UpdateAdminUserAvailableURLParameter,
} from './types';

function useUpdateAdminUserAvailable() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateAdminUserAvailableResponse,
    {
      body: UpdateAdminUserAvailableRequestBody;
      urlParameter: UpdateAdminUserAvailableURLParameter;
    }
  >(Endpoints.UpdateAdminUserAvailable, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateAdminUserAvailable };
