import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadAdminUserResponse, ReadAdminUserURLParameter } from './types';

function useReadAdminUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadAdminUserResponse,
    {
      urlParameter: ReadAdminUserURLParameter;
    }
  >(Endpoints.ReadAdminUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadAdminUser };
