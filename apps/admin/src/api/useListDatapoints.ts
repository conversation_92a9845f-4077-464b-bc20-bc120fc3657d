import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListDatapointsQueryParameter, ListDatapointsResponse } from '@/api/types';

function useListDatapoints() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListDatapointsResponse,
    {
      queryParameter?: ListDatapointsQueryParameter;
    }
  >(Endpoints.ListDatapoints, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListDatapoints };
