import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { CreateAdminRefreshTokenResponseBody } from './types/CreateAdminRefreshToken';

function useCreateAdminRefreshToken() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateAdminRefreshTokenResponseBody,
    {
      config: { headers: { Authorization: string } };
    }
  >(Endpoints.CreateAdminRefreshToken, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateAdminRefreshToken };
