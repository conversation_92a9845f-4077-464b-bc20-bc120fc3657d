import { abstractFetcher, useEcoisSWRMutation, Endpoints } from 'lib/api';
import { UpdateNodeRequestBody, UpdateNodeResponse, UpdateNodeURLParameter } from './types';

function useUpdateNode() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateNodeResponse,
    {
      body: UpdateNodeRequestBody;
      urlParameter: UpdateNodeURLParameter;
    }
  >(Endpoints.UpdateNode, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateNode };
