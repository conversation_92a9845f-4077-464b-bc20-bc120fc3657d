import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { CreateClientUserRequestBody, CreateClientUserResponse } from '@/api/types';

function useCreateClientUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    CreateClientUserResponse,
    { body: CreateClientUserRequestBody }
  >(Endpoints.CreateClientUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useCreateClientUser };
