import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  DeleteClientUserPermissionProjectResponse,
  DeleteClientUserPermissionProjectURLParameter,
} from './types';

function useDeleteClientUserPermissionProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteClientUserPermissionProjectResponse,
    {
      urlParameter: DeleteClientUserPermissionProjectURLParameter;
    }
  >(Endpoints.DeleteClientUserPermissionProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteClientUserPermissionProject };
