import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { UpdateDatapointsAvailableRequestBody, UpdateDatapointsAvailableResponse } from './types';

function useUpdateDatapointsAvailable() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateDatapointsAvailableResponse,
    {
      body: UpdateDatapointsAvailableRequestBody;
    }
  >(Endpoints.UpdateDatapointsAvailable, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateDatapointsAvailable };
