import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListContractTermsQueryParameter, ListContractTermsResponse } from '@/api/types';

function useListContractTerms() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListContractTermsResponse,
    {
      queryParameter?: ListContractTermsQueryParameter;
    }
  >(Endpoints.ListContractTerms, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListContractTerms };
