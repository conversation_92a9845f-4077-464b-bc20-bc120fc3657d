import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadProjectResponse, ReadProjectURLParameter } from './types';

function useReadProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadProjectResponse,
    {
      urlParameter: ReadProjectURLParameter;
    }
  >(Endpoints.ReadProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadProject };
