import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReplaceUnitcostRequestBody, ReplaceUnitcostResponse } from './types';

function useReplaceUnitcosts() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReplaceUnitcostResponse,
    {
      body: ReplaceUnitcostRequestBody;
    }
  >(Endpoints.ReplaceUnitcost, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReplaceUnitcosts };
