import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadContractTermResponse, ReadContractTermURLParameter } from './types';

function useReadContractTerm() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadContractTermResponse,
    {
      urlParameter: ReadContractTermURLParameter;
    }
  >(Endpoints.ReadContractTerm, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadContractTerm };
