import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  ReplaceNodeDatapointRequestBody,
  ReplaceNodeDatapointResponse,
  ReplaceNodeDatapointURLParameter,
} from './types';

function useReplaceNodeDatapoints() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReplaceNodeDatapointResponse,
    {
      body: ReplaceNodeDatapointRequestBody;
      urlParameter: ReplaceNodeDatapointURLParameter;
    }
  >(Endpoints.ReplaceNodeDatapoint, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReplaceNodeDatapoints };
