import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { DeleteAdminUserResponse, DeleteAdminUserURLParameter } from './types';

function useDeleteAdminUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    DeleteAdminUserResponse,
    {
      urlParameter: DeleteAdminUserURLParameter;
    }
  >(Endpoints.DeleteAdminUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useDeleteAdminUser };
