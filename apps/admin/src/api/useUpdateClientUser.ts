import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  UpdateClientUserRequestBody,
  UpdateClientUserResponse,
  UpdateClientUserURLParameter,
} from './types';

function useUpdateClientUser() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateClientUserResponse,
    {
      body: UpdateClientUserRequestBody;
      urlParameter: UpdateClientUserURLParameter;
    }
  >(Endpoints.UpdateClientUser, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateClientUser };
