import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadNodeURLParameter, ReadNodeResponse } from './types';

function useReadNode() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadNodeResponse,
    {
      urlParameter: ReadNodeURLParameter;
    }
  >(Endpoints.ReadNode, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadNode };
