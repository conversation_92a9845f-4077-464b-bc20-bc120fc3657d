import { ClientUser, ProjectGroup } from '@/types';

type DeleteClientUserPermissionProjectGroupURLParameter = {
  clientUserId: ClientUser['id'];
  projectGroupId: ProjectGroup['id'];
};

type DeleteClientUserPermissionProjectGroupResponse = {
  clientId: ClientUser['id'];
  projectGroupId: ProjectGroup['id'];
  deleted: boolean;
};

export type {
  DeleteClientUserPermissionProjectGroupURLParameter,
  DeleteClientUserPermissionProjectGroupResponse,
};
