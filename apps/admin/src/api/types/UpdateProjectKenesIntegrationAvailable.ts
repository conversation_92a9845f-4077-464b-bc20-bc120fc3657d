import { Project } from '@/types';

type UpdateProjectKenesIntegrationAvailableURLParameter = { projectId: number };

type UpdateProjectKenesIntegrationAvailableRequestBody = Pick<
  Project,
  'isKenesIntegrationAvailable'
>;

type UpdateProjectKenesIntegrationAvailableResponse = { project: Project };

export type {
  UpdateProjectKenesIntegrationAvailableRequestBody,
  UpdateProjectKenesIntegrationAvailableResponse,
  UpdateProjectKenesIntegrationAvailableURLParameter,
};
