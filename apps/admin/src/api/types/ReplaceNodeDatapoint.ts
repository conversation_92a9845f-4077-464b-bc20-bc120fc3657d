import { Datapoint, NodeDatapoint } from '@/types';

type ReplaceNodeDatapointURLParameter = {
  nodeId: number;
};

type ReplaceNodeDatapointRequestBody = {
  datapoints: (Pick<NodeDatapoint, 'coefficient'> & Pick<Datapoint, 'id'>)[];
};

type ReplaceNodeDatapointResponse = {
  nodeDatapoints: NodeDatapoint[];
};

export type {
  ReplaceNodeDatapointURLParameter,
  ReplaceNodeDatapointRequestBody,
  ReplaceNodeDatapointResponse,
};
