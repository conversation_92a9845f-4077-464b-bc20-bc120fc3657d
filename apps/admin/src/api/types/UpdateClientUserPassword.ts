import { ClientUser } from '@/types';

type UpdateClientUserPasswordURLParameter = {
  clientUserId: number;
};

type UpdateClientUserPasswordRequestBody = {
  password: string;
};

type UpdateClientUserPasswordResponse = {
  clientUserId: ClientUser['id'];
  passwordUpdated: boolean;
};

export type {
  UpdateClientUserPasswordURLParameter,
  UpdateClientUserPasswordRequestBody,
  UpdateClientUserPasswordResponse,
};
