import { NullablePartial } from 'lib/helper';

import { ClientUser } from '@/types';

type UpdateClientUserURLParameter = {
  clientUserId: number;
};

type UpdateClientUserRequestBody = NullablePartial<
  Pick<ClientUser, 'name' | 'email' | 'username' | 'company' | 'department' | 'position'>
>;

type UpdateClientUserResponse = {
  clientUser: ClientUser;
};

export type { UpdateClientUserURLParameter, UpdateClientUserRequestBody, UpdateClientUserResponse };
