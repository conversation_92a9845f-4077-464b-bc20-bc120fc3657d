import { Project, ProjectGroup } from '@/types';

type UpsertProjectGroupMembersURLParameter = {
  projectGroupId: number;
};

type UpsertProjectGroupMembersRequestBody = {
  projectIds: Project['id'][];
};

type UpsertProjectGroupMembersResponse = {
  projectGroupMembers: {
    projectGroupId: ProjectGroup['id'];
    projectId: Project['id'];
    project: Pick<
      Project,
      | 'id'
      | 'name'
      | 'isAvailable'
      | 'isEnableSummaryScore'
      | 'isEnableSummaryDetail'
      | 'isEnableSummaryTotal'
    >;
  }[];
};

export type {
  UpsertProjectGroupMembersURLParameter,
  UpsertProjectGroupMembersRequestBody,
  UpsertProjectGroupMembersResponse,
};
