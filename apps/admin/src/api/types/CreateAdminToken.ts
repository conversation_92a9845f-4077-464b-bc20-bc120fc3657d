type CreateAdminTokenRequestBody = {
  grant_type?: string;
  username: string;
  password: string;
  scope?: string;
  client_id?: string;
  client_secret?: string;
};

type CreateAdminTokenResponseBody = {
  access_token: string;
  token_type: string;
  refresh_token: string;
};

type CreateAdminTokenUnprocessableEntity = {
  detail: {
    errorCode: string;
    errorMsg: string;
  };
};

export type {
  CreateAdminTokenRequestBody,
  CreateAdminTokenResponseBody,
  CreateAdminTokenUnprocessableEntity,
};
