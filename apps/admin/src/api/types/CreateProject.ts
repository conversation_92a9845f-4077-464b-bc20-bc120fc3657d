import { Project } from '@/types';

type CreateProjectRequestBody = Pick<Project, 'name' | 'postcode' | 'address'> &
  Partial<
    Pick<
      Project,
      | 'latitude'
      | 'longitude'
      | 'backlogIssueKey'
      | 'isAvailable'
      | 'isEnableSummaryScore'
      | 'isEnableSummaryDetail'
      | 'isEnableSummaryTotal'
      | 'isKenesIntegrationAvailable'
    >
  >;

type CreateProjectResponse = { project: Project };

export type { CreateProjectRequestBody, CreateProjectResponse };
