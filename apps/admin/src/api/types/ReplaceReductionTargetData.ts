import { ReductionTargetData } from '@/types';

type ReplaceReductionTargetDataRequestBody = Pick<
  ReductionTargetData,
  'utilityId' | 'contractTermId'
> &
  Partial<Pick<ReductionTargetData, 'values'>>;

type ReplaceReductionTargetDataResponse = {
  reductionTargetData: Pick<ReductionTargetData, 'contractTermId' | 'utilityId' | 'values'>[];
};

export type { ReplaceReductionTargetDataRequestBody, ReplaceReductionTargetDataResponse };
