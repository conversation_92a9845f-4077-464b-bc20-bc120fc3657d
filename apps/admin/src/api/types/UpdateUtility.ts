import { EnergyType, Utility } from '@/types';

type UpdateUtilityURLParameter = {
  utilityId: Utility['id'];
};

type UpdateUtilityRequestBody = Partial<
  Pick<Utility, 'projectId' | 'name' | 'order' | 'nodeId' | 'isEnableSummary'> & {
    energyTypeId: EnergyType['id'];
  }
>;

type UpdateUtilityResponse = {
  utility: Utility;
};

export type { UpdateUtilityRequestBody, UpdateUtilityResponse, UpdateUtilityURLParameter };
