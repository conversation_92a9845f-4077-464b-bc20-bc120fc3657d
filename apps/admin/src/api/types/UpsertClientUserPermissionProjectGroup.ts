import { ClientUser, ProjectGroup } from '@/types';

type UpsertClientUserPermissionProjectGroupURLParameter = {
  clientUserId: ClientUser['id'];
};

type UpsertClientUserPermissionProjectGroupRequestBody = {
  projectGroupIds: ProjectGroup['id'][];
};

type UpsertClientUserPermissionProjectGroupResponse = {
  clientUserPermissionProjectGroups: {
    clientId: ClientUser['id'];
    projectGroupId: ProjectGroup['id'];
    projectGroup: Pick<ProjectGroup, 'id' | 'name'>;
  }[];
};

export type {
  UpsertClientUserPermissionProjectGroupURLParameter,
  UpsertClientUserPermissionProjectGroupRequestBody,
  UpsertClientUserPermissionProjectGroupResponse,
};
