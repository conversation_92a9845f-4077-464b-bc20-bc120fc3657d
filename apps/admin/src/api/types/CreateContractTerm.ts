import { ContractTerm, Project } from '@/types';

type CreateContractTermRequestBody = {
  projectId: Project['id'];
  startAt: string; // 2021-01-01 not 2023-01-01T00:00:00.123456+09:00
  endAt: string; // 2021-12-31 not 2023-01-01T00:00:00.123456+09:00
};

type CreateContractTermResponse = {
  contractTerm: ContractTerm;
};

export type { CreateContractTermRequestBody, CreateContractTermResponse };
