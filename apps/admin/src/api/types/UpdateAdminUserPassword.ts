import { AdminUser } from '@/types';

type UpdateAdminUserPasswordURLParameter = {
  adminUserId: number;
};

type UpdateAdminUserPasswordRequestBody = {
  password: string;
};

type UpdateAdminUserPasswordResponse = {
  adminUserId: AdminUser['id'];
  passwordUpdated: boolean;
};

export type {
  UpdateAdminUserPasswordURLParameter,
  UpdateAdminUserPasswordRequestBody,
  UpdateAdminUserPasswordResponse,
};
