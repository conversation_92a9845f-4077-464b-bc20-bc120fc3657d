import { ProjectGroup, ProjectGroupClientUser } from '@/types';

type UpsertProjectGroupClientUserPermissionURLParameter = {
  projectGroupId: ProjectGroup['id'];
};

type UpsertProjectGroupClientUserPermissionRequestBody = {
  clientUserIds: ProjectGroupClientUser['id'][];
};

type UpsertProjectGroupClientUserPermissionResponse = {
  clientUsers: ProjectGroupClientUser[];
};

export type {
  UpsertProjectGroupClientUserPermissionURLParameter,
  UpsertProjectGroupClientUserPermissionRequestBody,
  UpsertProjectGroupClientUserPermissionResponse,
};
