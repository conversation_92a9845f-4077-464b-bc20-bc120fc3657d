import { Client<PERSON>ser, ClientUserProject, Project } from '@/types';

type UpsertClientUserPermissionProjectURLParameter = {
  clientUserId: number;
};

type UpsertClientUserPermissionProjectRequestBody = {
  projectIds: Project['id'][];
};

type UpsertClientUserPermissionProjectResponse = {
  clientUserPermissionProjects: {
    clientUserId: ClientUser['id'];
    projectId: Project['id'];
    project: ClientUserProject;
  }[];
};

export type {
  UpsertClientUserPermissionProjectURLParameter,
  UpsertClientUserPermissionProjectRequestBody,
  UpsertClientUserPermissionProjectResponse,
};
