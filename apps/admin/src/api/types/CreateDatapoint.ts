import { NullablePartial } from 'lib/helper/typeFunctions';
import { Datapoint } from '@/types';

type CreateDatapointRequestBody = Pick<
  Datapoint,
  'name' | 'unitId' | 'terminalId' | 'pulserate' | 'pulserateUnit' | 'interval'
> &
  Partial<Pick<Datapoint, 'calcMethod' | 'address' | 'isAvailable'>> &
  NullablePartial<Pick<Datapoint, 'deviceNo'>>;

type CreateDatapointResponse = { datapoint: Datapoint };

export type { CreateDatapointRequestBody, CreateDatapointResponse };
