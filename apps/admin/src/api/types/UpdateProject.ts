import { Project } from '@/types';

type UpdateProjectURLParameter = { projectId: number };

type UpdateProjectRequestBody = Partial<
  Pick<
    Project,
    | 'name'
    | 'postcode'
    | 'address'
    | 'latitude'
    | 'longitude'
    | 'backlogIssueKey'
    | 'isAvailable'
    | 'isEnableSummaryScore'
    | 'isEnableSummaryDetail'
    | 'isEnableSummaryTotal'
  >
>;

type UpdateProjectResponse = { project: Project };

export type { UpdateProjectRequestBody, UpdateProjectResponse, UpdateProjectURLParameter };
