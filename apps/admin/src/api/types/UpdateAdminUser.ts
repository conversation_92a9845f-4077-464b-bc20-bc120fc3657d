import { NullablePartial } from 'lib/helper';
import { AdminUser } from '@/types';

type UpdateAdminUserURLParameter = {
  adminUserId: number;
};

type UpdateAdminUserRequestBody = NullablePartial<
  Pick<AdminUser, 'name' | 'email' | 'username' | 'company' | 'department' | 'position' | 'role'>
>;

type UpdateAdminUserResponse = {
  adminUser: AdminUser;
};

export type { UpdateAdminUserURLParameter, UpdateAdminUserRequestBody, UpdateAdminUserResponse };
