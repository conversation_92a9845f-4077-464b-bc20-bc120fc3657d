import { NullablePartial } from 'lib/helper/typeFunctions';
import { Datapoint } from '@/types';

type UpdateDatapointURLParameter = {
  datapointId: number;
};

type UpdateDatapointRequestBody = Partial<
  Pick<
    Datapoint,
    | 'name'
    | 'address'
    | 'unitId'
    | 'terminalId'
    | 'pulserate'
    | 'pulserateUnit'
    | 'interval'
    | 'calcMethod'
    | 'isAvailable'
  >
> &
  NullablePartial<Pick<Datapoint, 'deviceNo'>>;

type UpdateDatapointResponse = {
  datapoint: Datapoint;
};

export type { UpdateDatapointRequestBody, UpdateDatapointResponse, UpdateDatapointURLParameter };
