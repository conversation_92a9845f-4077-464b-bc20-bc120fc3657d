import { ClientUser, ProjectGroup } from '@/types';

type DeleteProjectGroupClientUserPermissionURLParameter = {
  projectGroupId: ProjectGroup['id'];
  clientUserId: ClientUser['id'];
};

type DeleteProjectGroupClientUserPermissionResponse = {
  projectGroupId: ProjectGroup['id'];
  clientUserId: ClientUser['id'];
  deleted: boolean;
};

export type {
  DeleteProjectGroupClientUserPermissionURLParameter,
  DeleteProjectGroupClientUserPermissionResponse,
};
