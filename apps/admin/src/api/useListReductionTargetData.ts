import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import {
  ListReductionTargetDataQueryParameter,
  ListReductionTargetDataResponse,
} from '@/api/types';

function useListReductionTargetData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListReductionTargetDataResponse,
    {
      queryParameter?: ListReductionTargetDataQueryParameter;
    }
  >(Endpoints.ListReductionTargetData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListReductionTargetData };
