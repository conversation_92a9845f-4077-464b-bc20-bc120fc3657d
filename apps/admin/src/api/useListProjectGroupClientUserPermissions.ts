import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import {
  ListProjectGroupClientUserPermissionsResponse,
  ListProjectGroupClientUserPermissionsURLParameter,
} from './types';

function useListProjectGroupClientUserPermissions() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListProjectGroupClientUserPermissionsResponse,
    {
      urlParameter: ListProjectGroupClientUserPermissionsURLParameter;
    }
  >(Endpoints.ListProjectGroupClientUserPermissions, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListProjectGroupClientUserPermissions };
