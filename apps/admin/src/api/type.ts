import { Location } from '../components/Location/type';

type ListLocationRequest = {
  locationIds?: number[];
  offset?: number;
  limit?: number;
};

type ListLocationResponse = {
  locations: Location[];
  offset: number;
  limit: number;
};

type CreateLocationRequest = Pick<Location, 'name' | 'address'> &
  Partial<Pick<Location, 'backlogIssueKey'>>;

type CreateLocationResponse = Location;

export type {
  ListLocationRequest,
  ListLocationResponse,
  CreateLocationRequest,
  CreateLocationResponse,
};
