import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { UpdateUnitRequestBody, UpdateUnitResponse, UpdateUnitURLParameter } from './types';

function useUpdateUnit() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpdateUnitResponse,
    {
      body: UpdateUnitRequestBody;
      urlParameter: UpdateUnitURLParameter;
    }
  >(Endpoints.UpdateUnit, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpdateUnit };
