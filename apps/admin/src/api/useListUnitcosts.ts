import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListUnitcostsQueryParameter, ListUnitcostsResponse } from '@/api/types';

function useListUnitcosts() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListUnitcostsResponse,
    {
      queryParameter?: ListUnitcostsQueryParameter;
    }
  >(Endpoints.ListUnitcosts, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListUnitcosts };
