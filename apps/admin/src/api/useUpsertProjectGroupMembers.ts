import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import {
  UpsertProjectGroupMembersRequestBody,
  UpsertProjectGroupMembersResponse,
  UpsertProjectGroupMembersURLParameter,
} from '@/api/types';

function useUpsertProjectGroupMembers() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    UpsertProjectGroupMembersResponse,
    {
      body: UpsertProjectGroupMembersRequestBody;
      urlParameter: UpsertProjectGroupMembersURLParameter;
    }
  >(Endpoints.UpsertProjectGroupMembers, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useUpsertProjectGroupMembers };
