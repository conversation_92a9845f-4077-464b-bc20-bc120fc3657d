import { useState, useEffect, useMemo } from 'react';

const getViewportTopToTitleBottom = (titleElementId: string): number => {
  const titleElement = document.getElementById(titleElementId);
  if (titleElement) {
    const titleRect = titleElement.getBoundingClientRect();
    return titleRect.bottom;
  }
  return 0;
};

function useViewportTopToBottom(titleElementId: string): number {
  const [height, setHeight] = useState(0);

  useEffect(() => {
    setHeight(getViewportTopToTitleBottom(titleElementId));
  }, [titleElementId]);

  return height;
}

function useViewportTopToBottoms(titleElementIds: string[]): number[] {
  const initialHeight = useMemo(() => {
    return titleElementIds.map((id) => getViewportTopToTitleBottom(id));
  }, [titleElementIds]);

  const [heights, setHeights] = useState(initialHeight);

  useEffect(() => {
    const newHeights = titleElementIds.map((id) => getViewportTopToTitleBottom(id));
    setHeights(newHeights);
  }, []);

  return heights;
}
export { useViewportTopToBottom, useViewportTopToBottoms };
