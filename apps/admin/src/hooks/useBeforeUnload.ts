import { useEffect } from 'react';
import { unstable_useBlocker as useBlocker } from 'react-router-dom';

const handleBeforeUnloadEvent = (event: BeforeUnloadEvent): void => {
  event.returnValue = '変更が保存されていません。このページを離れてもよろしいですか？';
};

const useBeforeUnload = (): void => {
  const blocker = () => {
    const isNavigable = window.confirm(
      '変更が保存されていません。このページを離れてもよろしいですか？',
    );
    if (isNavigable) {
      return false;
    }
    return true;
  };

  useBlocker(blocker);

  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnloadEvent);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnloadEvent);
    };
  }, []);
};

export { useBeforeUnload };
