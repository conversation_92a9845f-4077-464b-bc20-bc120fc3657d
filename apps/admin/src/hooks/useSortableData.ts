// hooks/useSortableData.ts
import { useState, useMemo } from 'react';

export type Order = 'asc' | 'desc' | 'default';

function useSortableData<T>(data: T[], defaultKey: keyof T) {
  const [order, setOrder] = useState<Order>('desc');
  const [orderBy, setOrderBy] = useState<keyof T>(defaultKey);

  const sortedData = useMemo(() => {
    const sortedArray = data.toSorted((a, b) => {
      const aVal = a[orderBy];
      const bVal = b[orderBy];

      if (aVal === bVal) return 0;

      // 数値はそのまま比較
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return aVal - bVal;
      }

      // null, undefined, ''常に下に表示
      if (!aVal) return 1;
      if (!bVal) return -1;

      //数値じゃない時の比較
      return String(aVal).localeCompare(String(bVal), 'ja-JP');
    });
    if (order === 'desc') {
      sortedArray.reverse();
    }
    if (order === 'default') {
      return data;
    }
    return sortedArray;
  }, [data, order, orderBy]);

  const onRequestSort = (key: keyof T) => {
    const isDefault = orderBy == defaultKey;
    const isAsc = orderBy === key && order === 'asc';
    const isDesc = orderBy === key && order === 'desc';

    if (!isDefault && isAsc) {
      setOrder('desc');
      return;
    }
    if (!isDefault && isDesc) {
      setOrderBy(defaultKey);
      return;
    }

    setOrder('asc');
    setOrderBy(key);
  };

  return { sortedData, order, orderBy, onRequestSort };
}

export { useSortableData };
