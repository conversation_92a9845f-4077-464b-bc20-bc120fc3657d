import { useErrorAlert } from 'lib/ErrorAlert';
import { useState } from 'react';

function useDialogState<T>(defaultOpen = false) {
  const [openState, setOpen] = useState(defaultOpen);
  const [context, setContext] = useState<T | undefined>(undefined);

  const { clearErrors } = useErrorAlert();

  const open = (value?: T) => {
    clearErrors();
    setOpen(true);
    if (value) {
      setContext(value);
    }
  };

  const close = () => {
    setOpen(false);
    if (context) {
      setContext(undefined);
    }
    clearErrors();
  };

  return { open, close, openState, setValue: setOpen, context };
}

type HooksDialogProps<T = unknown> = Pick<
  ReturnType<typeof useDialogState>,
  'close' | 'openState'
> &
  Partial<ReturnType<typeof useDialogState>> &
  T;

export { useDialogState };
export type { HooksDialogProps };
