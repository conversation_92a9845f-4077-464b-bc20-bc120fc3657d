/* 
参考：
https://github.com/streamich/react-use/blob/master/src/useMeasure.ts
*/

import { useState, useLayoutEffect, useMemo } from 'react';

type UseDOMRectRect = Pick<
  DOMRectReadOnly,
  'x' | 'y' | 'top' | 'left' | 'right' | 'bottom' | 'height' | 'width'
>;
type UseDOMRectRef<E extends Element = Element> = (element: E) => void;
type UseDOMRectResult<E extends Element = Element> = [UseDOMRectRef<E>, UseDOMRectRect];

const defaultState: UseDOMRectRect = {
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
};

function useDOMRect<E extends Element = Element>(): UseDOMRectResult<E> {
  const [element, ref] = useState<E | null>(null);
  const [rect, setRect] = useState<UseDOMRectRect>(defaultState);

  const resizeObserver = useMemo(
    () =>
      new window.ResizeObserver((entries) => {
        const entry = entries[0];
        const domRect = entry.target.getBoundingClientRect();
        setRect(domRect);
      }),
    [],
  );

  useLayoutEffect(() => {
    if (!element) return;
    resizeObserver.observe(element);
    return () => {
      resizeObserver.disconnect();
    };
  }, [element, resizeObserver]);

  return [ref, rect];
}

export { useDOMRect };
