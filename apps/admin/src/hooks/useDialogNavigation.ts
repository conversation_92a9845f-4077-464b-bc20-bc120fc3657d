import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

type Props = {
  dialogTriggerUrl: string;
  handleAccess?: () => void;
};
function useDialogNavigation({ dialogTriggerUrl, handleAccess }: Props) {
  const navigate = useNavigate();
  const location = useLocation();

  const isAccessed = location.pathname.includes(dialogTriggerUrl);

  useEffect(() => {
    if (isAccessed) {
      handleAccess?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAccessed]);

  function cancelNavigate(url: string) {
    navigate(url, { replace: true });
  }

  function submitNavigate(url: string) {
    navigate(url, isAccessed ? { replace: true } : undefined);
  }

  return { cancelNavigate, submitNavigate, isAccessed };
}
export { useDialogNavigation };
