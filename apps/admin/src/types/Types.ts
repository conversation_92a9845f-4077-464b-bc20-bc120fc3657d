type Location = {
  id: number;
  name: string;
  address: string;
  backlogIssueKey: string;
  units: Unit[];
  projects: Project[];
};

type sourceType = 'keyenece' | string;

type Unit = {
  id: number;
  locationId: Location['id'];
  no: number;
  name: string;
  sourceType: sourceType;
  simId: string;
  datapoints: Datapoint[];
};

type PulserateUnit = 'm3' | 'L' | 'kWh' | '℃';

type Datapoint = {
  id: number;
  name: string;
  address: string;
  unitId: Unit['id'];
  terminalId: number;
  pulserate: number;
  pulserateUnit: PulserateUnit;
  interval: number;
  calcMethod: boolean;
  isAvailable: boolean;
  deviceNo: number;
};

type Data = {
  datapointId: Datapoint['id'];
  startAt: Date;
  endAt: Date;
  value: number;
  pulserate: number;
  pulserateUnit: PulserateUnit;
};

type InterpolatedData = {
  datapointId: Datapoint['id'];
  startAt: Date;
  endAt: Date;
  value: number;
};

type Project = {
  id: number;
  name: string;
  isAvailable: boolean;
  postcode: string;
  address: string;
  latitude: number;
  longitude: number;
  backlogIssueKey: string;
  isEnableSummaryScore: boolean;
  isEnableSummaryDetail: boolean;
  isEnableSummaryTotal: boolean;
  isKenesIntegrationAvailable: boolean;
  locations: Location[];
  projectGroups: Pick<ProjectGroup, 'id' | 'name'>[];
  utilities: Utility[];
};

type ProjectOverview = Pick<Project, 'id' | 'name' | 'isAvailable'>;

type ProjectLocation = {
  projectId: Project['id'];
  locationId: Location['id'];
  location: Location;
};

type ProjectGroup = {
  id: number;
  name: string;
  detail: string;
  deleteProtection: boolean;
  projects: ProjectOverview[];
};

type ProjectGroupClientUser = Pick<
  ClientUser,
  'id' | 'isAvailable' | 'username' | 'email' | 'name'
>;

type Color = 'red' | 'blue' | 'green' | 'yellow';

type NodeDatapoint = {
  nodeId: Node['id'];
  datapointId: Datapoint['id'];
  coefficient: number;
  datapoint: Datapoint;
};

type Node = {
  id: number;
  name: string;
  projectId: Project['id'];
  order: number;
  parentId: Node['id'] | null;
  depth: number;
  v1GraphNodeId: number;
  energyType: EnergyType;
  relativeOrder: number;
  relativeOrderPath: number[];
  ancestorIds: Node['id'][];
  datapoints: (Pick<Datapoint, 'id' | 'name' | 'isAvailable'> & { coefficient: number })[];
};

type TreeNode = Node & {
  children: TreeNode[];
};

type TreeNodeWithHash = Omit<TreeNode, 'children'> & {
  hash: string;
  children: TreeNodeWithHash[];
};

type ContractTerm = {
  id: number;
  projectId: Project['id'];
  startAt: string;
  endAt: string;
};

type Utility = {
  id: number;
  projectId: Project['id'];
  name: string;
  order: number;
  energyType: EnergyType;
  isEnableSummary: boolean;
  nodeId: Node['id'] | null;
};

type ReductionTargetData = {
  utilityId: Utility['id'];
  contractTermId: ContractTerm['id'];
  values: MonthValue[] | null;
  projectId: Project['id'];
};

type MonthValue = {
  month: number;
  value: number | null;
};

type StandardData = {
  utilityId: Utility['id'];
  contractTermId: ContractTerm['id'];
  values: MonthValue[] | null;
  projectId: Project['id'];
};

type Unitcost = {
  utilityId: Utility['id'];
  contractTermId: ContractTerm['id'];
  value?: number | null;
  projectId: Project['id'];
};

type UnitcostWithKey = Unitcost & {
  key: string;
};

type StandardDataWithKey = StandardData & {
  key: string;
};

type ReductionTargetDataWithKey = ReductionTargetData & {
  key: string;
};

const RoleEnum = {
  superadmin: 'superadmin',
  general: 'general',
  kenes: 'kenes',
  data: 'data',
} as const;

type Role = keyof typeof RoleEnum;

type AdminUser = {
  id: number;
  username: string;
  isAvailable: boolean;
  email: string;
  name: string;
  company: string;
  department: string;
  position: string;
  role: Role;
};

type ClientUser = {
  id: number;
  username: string;
  isAvailable: boolean;
  email: string;
  name: string;
  company: string;
  department: string;
  position: string;
  role: Role;
};

type ClientUserProject = Pick<Project, 'id' | 'name' | 'isAvailable'>;
type ClientUserProjectGroup = Pick<ProjectGroup, 'id' | 'name'>;

type Status = {
  status: string;
  date: Date;
};

type EnergyType = {
  id: number;
  name: string;
  unit: string;
  color: Color;
};

type EnergyTypes = {
  energyTypes: EnergyType[];
};

export type {
  Location,
  Unit,
  Datapoint,
  Data,
  InterpolatedData,
  Project,
  ProjectOverview,
  ProjectGroup,
  ProjectGroupClientUser,
  Node,
  TreeNodeWithHash,
  TreeNode,
  ContractTerm,
  Utility,
  ReductionTargetData,
  StandardData,
  UnitcostWithKey,
  StandardDataWithKey,
  ReductionTargetDataWithKey,
  MonthValue,
  Unitcost,
  AdminUser,
  ClientUser,
  ClientUserProject,
  ClientUserProjectGroup,
  Status,
  Color,
  NodeDatapoint,
  PulserateUnit,
  ProjectLocation,
  Role,
  EnergyType,
  EnergyTypes,
};
export { RoleEnum };
