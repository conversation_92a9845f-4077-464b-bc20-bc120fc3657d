import { Box, CircularProgress, Typography } from '@mui/material';
import { useProject } from '@/providers/ProjectProvider';
import { GraphSettingEditScreen } from '@/screens/GraphSettingEditScreen';

function ProjectGraphEditPage() {
  const { project } = useProject();

  const depthLimit = project?.isKenesIntegrationAvailable ? 3 : 10;

  if (!project) {
    return (
      <Box>
        <CircularProgress />
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return <GraphSettingEditScreen project={project} depthLimit={depthLimit} />;
}

export default ProjectGraphEditPage;
