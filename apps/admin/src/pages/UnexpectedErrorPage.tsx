import { ArrowBack, NorthWest } from '@mui/icons-material';
import {
  Box,
  Button,
  ButtonProps,
  CardContent,
  Link,
  LinkProps,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import { PropsWithChildren } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppShell } from '@/layout/AppShell';

function UnexpectedErrorPage() {
  const navigate = useNavigate();
  const goBack = () => {
    navigate(-1);
  };

  return (
    <AppShell>
      <Box p={2}>
        <Paper>
          <CardContent>
            <h1>予期せぬエラー</h1>
            <Typography mb={2}>すみません。予期せぬエラーが発生しました。</Typography>
            <Stack direction={'row'} flexGrow={0} spacing={1}>
              <LinkButton onClick={() => goBack()} startIcon={<ArrowBack />}>
                元のページに戻る
              </LinkButton>
              <LinkButton href='/' startIcon={<NorthWest />}>
                ダッシュボードへ
              </LinkButton>
            </Stack>
          </CardContent>
        </Paper>
      </Box>
    </AppShell>
  );
}

function LinkButton({ children, ...props }: PropsWithChildren<ButtonProps & LinkProps>) {
  return (
    <Button LinkComponent={Link} variant='contained' disableElevation {...props}>
      {children}
    </Button>
  );
}

export default UnexpectedErrorPage;
