import { useAdminUser } from '@/components/AdminUser/AdminUserContext';
import { AdminUserDetailScreen } from '@/screens/AdminUserDetailScreen';

function AdminUserDetailPage() {
  const { fetch, adminUser, isLoading, error } = useAdminUser();

  function handleOnEdit({ id }: { id: number }) {
    fetch(id);
  }

  return (
    <AdminUserDetailScreen
      adminUser={adminUser}
      isLoading={isLoading}
      error={error}
      onEdit={handleOnEdit}
    />
  );
}

export default AdminUserDetailPage;
