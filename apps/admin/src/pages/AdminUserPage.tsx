import { useCallback, useEffect } from 'react';
import { Outlet, useParams } from 'react-router-dom';
import { useReadAdminUser } from '@/api';
import { AdminUserProvider } from '@/components/AdminUser/AdminUserContext';
import { AdminUserMenu } from '@/components/AdminUser/AdminUserMenu';
import { PageHeader } from '@/components/PageHeader';
import { SideBarLayout } from '@/layout/SidebarLayout';

function AdminUserPage() {
  const { adminUserId } = useParams();

  const { data, isLoading, trigger, error } = useReadAdminUser();

  const fetchAdminUser = useCallback(
    (adminUserId: number) => {
      trigger({
        urlParameter: {
          adminUserId,
        },
      });
    },
    [trigger],
  );

  useEffect(() => {
    fetchAdminUser(Number(adminUserId));
  }, [fetchAdminUser, adminUserId]);

  return (
    <AdminUserProvider
      adminUser={data?.adminUser}
      isLoading={isLoading}
      error={error}
      fetchFn={fetchAdminUser}
    >
      {data && (
        <PageHeader
          enableCaptionMode
          contents={[
            {
              label: 'アドミンユーザー',
              href: '/adminusers',
            },
            {
              label: data?.adminUser.username,
              href: `/adminusers/${adminUserId}`,
            },
          ]}
        />
      )}
      <SideBarLayout>
        <SideBarLayout.Menu>
          <AdminUserMenu />
        </SideBarLayout.Menu>
        <SideBarLayout.Contents maxWidth={'sm'}>
          <Outlet />
        </SideBarLayout.Contents>
      </SideBarLayout>
    </AdminUserProvider>
  );
}

export default AdminUserPage;
