import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { LoginScreen } from '../screens/LoginScreen';

export const LoginPage = () => {
  const navigate = useNavigate();
  const { authState } = useAuth();

  useEffect(() => {
    if (authState === 'AUTHENTICATED') {
      navigate('/');
    }
  }, [authState, navigate]);

  return <LoginScreen />;
};
