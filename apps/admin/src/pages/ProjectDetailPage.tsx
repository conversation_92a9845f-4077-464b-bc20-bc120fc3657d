import { useProject } from '@/providers/ProjectProvider';
import { ProjectDetailScreen } from '@/screens/ProjectDetailScreen';

function ProjectDetailPage() {
  const { fetch, project, isLoading, error } = useProject();

  function handleSubmit({ id }: { id: number }) {
    fetch(id);
  }

  return (
    <ProjectDetailScreen
      project={project}
      isLoading={isLoading}
      onSubmit={handleSubmit}
      error={error}
    />
  );
}

export default ProjectDetailPage;
