import { useParams } from 'react-router-dom';
import { ProjectUtilityProvider } from '@/components/ProjectUtility';
import { ProjectUtilityScreen } from '@/screens/ProjectUtilityScreen';

function ProjectUtilityPage() {
  const { projectId } = useParams();

  return (
    <ProjectUtilityProvider projectId={Number(projectId)}>
      <ProjectUtilityScreen />
    </ProjectUtilityProvider>
  );
}
export default ProjectUtilityPage;
