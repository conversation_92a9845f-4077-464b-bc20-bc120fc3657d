import { useClientUser } from '@/components/ClientUser/ClientUserContext';
import { ClientUserDetailScreen } from '@/screens/ClientUserDetailScreen';
import { ClientUser } from '@/types';

function ClientUserDetailPage() {
  const { clientUser, isLoading, error, fetch } = useClientUser();

  function handleOnEdit({ id }: ClientUser) {
    fetch(id);
  }

  return (
    <ClientUserDetailScreen
      clientUser={clientUser}
      isLoading={isLoading}
      error={error}
      onEdit={handleOnEdit}
    />
  );
}

export default ClientUserDetailPage;
