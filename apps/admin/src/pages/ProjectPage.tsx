import { useCallback } from 'react';
import { Outlet, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ProjectMenu } from '../components/Project/ProjectMenu';
import { useReadProject } from '@/api';
import { PageHeader } from '@/components/PageHeader';
import { SideBarLayout } from '@/layout';
import { EnergyTypesProvider } from '@/providers/EnergyTypesProvider';
import { ProjectProvider } from '@/providers/ProjectProvider';
import { Project } from '@/types';

function ProjectPage() {
  const { projectId } = useParams();

  const { data, isLoading, trigger, error } = useReadProject();

  useEffectOnce(() => {
    fetchProject(Number(projectId));
  });

  const fetchProject = useCallback(
    (projectId: Project['id']) => {
      trigger({
        urlParameter: {
          projectId,
        },
      });
    },
    [trigger],
  );

  return (
    <>
      {data && (
        <>
          <PageHeader
            enableCaptionMode
            contents={[
              {
                label: '案件',
                href: '/projects',
              },
              {
                label: data?.project.name,
                href: `/projects/${projectId}`,
              },
            ]}
          />
        </>
      )}
      <SideBarLayout>
        <SideBarLayout.Menu>
          <ProjectMenu />
        </SideBarLayout.Menu>
        <SideBarLayout.Contents>
          <EnergyTypesProvider>
            <ProjectProvider
              project={data?.project}
              isLoading={isLoading}
              error={error}
              fetchFn={fetchProject}
            >
              <Outlet />
            </ProjectProvider>
          </EnergyTypesProvider>
        </SideBarLayout.Contents>
      </SideBarLayout>
    </>
  );
}

export default ProjectPage;
