import { useClientUser } from '@/components/ClientUser/ClientUserContext';
import { ClientUserSettingScreen } from '@/screens/ClientUserSettingScreen';
import { ClientUser } from '@/types';

function ClientUserSettingPage() {
  const { fetch, clientUser } = useClientUser();

  function handleOnUserAvailableChange(clientUser: ClientUser) {
    fetch(clientUser.id);
  }

  return (
    <ClientUserSettingScreen
      clientUser={clientUser}
      onUserAvailableChange={handleOnUserAvailableChange}
    />
  );
}

export default ClientUserSettingPage;
