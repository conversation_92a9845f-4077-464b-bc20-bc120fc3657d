import { <PERSON>, Card, CardContent, Grid, <PERSON>, Stack, Typography } from '@mui/material';
import { PageHeader } from '@/components/PageHeader';
import { useAuth } from '@/hooks';

function DashboardPage() {
  const { profile } = useAuth();

  return (
    <Box sx={{ mx: 'auto' }} width={'100%'}>
      <Box>
        <PageHeader contents={[{ label: 'ダッシュボード' }]} />
        <Typography mt={-1} pl={1}>
          こんにちわ、{profile?.name ?? profile?.username}さん
        </Typography>
      </Box>
      <Grid container spacing={2} mt={2} alignItems={'stretch'}>
        <DashboardCard
          title={'ロケーション'}
          linkItems={[
            { title: 'ロケーション・ユニット・データポイントを表示', href: '/location' },
            { title: 'ロケーションを追加', href: '/location/add' },
          ]}
        />
        <DashboardCard
          title={'案件'}
          linkItems={[
            { title: 'すべての案件を表示', href: '/projects' },
            { title: '案件を追加', href: '/projects/add' },
          ]}
        />
        <DashboardCard
          title={'案件グループ'}
          linkItems={[
            { title: 'すべての案件グループを表示', href: '/projectgroups' },
            { title: '案件グループを追加', href: '/projectgroups/add' },
          ]}
        />
        <DashboardCard
          title={'クライアントユーザー'}
          linkItems={[
            { title: 'すべてのクライアントユーザーを表示', href: '/clientusers' },
            { title: 'クライアントユーザーの追加', href: '/clientusers/add' },
          ]}
        />
        {profile?.role === 'superadmin' && (
          <DashboardCard
            title={'アドミンユーザー'}
            linkItems={[
              { title: 'すべてのアドミンユーザーを表示', href: '/adminusers' },
              { title: 'アドミンユーザーの追加', href: '/adminusers/add' },
            ]}
          />
        )}
      </Grid>
    </Box>
  );
}

function DashboardCard({
  title,
  linkItems,
}: {
  title: string;
  linkItems: { title: string; href: string }[];
}) {
  return (
    <Grid item xs={4}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant={'h6'} fontWeight={'bold'}>
            {title}
          </Typography>
          <Stack spacing={1} mt={2}>
            {linkItems.map(({ title, href }) => {
              return (
                <Box key={href}>
                  <Link
                    href={href}
                    underline='none'
                    sx={{
                      '&:hover': {
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {title}
                  </Link>
                </Box>
              );
            })}
          </Stack>
        </CardContent>
      </Card>
    </Grid>
  );
}

export default DashboardPage;
