import { Outlet, useParams } from 'react-router-dom';
import { ProjectGroupScreenHeader } from '@/components/ProjectGroup';
import { ProjectGroupProvider } from '@/components/ProjectGroup/Provider';
import { SideBarLayout } from '@/layout';

function ProjectGroupPage() {
  const { projectGroupId } = useParams<{ projectGroupId: string }>();

  const pageLinks = [
    {
      to: 'detail',
      title: '基本情報',
    },
    {
      to: 'project_members',
      title: '案件',
    },
    {
      to: 'clientuser_permissions',
      title: 'ユーザー',
    },
    {
      to: 'setting',
      title: '設定',
    },
  ];

  return (
    <ProjectGroupProvider initialProjectGroupId={Number(projectGroupId)}>
      <ProjectGroupScreenHeader />
      <SideBarLayout>
        <SideBarLayout.Menu>
          <SideBarLayout.MenuItems items={pageLinks} />
        </SideBarLayout.Menu>
        <SideBarLayout.Contents>
          <Outlet />
        </SideBarLayout.Contents>
      </SideBarLayout>
    </ProjectGroupProvider>
  );
}
export default ProjectGroupPage;
