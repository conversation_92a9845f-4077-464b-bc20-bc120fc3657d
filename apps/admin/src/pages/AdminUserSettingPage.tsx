import { useAdminUser } from '@/components/AdminUser/AdminUserContext';
import { AdminUserSettingScreen } from '@/screens/AdminUserSettingScreen';

function AdminUserSettingPage() {
  const { fetch, adminUser } = useAdminUser();

  function handleOnChange({ id }: { id: number }) {
    fetch(id);
  }

  return <AdminUserSettingScreen adminUser={adminUser} onChangeAvailable={handleOnChange} />;
}

export default AdminUserSettingPage;
