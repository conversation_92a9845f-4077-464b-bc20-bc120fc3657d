import { useParams } from 'react-router-dom';
import { ClientUserPermissionScreen } from '@/screens/ClientUserPermissionScreen';
import { ClientUser } from '@/types';

function ClientUserPermissionPage() {
  const { clientUserId: urlClientUserId } = useParams();
  const clientUserId: ClientUser['id'] | undefined = urlClientUserId
    ? parseInt(urlClientUserId, 10)
    : undefined;

  return <ClientUserPermissionScreen clientUserId={clientUserId} />;
}

export default ClientUserPermissionPage;
