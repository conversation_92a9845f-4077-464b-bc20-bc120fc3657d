import { useCallback, useEffect } from 'react';
import { Outlet, useParams } from 'react-router-dom';
import { useReadClientUser } from '@/api';
import { ClientUserProvider } from '@/components/ClientUser/ClientUserContext';
import { ClientUserMenu } from '@/components/ClientUser/ClientUserMenu';
import { PageHeader } from '@/components/PageHeader';
import { SideBarLayout } from '@/layout/SidebarLayout';

function ClientUserPage() {
  const { clientUserId } = useParams();

  const { data, isLoading, trigger, error } = useReadClientUser();

  const fetchClientUser = useCallback(
    (clientUserId: number) => {
      trigger({
        urlParameter: {
          clientUserId,
        },
      });
    },
    [trigger],
  );

  useEffect(() => {
    fetchClientUser(Number(clientUserId));
  }, [fetchClientUser, clientUserId]);

  return (
    <ClientUserProvider
      clientUser={data?.clientUser}
      isLoading={isLoading}
      error={error}
      fetchFn={fetchClientUser}
    >
      {data && (
        <PageHeader
          enableCaptionMode
          contents={[
            {
              label: 'クライアントユーザー',
              href: '/clientusers',
            },
            {
              label: data?.clientUser.username,
              href: `/clientusers/${clientUserId}`,
            },
          ]}
        />
      )}
      <SideBarLayout>
        <SideBarLayout.Menu>
          <ClientUserMenu />
        </SideBarLayout.Menu>
        <SideBarLayout.Contents>
          <Outlet />
        </SideBarLayout.Contents>
      </SideBarLayout>
    </ClientUserProvider>
  );
}

export default ClientUserPage;
