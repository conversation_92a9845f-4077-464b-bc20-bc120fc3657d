import matchers from '@testing-library/jest-dom/matchers';
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { expect, afterEach } from 'vitest';
import { handlers } from '@/mocks/handlers';

// extends Vitest's expect method with methods from react-testing-library
expect.extend(matchers);

// Call MSW setup server with the given request handlers.
const server = setupServer(...handlers);

// runs a setup before all test cases (e.g. starting the server)
beforeAll(() => {
  server.listen();
});

// runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
  server.resetHandlers();
});
