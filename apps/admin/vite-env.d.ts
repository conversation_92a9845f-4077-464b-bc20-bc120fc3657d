import '@mui/material/styles';
import '@mui/material/chip';

/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

/// <reference types="@emotion/react/types/css-prop" />

declare module '@mui/material/styles' {
  interface Palette {
    attention: PaletteColor;
  }
  interface PaletteOptions {
    attention?: PaletteColorOptions;
  }

  interface Theme {
    status: {
      attention: React.CSSProperties['color'];
    };
  }
}

declare module '@mui/material/chip' {
  interface ChipPropsColorOverrides {
    attention: true;
  }
}
