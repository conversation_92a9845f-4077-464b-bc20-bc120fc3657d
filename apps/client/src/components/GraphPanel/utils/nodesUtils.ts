import { CashedNode, NodeWithProject, Project, Node } from '@/types';

/**
 *
 * - ノードをフラットにする。childrenに対し再帰処理を行う
 * - ノードにprojectIdを追加する
 *
 * */
function flattenAndAddProjectIdToNodes(nodes: Node[], projectId: Project['id']): NodeWithProject[] {
  const flatNodesList: NodeWithProject[] = [];
  nodes.forEach((node) => {
    const newNode = { ...node, projectId };
    flatNodesList.push(newNode as NodeWithProject);
    if (node.children !== undefined) {
      flatNodesList.push(...flattenAndAddProjectIdToNodes(node.children, projectId));
    }
  });
  return flatNodesList;
}

/**
 *
 * cashedNodesをフラットにする
 * ノードにprojectIdを追加する
 * */
function cashNodesToNodes(cashedNodes: CashedNode[]): NodeWithProject[] {
  const addProjectIdToNode = cashedNodes.flatMap((cashedNode) =>
    flattenAndAddProjectIdToNodes(
      cashedNode.nodes.map((node) => ({ ...node, projectId: cashedNode.projectId })),
      cashedNode.projectId,
    ),
  );

  return addProjectIdToNode;
}

export { flattenAndAddProjectIdToNodes, cashNodesToNodes };
