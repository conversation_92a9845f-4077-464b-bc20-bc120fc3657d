import { Box, CircularProgress, Paper, Stack, Typography } from '@mui/material';
import { blue, red } from '@mui/material/colors';
import * as Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useDOMRect } from 'lib/hooks/useDOMRect';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { HighchartsReactRef, Node, NodeGraphDataList, colorMap, DataInterval } from '@/types';
import { GraphType } from '@/types/Graph';

interface GraphOptions extends Highcharts.Options {
  chart: Highcharts.ChartOptions;
  plotOptions: Highcharts.PlotOptions;
  title: Highcharts.TitleOptions;
  series: Highcharts.SeriesOptionsType[];
}

// TODO: Make GraphSetting State Management to useReducer
//       and separate from component
Highcharts.setOptions({
  lang: {
    weekdays: ['日', '月', '火', '水', '木', '金', '土'],
  },
});

const baseGraphSetting: GraphOptions = {
  credits: {
    enabled: false,
  },
  title: {
    text: undefined,
  },
  chart: {
    type: 'line',
    zooming: {
      type: 'x',
    },
    width: null,
    height: null,
    // marginRight: 20,
  },
  plotOptions: {
    line: {
      dataLabels: {
        enabled: false,
      },
    },
  },
  tooltip: {
    shared: true,
  },
  exporting: {
    chartOptions: {},
    fallbackToExportServer: false,
    buttons: {
      contextButton: {
        enabled: false,
      },
    },
  },
  time: {
    useUTC: false,
  },
  xAxis: {
    crosshair: true,
    type: 'datetime',
    dateTimeLabelFormats: {
      day: '%m/%d',
      week: '%m/%d',
      month: '%Y/%m',
    },
    labels: {
      useHTML: true,
      formatter() {
        const day = this.chart.time.dateFormat('%m/%d', Number(this.value));
        const dayOfWeek = this.chart.time.dateFormat('%a', Number(this.value));
        const dayColor = {
          日: red[600],
          土: blue[800],
        }[dayOfWeek];
        return `${day}<span style="color: ${dayColor}; font-size:11px;">${dayOfWeek}</span>`;
      },
    },
    minRange: 1000 * 60 * 60 * 24, // maximum zoom allowed: 1 days
    units: [
      ['day', [1, 4]],
      ['month', [1]],
    ],
  },
  yAxis: {
    title: {
      text: undefined,
    },
  },
  series: [],
};

type getGraphOptionsProps = {
  nodeGraphDataList: NodeGraphDataList;
  pickedNodes: Node[];
  graphType: GraphType;
  size: {
    width: number;
    height: number;
  };
  includeLastWeekData: boolean;
  includeLastYearData: boolean;
  dataInterval: DataInterval;
};

/**
 *
 * Generate graph options for Highcharts from NodeData
 *
 */
function getGraphOptions({
  graphType,
  nodeGraphDataList,
  pickedNodes,
  size,
  includeLastWeekData,
  includeLastYearData,
}: getGraphOptionsProps): GraphOptions {
  const graphOption = { ...baseGraphSetting };

  // enable dataLabels only when node <= maxNodeCount
  const maxNodeCount = 30;
  const isDataLabelsEnabled = nodeGraphDataList.data.length <= maxNodeCount;

  // set graph type
  let highchartsGraphType: string;
  switch (graphType) {
    case GraphType.Line:
      highchartsGraphType = 'line';
      graphOption.chart.type = highchartsGraphType;
      graphOption.plotOptions = {
        series: {
          dataLabels: {
            enabled: isDataLabelsEnabled,
          },
          showInNavigator: true,
        },
      };
      break;
    case GraphType.Bar:
      highchartsGraphType = 'column';
      graphOption.chart.type = highchartsGraphType;
      graphOption.plotOptions = {
        column: {
          stacking: undefined,
          dataLabels: {
            enabled: isDataLabelsEnabled,
          },
        },
      };
      break;
    case GraphType.Column:
      highchartsGraphType = 'column';
      graphOption.chart.type = highchartsGraphType;
      graphOption.plotOptions = {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: isDataLabelsEnabled,
          },
        },
      };
      break;
    default:
      break;
  }

  // set graph data
  const measuredData = nodeGraphDataList.data.map((nodeData) => {
    // nodeInfo is pickedNodes
    const nodeInfo = pickedNodes.find((node) => node.id === nodeData.nodeId);

    return {
      type: highchartsGraphType,
      custom: { id: nodeData.nodeId },
      name: `${nodeInfo?.name}`,
      data: nodeData.measuredData.map((d) => [Date.parse(d.date), d.value]),
      color: nodeInfo ? colorMap[nodeInfo?.energyType.color] : colorMap.default,
    } as Highcharts.SeriesOptionsType;
  });

  let lastWeekData: Highcharts.SeriesOptionsType[] = [];
  if (includeLastWeekData) {
    lastWeekData = nodeGraphDataList.data.map((nodeData) => {
      const nodeInfo = pickedNodes.find((node) => node.id === nodeData.nodeId);

      // const nodeInfo = nodeMap.get(nodeData.nodeId);

      return {
        type: highchartsGraphType,
        custom: { id: nodeData.nodeId },
        name: `${nodeInfo?.name} (前週)`,
        data: nodeData.lastWeekData.map((d) => [Date.parse(d.date), d.value]),
        color: nodeInfo ? colorMap[nodeInfo?.energyType.color] : colorMap.default,
        dashStyle: 'Dot',
        lineWidth: 1.8,
      } as Highcharts.SeriesOptionsType;
    });
  }

  let lastYearData: Highcharts.SeriesOptionsType[] = [];
  if (includeLastYearData) {
    lastYearData = nodeGraphDataList.data.map((nodeData) => {
      // const nodeInfo = nodeMap.get(nodeData.nodeId);
      const nodeInfo = pickedNodes.find((node) => node.id === nodeData.nodeId);

      return {
        type: highchartsGraphType,
        custom: { id: nodeData.nodeId },
        name: `${nodeInfo?.name} (前年)`,
        data: nodeData.lastYearData.map((d) => [Date.parse(d.date), d.value]),
        color: nodeInfo ? colorMap[nodeInfo?.energyType.color] : colorMap.default,
        dashStyle: 'longDash',
      } as Highcharts.SeriesOptionsType;
    });
  }

  graphOption.series = [...measuredData, ...lastWeekData, ...lastYearData];

  // set graph height
  graphOption.chart.height = size.height;
  graphOption.chart.width = size.width;
  graphOption.xAxis = {
    ...graphOption.xAxis,
    minPadding: 0.05,
    maxPadding: 0.05,
  };

  return graphOption;
}
const DetailedGraph = () => {
  const {
    // nodes,
    clientNodeData,
    // nodeMap,
    graphType,
    // displayPeriod: { fromDate, toDate },
    dataInterval,
    isLoading: { isLoadingNodeData },
    optionalData: { includeLastWeekData, includeLastYearData },
    setChartComponentRef,
    columnHeight,
    nodesTree,
    getPickedNodes,
    pickedNodeIds,
  } = useGraphPanelContext();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const pickedNodes = useMemo(() => getPickedNodes(), [pickedNodeIds]);
  const [graphOptions, setGraphOptions] = useState<GraphOptions>(baseGraphSetting);

  const highChartsRef = useRef<HighchartsReactRef | undefined>(undefined);
  setChartComponentRef(highChartsRef);

  const [containerRef, rect] = useDOMRect();

  useEffect(() => {
    if (clientNodeData === undefined) return;
    setGraphOptions(
      getGraphOptions({
        graphType,
        pickedNodes,
        nodeGraphDataList: clientNodeData,
        size: {
          width: rect.width,
          height: rect.height,
        },
        includeLastWeekData,
        includeLastYearData,
        dataInterval,
      }),
    );
    if (highChartsRef.current) {
      highChartsRef.current.chart?.update(graphOptions, true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    nodesTree,
    clientNodeData,
    includeLastWeekData,
    includeLastYearData,
    graphType,
    columnHeight,
    rect.width,
    rect.height,
  ]);

  return (
    <Box
      ref={containerRef}
      sx={{
        display: 'grid',
        placeContent: 'center',
        position: 'relative',
        width: '100%',
        height: '100%',
      }}
    >
      {/* {isLoadingNodeData && ( */}
      <Box
        visibility={isLoadingNodeData ? 'visible' : 'hidden'}
        sx={{
          position: 'absolute',
          inset: 0,
          width: '100%',
          height: '100%',
          display: 'grid',
          placeContent: 'center',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.075)',
        }}
      >
        <Paper
          sx={{
            width: 'max-content',
            padding: 2,
          }}
          component={Stack}
          spacing={1}
        >
          <Box textAlign={'center'}>
            <CircularProgress color='primary' />
          </Box>
          <Typography variant='caption'>データ取得中</Typography>
        </Paper>
      </Box>
      {/* )} */}
      {pickedNodes.length === 0 ? (
        <Typography variant='h6'>「データ」の設定からノードを選択してください</Typography>
      ) : (
        <Box overflow={'hidden'}>
          <HighchartsReact highcharts={Highcharts} options={graphOptions} ref={highChartsRef} />
        </Box>
      )}
    </Box>
  );
};

export default DetailedGraph;
