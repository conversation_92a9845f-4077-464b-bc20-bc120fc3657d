import { FileDownloadOutlined } from '@mui/icons-material';
import { Button, Stack, Typography } from '@mui/material';
import Highcharts from 'highcharts';
import HighchartsExportData from 'highcharts/modules/export-data';
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsOfflineExporting from 'highcharts/modules/offline-exporting';
import { TabPanelProps } from '../types';
import { paneContainerLayoutStyle } from './Layout';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';

if (typeof Highcharts === 'object') {
  HighchartsExporting(Highcharts);
  HighchartsOfflineExporting(Highcharts);
  HighchartsExportData(Highcharts);
}

const DownloadPanel = (props: TabPanelProps) => {
  const { value, index, ...other } = props;
  const { getCurrentChart, downloadGraph } = useGraphPanelContext();

  const chart = getCurrentChart();
  const isChartAvailable = !!chart;

  const onClickImageDownload = () => {
    downloadGraph([
      {
        fileType: 'image',
      },
    ]);
  };
  const onClickDataDownload = () => {
    downloadGraph([
      {
        fileType: 'csv',
      },
    ]);
  };

  return (
    <Stack
      role='tabpanel'
      hidden={value !== index}
      id={`vertical-setting-${index}`}
      {...other}
      sx={{
        ...paneContainerLayoutStyle,
      }}
      spacing={2}
    >
      <Typography variant='h6'>グラフダウンロード</Typography>

      <Button
        variant='contained'
        fullWidth
        endIcon={<FileDownloadOutlined />}
        onClick={onClickImageDownload}
        disableElevation
        disabled={!isChartAvailable}
      >
        画像としてダウンロード
      </Button>

      <Button
        variant='contained'
        fullWidth
        endIcon={<FileDownloadOutlined />}
        disabled={!isChartAvailable}
        onClick={onClickDataDownload}
      >
        CSVとしてダウンロード
      </Button>
      {!isChartAvailable && (
        <Typography variant='body2' color='text.secondary'>
          グラフを出力するには「データ」からノードを選択してください
        </Typography>
      )}
    </Stack>
  );
};

export default DownloadPanel;
