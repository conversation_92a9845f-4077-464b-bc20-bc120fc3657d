import { Typography, Box, Stack } from '@mui/material';
import { TreeItem } from '@mui/x-tree-view';
import { EnergyTypeColorIcons } from 'lib/Component/Icons/EnergyTypeIcons';
import { MouseEvent } from 'react';
import { ControlButtonAdd, ControlButtonRemove } from './ControlButtons';
import { ColorConstants } from '@/constants/ColorConstants';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { Node } from '@/types';

const purpleColor = ColorConstants.PANE_PURPLE;

function TreeNode({ node }: { node: Node }) {
  const { pickedNodeIds, appendPickedNodeId, removePickedNodeId } = useGraphPanelContext();

  const isPicked = pickedNodeIds.includes(node.id);

  function handleAdd(event: MouseEvent<HTMLButtonElement>) {
    event.stopPropagation();
    appendPickedNodeId(node.id);
  }

  function handleRemove(event: React.MouseEvent<HTMLButtonElement>) {
    event.stopPropagation();
    removePickedNodeId(node.id);
  }

  return (
    <TreeItem
      sx={{
        // ここでTreeItemの背景色を変更する
        '&>.MuiTreeItem-content': {
          backgroundColor: isPicked ? purpleColor : 'white',
        },
        // hover状態の補正
        '&>.MuiTreeItem-content:hover': {
          backgroundColor: (theme) => theme.palette.action.hover,
        },
        // focused状態の補正
        '&>.MuiTreeItem-content.Mui-focused:not(&>.MuiTreeItem-content:hover)': {
          backgroundColor: isPicked ? purpleColor : 'white',
        },
      }}
      nodeId={node.id.toString()}
      label={
        <Stack
          position={'relative'}
          direction={'row'}
          justifyContent={'space-between'}
          alignItems={'center'}
          sx={{
            '&:hover .nodeController': {
              visibility: 'visible',
            },
          }}
        >
          <Stack direction={'row'} gap={0.5}>
            <Box display={'flex'} alignItems={'center'}>
              <EnergyTypeColorIcons energyTypeId={node?.energyType.id} />
            </Box>
            <Typography>{node.name}</Typography>
          </Stack>
          <Box>
            {isPicked ? (
              <ControlButtonRemove onClick={handleRemove} />
            ) : (
              <ControlButtonAdd className='nodeController' onClick={handleAdd} />
            )}
          </Box>
        </Stack>
      }
    >
      {Array.isArray(node.children)
        ? node.children.map((item) => <TreeNode key={item.id} node={item} />)
        : null}
    </TreeItem>
  );
}

export { TreeNode };
