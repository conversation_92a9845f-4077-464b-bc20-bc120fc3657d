import { NodeWithHash } from './type';
import { Node } from '@/types';

function addHashProperty(nodes: Node[]): NodeWithHash[] {
  return nodes.map((node) => addHashToNode(node));
}

function addHashToNode(node: Node): NodeWithHash {
  const newNode: NodeWithHash = {
    ...node,
    hash: generateHash(node),
    children: addHashProperty(node.children),
  };
  return newNode;
}

function generateHash(node: Node): string {
  return `${node.id}`;
}

export { addHashProperty };
