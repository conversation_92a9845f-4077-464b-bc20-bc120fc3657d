import { ColorConstants } from '@/constants/ColorConstants';
import { RemoveCircleOutline, AddCircleOutline } from '@mui/icons-material';
import { Button, ButtonProps } from '@mui/material';

function ControlButtonRemove({ ...buttonProps }: ButtonProps) {
  return (
    <Button
      variant='outlined'
      color='error'
      endIcon={<RemoveCircleOutline />}
      size='small'
      sx={{
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: ColorConstants.PANE_RED,
        },
        ...buttonProps.sx,
      }}
      {...buttonProps}
    >
      除く
    </Button>
  );
}
function ControlButtonAdd({ ...buttonProps }: ButtonProps) {
  return (
    <Button
      sx={{
        visibility: 'hidden',
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: ColorConstants.PANE_BLUE,
        },
        ...buttonProps.sx,
      }}
      variant='outlined'
      disableElevation
      endIcon={<AddCircleOutline />}
      size='small'
      {...buttonProps}
    >
      追加
    </Button>
  );
}

export { ControlButtonRemove, ControlButtonAdd };
