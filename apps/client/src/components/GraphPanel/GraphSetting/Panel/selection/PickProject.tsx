import { Box, Autocomplete, TextField } from '@mui/material';
import { useState } from 'react';
import { useEffectOnce } from 'react-use';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { useProjects } from '@/providers/ProjectsProvider';
import { Project } from '@/types';

type AutocompleteItem = {
  label: string;
  id: Project['id'];
};

export const PickProject = () => {
  const projects = useProjects();

  const { updateCurrentProjectId } = useGraphPanelContext();

  const [inputText, setInputText] = useState('');
  function updateInputText(_event: any, newValue: string | null) {
    setInputText(newValue ?? '');
  }

  const handleSelectChange = (_event: any, newValue: string | AutocompleteItem | null) => {
    if (newValue === null) {
      updateCurrentProjectId(null);
    }

    if (typeof newValue !== 'string' && newValue !== null) {
      // ノードの更新、プロジェクトIdの更新
      updateCurrentProjectId(newValue.id);
    }
  };

  useEffectOnce(() => {
    setInputText(projects[0].name);
    updateCurrentProjectId(projects[0].id);
  });

  return (
    <>
      <Box sx={{ p: 3 }}>
        <Autocomplete
          freeSolo
          id='project-select'
          options={projects.map((project) => {
            return { label: project.name, id: project.id };
          })}
          onChange={handleSelectChange}
          onInputChange={updateInputText}
          value={inputText}
          renderInput={(params) => <TextField {...params} label='案件を選択してください' />}
        />
      </Box>
    </>
  );
};
