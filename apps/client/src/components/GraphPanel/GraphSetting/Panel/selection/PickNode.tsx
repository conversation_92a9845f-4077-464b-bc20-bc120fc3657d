import { ChevronRight, ExpandMore } from '@mui/icons-material';
import { Box, CircularProgress } from '@mui/material';
import { TreeView } from '@mui/x-tree-view';
import { DataSelectionPaneTitle } from './DataSelectionPaneTitle';
import { TreeNode } from './TreeNode';
import { LayoutConstants } from '@/constants/LayoutConstants';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';

export const PickNode = () => {
  const {
    nodesTree,
    isLoading: { isLoadingProjectNodes },
  } = useGraphPanelContext();

  return (
    <>
      <Box sx={{ height: '100%', overflow: 'hidden' }}>
        <DataSelectionPaneTitle label='ノード' />
        {isLoadingProjectNodes === true && <CircularProgress />}
        {nodesTree.length > 0 && !isLoadingProjectNodes && (
          <TreeView
            disableSelection
            disabledItemsFocusable={true}
            defaultCollapseIcon={<ExpandMore />}
            defaultExpandIcon={<ChevronRight />}
            sx={{
              height: `calc(100% - ${LayoutConstants.DATA_SELECTION_PANE_TITLE_HEIGHT})`,
              overflowY: 'auto',
            }}
          >
            {nodesTree.map((node) => (
              <TreeNode key={node.id} node={node} />
            ))}
          </TreeView>
        )}
      </Box>
    </>
  );
};
