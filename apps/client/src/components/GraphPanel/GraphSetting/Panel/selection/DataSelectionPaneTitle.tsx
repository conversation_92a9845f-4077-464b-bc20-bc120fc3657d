import { LayoutConstants } from '@/constants/LayoutConstants';
import { Box, Divider, Typography } from '@mui/material';

const TITLE_HEIGHT = LayoutConstants.DATA_SELECTION_PANE_TITLE_HEIGHT;

function DataSelectionPaneTitle({ label }: { label: string }) {
  return (
    <Box height={TITLE_HEIGHT}>
      <Divider />
      <Box py={1} textAlign={'left'} pl={3}>
        <Typography variant='button' sx={{ textAlign: 'left' }}>
          {label}
        </Typography>
      </Box>
      <Divider />
    </Box>
  );
}

export { DataSelectionPaneTitle };
