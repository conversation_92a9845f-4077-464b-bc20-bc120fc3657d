import { Brightness1 } from '@mui/icons-material';
import { Box, Stack, Typography, Divider, Icon } from '@mui/material';
import { ControlButtonRemove } from './ControlButtons';
import { DataSelectionPaneTitle } from './DataSelectionPaneTitle';
import { LayoutConstants } from '@/constants/LayoutConstants';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { useProjects } from '@/providers/ProjectsProvider';
import { Color, Node, Project, colorMap } from '@/types';

type NodeDetailProps = { projectName: string } & Pick<Node, 'id' | 'name'> & { color: Color };

const NodeDetail = ({ id, projectName, name, color }: NodeDetailProps) => {
  const { removePickedNodeId } = useGraphPanelContext();

  // Temporary Disable https://github.com/PicoAda/ecois-web/issues/251#issuecomment-**********
  // const handleNodeClick = () => {
  // return null;
  // const series = chartComponentRef?.current?.chart.series.find(
  //   (s) => s?.options?.custom?.id === id,
  // );
  // if (series?.visible === true) {
  //   series.hide();
  //   setVisibility(false);
  // } else if (series?.visible === false) {
  //   series.show();
  //   setVisibility(true);
  // }
  // };

  function handleRemoveButtonClick() {
    removePickedNodeId(id);
  }

  return (
    <>
      <Box
        key={id}
        pt={1}
        pb={1}
        pl={2}
        sx={{
          '&:hover .buttonContainer': {
            visibility: 'visible',
          },
        }}
      >
        <Stack alignItems={'center'} direction={'row'}>
          <Stack alignItems={'center'}>
            <Icon
              sx={{
                height: '28px',
                width: '28px',
              }}
            >
              <Brightness1 sx={{ color: colorMap[color], width: '100%', height: '100%' }} />
            </Icon>
            {/* Temporary Disable https://github.com/PicoAda/ecois-web/issues/251#issuecomment-**********  */}
            {/* <IconButton onClick={handleNodeClick}> */}
            {/*   {visibility === true && <Brightness1 sx={{ color: colorMap[color] }} />} */}
            {/*   {visibility === false && <Brightness1 sx={{ color: colorMap.default }} />} */}
            {/* </IconButton> */}
          </Stack>
          <Box width={'64%'} pl={1}>
            <Stack direction='column' sx={{ textAlign: 'left' }}>
              <Box pl={1}>
                <Typography variant='caption'>{projectName}</Typography>
              </Box>
              <Box>
                <Typography textOverflow={'ellipsis'} variant='body2'>
                  {name}
                </Typography>
              </Box>
            </Stack>
          </Box>
          <Box
            visibility={'hidden'}
            flexGrow={1}
            textAlign={'right'}
            pr={1}
            className={'buttonContainer'}
          >
            <ControlButtonRemove onClick={handleRemoveButtonClick} />
          </Box>
        </Stack>
      </Box>
      <Divider />
    </>
  );
};

const TITLE_HEIGHT = LayoutConstants.DATA_SELECTION_PANE_TITLE_HEIGHT;

function NodeDetails() {
  const { getPickedNodes } = useGraphPanelContext();

  const projects = useProjects();
  const pickedNodes = getPickedNodes();

  function getProjectName(id: Project['id']) {
    const project = projects.find((project) => project.id === id);
    return project?.name ?? '';
  }

  return (
    <>
      {pickedNodes.length > 0 && (
        <Box maxHeight={'100%'} overflow={'hidden'}>
          <DataSelectionPaneTitle label={'選択中のノード'} />
          <Box sx={{ overflow: 'scroll', height: `calc(100% - ${TITLE_HEIGHT})` }}>
            {pickedNodes.map((node) => {
              return (
                <NodeDetail
                  key={node.id}
                  id={node.id}
                  projectName={getProjectName(node.projectId)}
                  name={node.name}
                  color={node.energyType.color}
                />
              );
            })}
          </Box>
        </Box>
      )}{' '}
    </>
  );
}

export { NodeDetails };
