import { Box } from '@mui/material';
import { TabPanelProps } from '../types';
import { PickProject, PickNode } from './selection';
import { NodeDetails } from './selection/NodeDetails';

const DataSelectPanel = ({ value, index, ...other }: TabPanelProps) => {
  return (
    <Box
      role='tabpanel'
      hidden={value !== index}
      id={`vertical-setting-${index}`}
      overflow={'hidden'}
      {...other}
      sx={{
        display: 'grid',
        height: '100%',
        maxHeight: '100%',
        overflow: 'hidden',
        gridTemplateColumns: '1fr',
        gridTemplateRows: '90px 6fr 4fr',
      }}
    >
      {value === index && (
        <>
          <PickProject />
          <PickNode />
          <NodeDetails />
        </>
      )}
    </Box>
  );
};
export default DataSelectPanel;
