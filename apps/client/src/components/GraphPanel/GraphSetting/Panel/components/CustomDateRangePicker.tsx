import { Event } from '@mui/icons-material';
import { GlobalStyles, useTheme } from '@mui/material';
import { deepPurple } from '@mui/material/colors';
import dayjs from 'dayjs';
import { DateRangePicker, CustomProvider } from 'rsuite';
import jaJP from 'rsuite/locales/ja_JP';
import type { RangeType } from 'rsuite/DateRangePicker';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';

function CustomDateRangePicker() {
  const {
    displayPeriod: { fromDate: fromDateDayjs, toDate: toDateDayjs },
    setFromDate,
    setToDate,
  } = useGraphPanelContext();
  const fromDate = fromDateDayjs.toDate();
  const toDate = toDateDayjs.toDate();

  const handleSetValues = (value: [Date, Date] | null) => {
    if (!value) return;

    const [newFromDate, newToDate] = value;
    setFromDate(dayjs(newFromDate).startOf('day'));
    setToDate(dayjs(newToDate).endOf('day'));
  };

  const today = dayjs().toDate();
  const yesterday = dayjs().subtract(1, 'day').toDate();
  const sevenDaysAgo = dayjs().subtract(7, 'day').toDate();
  const thirtyDaysAgo = dayjs().subtract(30, 'day').toDate();

  const predefinedRanges: RangeType[] = [
    {
      label: '今日',
      value: [today, today],
      placement: 'left',
    },
    {
      label: '昨日',
      value: [yesterday, yesterday],
      placement: 'left',
    },
    {
      label: '直近7日間',
      value: [sevenDaysAgo, today],
      placement: 'left',
    },
    {
      label: '直近30日間',
      value: [thirtyDaysAgo, today],
      placement: 'left',
    },
  ];

  function CustomIcon() {
    return <Event style={{ width: 20, height: 20 }} />;
  }

  const theme = useTheme();
  return (
    <CustomProvider locale={jaJP}>
      <GlobalStyles
        styles={{
          fontFamily: theme.typography.fontFamily,
          '.rs-theme,:root ': {
            '--rs-primary-50': deepPurple[50],
            '--rs-primary-100': deepPurple[100],
            '--rs-primary-200': deepPurple[200],
            '--rs-primary-300': deepPurple[300],
            '--rs-primary-400': deepPurple[400],
            '--rs-primary-500': deepPurple[500],
            '--rs-primary-600': deepPurple[600],
            '--rs-primary-700': deepPurple[700],
            '--rs-primary-800': deepPurple[800],
            '--rs-primary-900': deepPurple[900],
          },
          '.rs-picker-daterange-header.rs-picker-tab-active-end:after, .rs-picker-daterange-header.rs-picker-tab-active-start:after ':
            {
              borderBottom: `2px solid ${deepPurple[500]}`,
            },
        }}
      />{' '}
      <DateRangePicker
        caretAs={CustomIcon}
        size='lg'
        style={{
          width: '100%',
        }}
        onChange={handleSetValues}
        showOneCalendar
        ranges={predefinedRanges}
        defaultCalendarValue={[fromDate, toDate]}
        value={fromDate && toDate ? [fromDate, toDate] : null}
        cleanable={false}
      />
    </CustomProvider>
  );
}
export { CustomDateRangePicker };
