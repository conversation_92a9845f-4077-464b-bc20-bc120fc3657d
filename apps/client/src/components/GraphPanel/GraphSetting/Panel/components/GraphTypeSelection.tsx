import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { GraphType } from '@/types/Graph';
import {
  TimelineOutlined,
  SignalCellularAltOutlined,
  StackedBarChartOutlined,
} from '@mui/icons-material';
import { Typography, ToggleButtonGroup, ToggleButton, Stack } from '@mui/material';

export const GraphTypeSelection = () => {
  const { graphType, setGraphType } = useGraphPanelContext();

  const handleGraphTypeSelection = (
    _event: React.MouseEvent<HTMLElement>,
    newGraphType: GraphType,
  ) => {
    if (newGraphType === null) {
      return;
    }
    setGraphType(newGraphType);
  };

  return (
    <>
      <Typography variant='h6' component='div' textAlign='left' mb={1}>
        グラフタイプ
      </Typography>
      <ToggleButtonGroup value={graphType} onChange={handleGraphTypeSelection} exclusive fullWidth>
        <ToggleButton value={GraphType.Line} key={GraphType.Line}>
          <Stack alignItems='center'>
            <TimelineOutlined />
            <Typography variant='button'>折れ線</Typography>
          </Stack>
        </ToggleButton>
        <ToggleButton value={GraphType.Bar} key={GraphType.Bar}>
          <Stack alignItems='center'>
            <SignalCellularAltOutlined />
            <Typography variant='button'>棒グラフ</Typography>
          </Stack>
        </ToggleButton>
        <ToggleButton value={GraphType.Column} key={GraphType.Column}>
          <Stack alignItems='center'>
            <StackedBarChartOutlined />
            <Typography variant='button'>積上げ棒</Typography>
          </Stack>
        </ToggleButton>
      </ToggleButtonGroup>
    </>
  );
};
