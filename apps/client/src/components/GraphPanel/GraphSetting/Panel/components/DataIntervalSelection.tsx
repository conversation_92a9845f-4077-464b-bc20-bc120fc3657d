import { Typography, ToggleButtonGroup, ToggleButton } from '@mui/material';
import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { DataInterval } from '@/types';

type DataIntervalListItem = {
  value: DataInterval;
  label: string;
  datapointCountPerDay: number;
};

const dataIntervalList: DataIntervalListItem[] = [
  { value: '10min', label: '10分', datapointCountPerDay: 144 },
  { value: '30min', label: '30分', datapointCountPerDay: 48 },
  { value: '1hour', label: '1時間', datapointCountPerDay: 24 },
  { value: '1day', label: '1日', datapointCountPerDay: 1 },
  { value: '1month', label: '1ヶ月', datapointCountPerDay: 1 },
];

export const DataIntervalSelection = () => {
  const {
    pickedNodeIds,
    dataInterval,
    setDataInterval,
    DATAPOINT_LOADING_THRESHOLD,
    displayPeriod: { fromDate, toDate },
  } = useGraphPanelContext();

  const nodeCount = pickedNodeIds.length;
  const durationDay = toDate.diff(fromDate, 'day') + 1; //同じ日の場合は1

  const { datapointCountPerDay } = dataIntervalList.find(
    (value) => value.value === dataInterval,
  ) as DataIntervalListItem;

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const _isHeavyloading =
    nodeCount * durationDay * datapointCountPerDay > DATAPOINT_LOADING_THRESHOLD;
  const handleDataStepSelection = (
    _event: React.MouseEvent<HTMLElement>,
    newDataStep: DataInterval,
  ) => {
    if (newDataStep === null) {
      return;
    }
    setDataInterval(newDataStep);
  };

  return (
    <>
      <Typography variant='h6' component='div' textAlign='left' mb={1} mt={3}>
        データの表示粒度
      </Typography>
      <ToggleButtonGroup
        value={dataInterval}
        onChange={handleDataStepSelection}
        exclusive
        fullWidth
      >
        {dataIntervalList.map(({ label, value }) => {
          return (
            <ToggleButton value={value} key={value}>
              <Typography variant='button'>{label}</Typography>
            </ToggleButton>
          );
        })}
      </ToggleButtonGroup>
    </>
  );
};

export { dataIntervalList };
