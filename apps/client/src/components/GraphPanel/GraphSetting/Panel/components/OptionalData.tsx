import { useGraphPanelContext } from '@/providers/GraphPanelProvider';
import { Typography, FormControl, FormGroup, FormControlLabel, Checkbox } from '@mui/material';

export const OptionalData = () => {
  const {
    optionalData: { includeLastWeekData, includeLastYearData },
    setIncludeLastWeekData,
    setIncludeLastYearData,
  } = useGraphPanelContext();

  const handleLastWeekDataSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIncludeLastWeekData(event.target.checked);
  };
  const handleLastYearDataSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIncludeLastYearData(event.target.checked);
  };

  return (
    <>
      <Typography variant='h6' component='div' textAlign='left' mb={1} mt={3}>
        関連データの表示
      </Typography>
      <FormControl component='fieldset' variant='standard' fullWidth>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={includeLastWeekData}
                onChange={handleLastWeekDataSelection}
                name={'includeLastWeekData'}
              />
            }
            label='前週同曜日を表示'
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={includeLastYearData}
                onChange={handleLastYearDataSelection}
                name={'includeLastYearData'}
              />
            }
            label='前年同週同曜日を表示'
          />
        </FormGroup>
      </FormControl>
    </>
  );
};
