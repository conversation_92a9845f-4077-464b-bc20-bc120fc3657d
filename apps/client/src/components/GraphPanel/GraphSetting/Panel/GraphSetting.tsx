import { Box } from '@mui/material';
import { TabPanelProps } from '../types';
import {
  OptionalData,
  GraphTypeSelection,
  DataIntervalSelection,
  DisplayPeriodSelection,
} from './components';
import { paneContainerLayoutStyle } from './Layout';

const SettingPanel = (props: TabPanelProps) => {
  const { value, index, ...other } = props;

  return (
    <Box
      role='tabpanel'
      hidden={value !== index}
      id={`vertical-setting-${index}`}
      {...other}
      sx={{
        ...paneContainerLayoutStyle,
      }}
    >
      <GraphTypeSelection />
      <DisplayPeriodSelection />
      <DataIntervalSelection />
      <OptionalData />
    </Box>
  );
};

export default SettingPanel;
