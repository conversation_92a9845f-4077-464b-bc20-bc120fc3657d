import {
  TimelineOutlined,
  SettingsOutlined,
  FileDownloadOutlined,
  DeleteOutlined,
} from '@mui/icons-material';
import {
  Box,
  Tabs,
  Tab,
  Divider,
  Typography,
  Stack,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { useState } from 'react';
import { SettingPanel, DownloadPanel, DataSelectPanel } from './Panel';
import { LayoutConstants } from '@/constants/LayoutConstants';
import { useGraphContext } from '@/providers/GraphProvider';

interface GraphControlProps {
  graphId: number;
}

export const GraphControl = ({ graphId }: GraphControlProps) => {
  const [tabIndex, setTabIndex] = useState(0);
  const { removeGraph } = useGraphContext();

  const [dialogOpen, setDialogOpen] = useState(false);

  const handleTabChange = (_event: React.SyntheticEvent, newTabIndex: number) => {
    setTabIndex(newTabIndex);
  };

  const handleGraphDelete = () => {
    setDialogOpen(true);
  };

  function handleClose() {
    setDialogOpen(false);
  }

  function handleDelete() {
    removeGraph(graphId);
    setDialogOpen(false);
  }

  return (
    <>
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle color={'error'}>グラフ削除</DialogTitle>
        <DialogContent>
          <DialogContentText>
            選択したグラフを削除してもよろしいですか？この操作は取り消しできません。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button color='primary' onClick={handleClose}>
            キャンセル
          </Button>
          <Button color='error' onClick={handleDelete}>
            削除
          </Button>
        </DialogActions>
      </Dialog>
      <Box
        sx={{
          display: 'grid',
          gridTemplateRows: '1fr',
          gridTemplateColumns: `${LayoutConstants.GRAPH_CONTROLLER_MENU_WIDTH} ${LayoutConstants.GRAPH_CONTROLLER_PANE_WIDTH}`,
          height: `calc(100vh - ${LayoutConstants.GRAPH_TAB_BAR_HEIGHT} - ${LayoutConstants.GLOBAL_NAVIGATION_HEIGHT}})`,
        }}
      >
        <Stack>
          <Tabs
            orientation='vertical'
            variant='scrollable'
            value={tabIndex}
            onChange={handleTabChange}
            aria-label='Graph Setting Tab'
            sx={{
              borderRight: 1,
              borderColor: 'divider',
              overflow: 'visible',
              paddingBottom: 2,
              paddingTop: 2,
            }}
          >
            <Tab label='データ' id='setting-tab-0' icon={<TimelineOutlined />} />
            <Tab label='設定' id='setting-tab-1' icon={<SettingsOutlined />} />
            <Tab label='出力' id='setting-tab-2' icon={<FileDownloadOutlined />} />
          </Tabs>
          <Divider />
          <Stack flex={1} direction={'row'}>
            <Box flex={1} pt={2}>
              {graphId !== 1 && (
                <Button sx={{ width: '100%' }} color={'error'} onClick={handleGraphDelete}>
                  <Box>
                    <DeleteOutlined />
                    <Typography variant='button' display='block'>
                      削除
                    </Typography>
                  </Box>
                </Button>
              )}
              <Box flex={1}>{/* empty */}</Box>
            </Box>
            <Divider flexItem orientation='vertical' />
          </Stack>
        </Stack>
        {tabIndex === 0 && <DataSelectPanel value={tabIndex} index={0} />}
        {tabIndex === 1 && <SettingPanel value={tabIndex} index={1} />}
        {tabIndex === 2 && <DownloadPanel value={tabIndex} index={2} />}
      </Box>
    </>
  );
};
