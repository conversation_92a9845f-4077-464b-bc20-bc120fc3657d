import { Box, Divider } from '@mui/material';
import { memo, PropsWithChildren } from 'react';
import { DetailedGraph } from '@/components/GraphPanel/Graph';
import { GraphControl } from '@/components/GraphPanel/GraphSetting/GraphControl';
import { GraphPanelContextProvider } from '@/providers/GraphPanelProvider';
import { useGraphContext } from '@/providers/GraphProvider';

interface GraphPanelProps {
  children?: React.ReactNode;
  graphId: number;
}

const GraphPanel = (props: GraphPanelProps) => {
  const { graphId, ...other } = props;

  const {
    graphDisplayState: { graphIdToShow },
  } = useGraphContext();

  return (
    <Box
      role='tabpanel'
      hidden={graphId !== graphIdToShow}
      id={`graph-panel-${graphId}`}
      {...other}
      sx={{ flexGrow: 1 }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'row', height: '100%' }}>
        <GraphControl graphId={graphId} />
        <Divider flexItem orientation={'vertical'} />
        <DetailedGraph />
      </Box>
    </Box>
  );
};

function Child({ children }: PropsWithChildren) {
  return <>{children}</>;
}

interface PanelProps {
  graphId: number;
}

export const Panel = memo<PanelProps>((props: PanelProps) => {
  const { graphId } = props;

  return (
    <GraphPanelContextProvider>
      <Child>
        <GraphPanel graphId={graphId} />
      </Child>
    </GraphPanelContextProvider>
  );
});

Panel.displayName = 'GraphPanel';
