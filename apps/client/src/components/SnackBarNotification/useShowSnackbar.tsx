import { CloseOutlined } from '@mui/icons-material';
import { But<PERSON> } from '@mui/material';
import { useSnackbar } from 'notistack';

function useShowSnackbar() {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  return (
    message: string,
    variant: 'success' | 'error' | 'info' | 'warning' = 'success',
    preventDuplicate = false,
  ) => {
    enqueueSnackbar(message, {
      preventDuplicate,
      variant,
      action: (SnackbarKey) => (
        <Button
          onClick={() => closeSnackbar(SnackbarKey)}
          color='inherit'
          sx={{ minWidth: 0, padding: '5px', borderRadius: '50%', cursor: 'pointer' }}
        >
          <CloseOutlined fontSize='small' />
        </Button>
      ),
    });
  };
}

export { useShowSnackbar };
