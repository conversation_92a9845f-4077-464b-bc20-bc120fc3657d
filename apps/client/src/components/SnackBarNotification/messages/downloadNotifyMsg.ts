import { FileTypes } from '@/components/Chart/types';

// filetypesから型を生成
type DownloadNotifyMsgType = {
  [key in FileTypes]: {
    success: string;
    error: string;
  };
};

const downloadNotifyMsg: DownloadNotifyMsgType = {
  image: {
    success: 'グラフ画像のダウンロードに成功しました',
    error: 'グラフ画像のダウンロードに失敗しました。\n管理者にお問い合わせください。',
  },
  csv: {
    success: 'CSVデータのダウンロードに成功しました',
    error: 'CSVデータのダウンロードに失敗しました。\n管理者にお問い合わせください。',
  },
};

function selectDownloadNotifyMsg({
  fileType,
  isSuccess,
}: {
  fileType: FileTypes;
  isSuccess: boolean;
}) {
  // downloadNotifyMsg オブジェクトから直接メッセージを取得
  const msgType = downloadNotifyMsg[fileType];

  return isSuccess ? msgType.success : msgType.error;
}

export { selectDownloadNotifyMsg };
