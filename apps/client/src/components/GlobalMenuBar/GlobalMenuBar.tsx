import { KeyboardArrowDown } from '@mui/icons-material';
import { AppBar, Toolbar, Typography, Box, Button, Grid, styled, alpha } from '@mui/material';
import { deepPurple } from '@mui/material/colors';
import { Link, NavLink, NavLinkProps } from 'react-router-dom';
import { useDropdownMenu } from './hooks/useDropdownMenu';
import { useAuth } from '@/hooks';

type Pages = {
  label: string;
  to: string;
};

const EcoisNavLink = styled((props: NavLinkProps) => <NavLink {...props} />)(({ theme }) => ({
  fontSize: theme.typography.body2.fontSize,
  padding: theme.spacing(1, 1.5),
  borderRadius: theme.shape.borderRadius * 100,
  color: theme.palette.primary.main,
  textDecoration: 'none',
  backgroundColor: 'transparent',
  '&:hover': {
    color: theme.palette.primary.dark,
    backgroundColor: alpha(deepPurple[50], 0.4),
  },
  '&.active': {
    backgroundColor: deepPurple[50],
    color: theme.palette.primary,
    fontWeight: '600',
    opacity: 1,
  },
}));

function GlobalMenuBar({ pages }: { pages: Pages[] }) {
  const { profile } = useAuth();
  const { DropdownMenu, props, open } = useDropdownMenu();

  return (
    <AppBar
      position='fixed'
      sx={{
        backgroundColor: (theme) => theme.palette.grey[50],
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
      elevation={0}
    >
      <Toolbar>
        <Typography
          component={Link}
          variant='h6'
          to='/'
          mr={4}
          sx={{
            color: (theme) => theme.palette.primary.dark,
            textDecoration: 'none',
            '&:hover': {
              color: (theme) => theme.palette.primary.main,
            },
          }}
        >
          Ecois Client
        </Typography>
        <Box flexGrow={1}>
          <Grid container spacing={2}>
            {pages.map(({ label, to }) => (
              <Grid item key={label}>
                <EcoisNavLink to={to}>{label}</EcoisNavLink>
              </Grid>
            ))}
          </Grid>
        </Box>
        <Button
          sx={{
            textTransform: 'none',
          }}
          onClick={open}
          endIcon={<KeyboardArrowDown />}
        >
          {profile?.username}
        </Button>
        <DropdownMenu {...props} />
      </Toolbar>
    </AppBar>
  );
}

export { GlobalMenuBar };
