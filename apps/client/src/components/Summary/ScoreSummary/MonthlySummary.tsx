import { Box, CircularProgress } from '@mui/material';
import { Dayjs } from 'dayjs';
import { useEffect } from 'react';
import { SectionHeader } from '../Components/SectionHeader';
import { NodeScore } from './Nodes';
import { OverallScore } from './Overall';
import { useReadSummaryScore } from '@/api';
import { Project } from '@/types';

interface MonthlySummaryProps {
  projectId: Project['id'] | null;
  selectedMonth: Dayjs;
}
const MonthlySummary = (props: MonthlySummaryProps) => {
  const { projectId, selectedMonth } = props;

  const { data, trigger, isLoading } = useReadSummaryScore();

  useEffect(() => {
    if (projectId !== null && selectedMonth.isValid() === true) {
      trigger({
        queryParameter: { projectId, year: selectedMonth.year(), month: selectedMonth.month() + 1 },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, selectedMonth]);

  // const { addError } = useErrorAlert();
  // useEffect(() => {
  //   if (error) {
  //     addError({ message: 'サマリ速報値が取得できませんでした' + error.message });
  //   }
  // }, [error]);

  return (
    <>
      <SectionHeader title='月間削減金額' />
      {isLoading === true && (
        <Box mt={5} minHeight={200} textAlign={'center'}>
          <CircularProgress />
        </Box>
      )}
      {isLoading === false && data && (
        <>
          <OverallScore
            selectedMonth={selectedMonth}
            score={data.summaryScore.totalReductionCost}
          />
          <NodeScore selectedMonth={selectedMonth} utilityScores={data.summaryScore.utilities} />
        </>
      )}
      {isLoading === false && data === undefined && <Box minHeight={200}>データがありません</Box>}
    </>
  );
};

export { MonthlySummary };
