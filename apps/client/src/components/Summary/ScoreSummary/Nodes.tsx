import { Typography, Chip, Grid } from '@mui/material';
import { deepPurple } from '@mui/material/colors';
import dayjs, { Dayjs } from 'dayjs';
import { getFormattedScore } from './utils/getFormattedScore';
import { getUtilityScoreTypographyProps } from './utils/scoreTypography';
import { UtilityScore } from '@/types';

interface NodeScoreProps {
  utilityScores: UtilityScore[];
  selectedMonth: Dayjs;
}

const NodeScore = ({ utilityScores, selectedMonth }: NodeScoreProps) => {
  const isSameMonth = selectedMonth.isSame(dayjs(), 'month');

  return (
    <>
      <Grid container flexWrap='wrap' mb={1} mt={8} gap={1}>
        {utilityScores.map((utilityScore, index) => (
          <Grid
            xs={10}
            sm={4}
            md={2}
            item
            key={index}
            sx={{
              border: 1,
              borderRadius: 3,
              borderColor: deepPurple[50],
              minWidth: '240px',
              p: 2,
            }}
          >
            {/* ユーティリティ名 */}
            <Typography fontWeight='medium' ml={1}>
              {utilityScore.name}
            </Typography>

            {/* 月間削減金額 */}
            <Grid container alignItems='center' sx={{ mt: 1, ml: 1, minHeight: '32px' }}>
              <Grid item>
                <Chip
                  size='small'
                  label='月間'
                  sx={{
                    bgcolor: deepPurple[50],
                    color: deepPurple[400],
                    width: 40,
                    fontSize: 11,
                  }}
                />
              </Grid>
              <Grid item>
                <Typography
                  {...getUtilityScoreTypographyProps(utilityScore.reductionCost.monthly)}
                  ml={1}
                >
                  {getFormattedScore(utilityScore.reductionCost.monthly, selectedMonth, 'monthly')}
                </Typography>
              </Grid>
            </Grid>

            {/* 昨日削減金額 */}
            {isSameMonth && (
              <Grid container alignItems='center' sx={{ mt: 1, ml: 1, minHeight: '32px' }}>
                <Grid item>
                  <Chip
                    size='small'
                    label='昨日'
                    sx={{
                      bgcolor: deepPurple[50],
                      color: deepPurple[400],
                      width: 40,
                      fontSize: 11,
                    }}
                  />
                </Grid>
                <Grid>
                  <Typography
                    {...getUtilityScoreTypographyProps(utilityScore.reductionCost.latest)}
                    ml={1}
                  >
                    {getFormattedScore(utilityScore.reductionCost.latest, selectedMonth, 'latest')}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export { NodeScore };
