import WarningAmberRoundedIcon from '@mui/icons-material/WarningAmberRounded';
import { Typography, Stack, Grid } from '@mui/material';
import dayjs, { Dayjs } from 'dayjs';
import { getFormattedScore } from './utils/getFormattedScore';
import { getOverallScoreTypographyProps } from './utils/scoreTypography';
import { MonthlyScore } from '@/types';

interface OverallScoreProps {
  score: MonthlyScore;
  selectedMonth: Dayjs;
}

const OverallScore = ({ score, selectedMonth }: OverallScoreProps) => {
  const isSameMonth = selectedMonth.isSame(dayjs(), 'month');
  const monthlyScore = getFormattedScore(score.monthly, selectedMonth, 'monthly');
  const latestScore = getFormattedScore(score.latest, selectedMonth, 'latest');

  return (
    <Grid container direction='row' mt={3} mb={3} ml={3} gap={3} alignItems={'center'}>
      <Grid item md={4} minWidth={240}>
        <Typography fontWeight='medium'>月間合計削減金額</Typography>
        <Stack height={44} justifyContent='center'>
          <Typography {...getOverallScoreTypographyProps(score.monthly)}>{monthlyScore}</Typography>
        </Stack>
      </Grid>

      <Grid item xs={6} md={4} minWidth={260}>
        <Typography fontWeight='medium'>昨日合計削減金額</Typography>
        <Stack height={44} justifyContent='center'>
          <Stack direction='row' alignItems='center'>
            {!isSameMonth && (
              <WarningAmberRoundedIcon fontSize='small' color='warning' sx={{ opacity: 0.6 }} />
            )}
            <Typography {...getOverallScoreTypographyProps(score.latest)}>{latestScore}</Typography>
          </Stack>
        </Stack>
      </Grid>
    </Grid>
  );
};

export { OverallScore };
