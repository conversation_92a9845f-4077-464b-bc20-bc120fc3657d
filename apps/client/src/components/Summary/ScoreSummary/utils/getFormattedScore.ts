import dayjs, { Dayjs } from 'dayjs';
import { toCurrencyString } from './currency';

function getFormattedScore(
  score: number | null,
  selectedMonth: Dayjs,
  mode: 'monthly' | 'latest',
): string {
  const isSameMonth = selectedMonth.isSame(dayjs(), 'month');
  const messages = {
    dataNull: 'データがありません',
    sameMonth: '当月以外は表示されません',
  };

  if (mode === 'latest' && !isSameMonth) {
    return messages.sameMonth;
  }

  if (score === null) {
    return messages.dataNull;
  }

  return toCurrencyString(score);
}

export { getFormattedScore};
