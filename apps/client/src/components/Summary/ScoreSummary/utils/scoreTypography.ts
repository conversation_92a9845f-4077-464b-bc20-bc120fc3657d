import { TypographyProps } from '@mui/material';
import { grey } from '@mui/material/colors';

function getOverallScoreTypographyProps(
  score: number | null,
): Pick<TypographyProps, 'variant' | 'sx'> {
  const numberStyle: TypographyProps = {
    variant: 'h4',
    sx: { color: 'inherit' },
  };

  const stringStyle: TypographyProps = {
    variant: 'inherit',
    sx: { color: grey[500] },
  };
  return typeof score === 'number' ? numberStyle : stringStyle;
}

function getUtilityScoreTypographyProps(
  score: number | null,
): Pick<TypographyProps, 'variant' | 'sx'> {
  const numberStyle: TypographyProps = {
    variant: 'h6',
    sx: { color: 'inherit' },
  };

  const stringStyle: TypographyProps = {
    variant: 'inherit',
    sx: { color: grey[500] },
  };
  return typeof score === 'number' ? numberStyle : stringStyle;
}

export { getUtilityScoreTypographyProps, getOverallScoreTypographyProps };
