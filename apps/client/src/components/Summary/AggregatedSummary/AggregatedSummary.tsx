import { Box, CircularProgress, Typography } from '@mui/material';
import { grey, deepPurple } from '@mui/material/colors';
import { Dayjs } from 'dayjs';
import * as Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import { SectionHeader } from '../Components/SectionHeader';
import { useReadSummaryDetail } from '@/api';
import { ReadSummaryDetailResponse } from '@/api/types';
import { colorMap, Project } from '@/types';

const baseGraphOptions: Highcharts.Options = {
  credits: {
    enabled: false,
  },
  title: {
    text: undefined,
  },
  chart: {
    type: 'column',
    zooming: {
      type: 'x',
    },
  },
  xAxis: {
    type: 'datetime',
    dateTimeLabelFormats: {
      day: '%m/%d',
      week: '%m/%d',
      month: '%m/%d',
    },
    minRange: 1000 * 60 * 60 * 24 * 3, // maximum zoom allowed: 3 days
    units: [
      ['day', [1, 4]],
      ['month', [1]],
    ],
  },
  yAxis: {
    title: {
      text: '金額',
    },
  },
  plotOptions: {
    column: {
      stacking: 'normal',
    },
  },
  exporting: {
    buttons: {
      contextButton: {
        enabled: false,
      },
    },
  },
  time: {
    useUTC: false,
  },
  series: [],
};

function getGraphOptions(data: ReadSummaryDetailResponse): Highcharts.Options {
  const graphOptions = { ...baseGraphOptions };

  graphOptions.series = data.summaryDetail.utilities.map((utility) => ({
    type: 'column',
    name: utility.name,
    data: utility.reductionCost.daily.map(({ date, value }) => [Date.parse(date), value]),
    color: colorMap[utility.color],
  }));

  return graphOptions;
}

interface AggregatedSummaryProps {
  projectId: Project['id'] | null;
  selectedMonth: Dayjs;
}

const AggregatedSummary = (props: AggregatedSummaryProps) => {
  const { projectId, selectedMonth } = props;
  const [aggregatedGraphOptions, setGraphOptions] = useState<Highcharts.Options>(baseGraphOptions);
  const { data, isLoading, trigger } = useReadSummaryDetail();
  const isAllValueNull = data
    ? data?.summaryDetail.utilities.every((utility) =>
        utility.reductionCost.daily.every((daily) => daily.value === null),
      )
    : false;

  // const { addError } = useErrorAlert();
  // useEffect(() => {
  //   if (error) {
  //     addError({ message: 'サマリ集計値のデータが取得できませんでした' + error.message });
  //   }
  // }, [error]);

  useEffect(() => {
    if (data !== undefined) {
      setGraphOptions(getGraphOptions(data));
    }
  }, [data]);

  useEffect(() => {
    if (projectId !== null && selectedMonth.isValid() === true) {
      trigger({
        queryParameter: { projectId, year: selectedMonth.year(), month: selectedMonth.month() + 1 },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, selectedMonth]);

  return (
    <>
      <SectionHeader title='月間削減金額詳細' />
      {isLoading === true && (
        <Box mt={5} minHeight={200} textAlign={'center'}>
          <CircularProgress />
        </Box>
      )}
      {isLoading === false && !isAllValueNull && (
        <Box mt={5}>
          <HighchartsReact highcharts={Highcharts} options={aggregatedGraphOptions} />
        </Box>
      )}
      {isLoading === false && isAllValueNull && (
        <Box
          mb={5}
          display='flex'
          justifyContent='center'
          alignItems='center'
          minWidth='100%'
          minHeight={400}
          sx={{ border: 1, borderRadius: 3, borderColor: deepPurple[50] }}
        >
          <Typography sx={{ color: grey[500] }}>データがありません</Typography>
        </Box>
      )}
    </>
  );
};

export { AggregatedSummary };
