import {
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { useLogout } from '@/hooks/useLogout';

function ProjectUnlinkedErrorDialog() {
  function handleReload() {
    window.location.reload();
  }
  const handleLogout = useLogout();
  return (
    <Dialog open={true}>
      <DialogTitle>案件が紐付いていません</DialogTitle>
      <DialogContent>
        <Typography>管理者に連絡してください。紐付け完了後に更新してください。</Typography>
      </DialogContent>
      <DialogActions>
        <Button color='primary' variant='contained' onClick={handleReload}>
          更新
        </Button>
        <Button color='primary' variant='outlined' onClick={handleLogout}>
          ログアウト
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export { ProjectUnlinkedErrorDialog };
