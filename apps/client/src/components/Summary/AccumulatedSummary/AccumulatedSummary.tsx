import { Box, CircularProgress, Typography } from '@mui/material';
import { grey, deepPurple } from '@mui/material/colors';
import { Dayjs } from 'dayjs';
import * as Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import { SectionHeader } from '../Components/SectionHeader';
import { useReadSummaryTotal } from '@/api';
import { ReadSummaryTotalResponse } from '@/api/types';
import { Project, reductionCostColorMap } from '@/types';

interface AccumulatedSummaryBaseProps extends Highcharts.Options {
  series: Highcharts.SeriesOptionsType[];
}

const baseGraphOptions: AccumulatedSummaryBaseProps = {
  credits: {
    enabled: false,
  },
  title: {
    text: undefined,
  },
  chart: {
    type: 'line',
    zooming: {
      type: 'x',
    },
  },
  xAxis: {
    type: 'datetime',
    dateTimeLabelFormats: {
      day: '%m/%d',
      week: '%m/%d',
      month: '%m/%d',
    },
    minRange: 1000 * 60 * 60 * 24 * 3, // maximum zoom allowed: 3 days
    units: [
      ['day', [1, 4]],
      ['month', [1]],
    ],
  },
  yAxis: {
    title: {
      text: '金額',
    },
  },
  plotOptions: {},
  exporting: {
    buttons: {
      contextButton: {
        enabled: false,
      },
    },
  },
  time: {
    useUTC: false,
  },
  series: [],
};

function getGraphOptions(data: ReadSummaryTotalResponse): Highcharts.Options {
  const graphOptions = { ...baseGraphOptions };

  graphOptions.series[0] = {
    type: 'line',
    name: '累積削減金額',
    data: data.summaryTotal.totalReductionCost.daily.map(({ date, value }) => [
      Date.parse(date),
      value,
    ]),
    color: reductionCostColorMap.amount,
  };

  graphOptions.series[1] = {
    type: 'line',
    name: '削減目標金額',
    data: data.summaryTotal.totalReductionTargetCost.daily.map(({ date, value }) => [
      Date.parse(date),
      value,
    ]),
    color: reductionCostColorMap.target,
  };

  return graphOptions;
}

interface AccumulatedSummaryProps {
  projectId: Project['id'] | null;
  selectedMonth: Dayjs;
}

const AccumulatedSummary = (props: AccumulatedSummaryProps) => {
  const { projectId, selectedMonth } = props;
  const [accumulatedGraphOptions, setGraphOptions] = useState<Highcharts.Options>(baseGraphOptions);

  const { data, isLoading, trigger } = useReadSummaryTotal();
  const isAllValueNull = data
    ? data?.summaryTotal.totalReductionCost.daily.every((dairy) => dairy.value === null)
    : false;

  // const { addError } = useErrorAlert();
  // useEffect(() => {
  //   if (error) {
  //     addError({ message: 'サマリ集計値のデータが取得できませんでした' + error.message });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [error]);

  useEffect(() => {
    if (data !== undefined) {
      setGraphOptions(getGraphOptions(data));
    }
  }, [data]);

  useEffect(() => {
    if (projectId !== null && selectedMonth.isValid() === true) {
      trigger({
        queryParameter: { projectId, year: selectedMonth.year(), month: selectedMonth.month() + 1 },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, selectedMonth]);

  return (
    <>
      <SectionHeader title='月間累積削減金額' />
      {isLoading === true && (
        <Box mt={5} minHeight={200} textAlign={'center'}>
          <CircularProgress />
        </Box>
      )}
      {isLoading === false && !isAllValueNull && (
        <Box mt={5}>
          <HighchartsReact highcharts={Highcharts} options={accumulatedGraphOptions} />
        </Box>
      )}
      {isLoading === false && isAllValueNull && (
        <Box
          mt={5}
          display='flex'
          justifyContent='center'
          alignItems='center'
          minWidth='100%'
          minHeight={400}
          sx={{ border: 1, borderRadius: 3, borderColor: deepPurple[50] }}
        >
          <Typography sx={{ color: grey[500] }}>データがありません</Typography>
        </Box>
      )}
    </>
  );
};

export { AccumulatedSummary };
