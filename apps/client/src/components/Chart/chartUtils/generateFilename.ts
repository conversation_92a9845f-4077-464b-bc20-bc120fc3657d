import dayjs from 'dayjs';
import { FileTypes } from '../types';

function generateDownloadFilename({ fileType }: { fileType: FileTypes }) {
  const dateOnFilename = dayjs().format('YYYYMMDDHHmm');

  if (fileType === 'image') {
    return `グラフ画像_${dateOnFilename}`;
  }
  if (fileType === 'csv') {
    return `グラフデータ_${dateOnFilename}`;
  }
  return `ダウンロードデータ_${dateOnFilename}`;
}

export { generateDownloadFilename };
