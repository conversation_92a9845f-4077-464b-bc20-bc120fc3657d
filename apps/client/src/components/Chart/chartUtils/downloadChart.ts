import { Chart } from 'highcharts';
import { FileTypes } from '../types';
import { downloadImage, downloadCsv } from './downloadStrategies';

function downloadChart(chart: Chart, options: { fileType: FileTypes; filename: string }) {
  const { fileType, filename } = options;

  if (fileType === 'image') {
    downloadImage(chart, {
      filename,
    });
  }
  if (fileType === 'csv') {
    downloadCsv(chart, {
      filename,
    });
  }
}

export { downloadChart };
