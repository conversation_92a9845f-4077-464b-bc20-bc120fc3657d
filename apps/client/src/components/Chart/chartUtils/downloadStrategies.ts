import CsvDownloader from 'react-csv-downloader';

function downloadImage(chart: Highcharts.Chart, options: { filename: string }) {
  chart.exportChartLocal({
    type: 'image/png',
    filename: options.filename,
  });
}

function downloadCsv(chart: Highcharts.Chart, options: { filename: string }) {
  const csvData = generateCsvDataFromChart(chart);
  const csvDownloader = new CsvDownloader({
    filename: options.filename,
    extension: '.csv',
    datas: csvData,
  });
  csvDownloader.handleClick();
}

type CsvData = { [key: string]: string };

function generateCsvDataFromChart(chart: Highcharts.Chart): CsvData[] {
  const row_data = chart.getDataRows();

  const headers = row_data[0];
  const data = row_data.slice(1).map((row) => {
    const csv_row: { [key: string]: string } = {};
    headers.forEach((header, i) => {
      csv_row[header] = row[i]?.toString() ?? '';
    });
    return csv_row;
  });
  return data;
}

export { downloadImage, downloadCsv };
