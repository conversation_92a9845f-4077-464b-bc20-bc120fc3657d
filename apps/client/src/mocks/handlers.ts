import { faker } from '@faker-js/faker/locale/ja';
import { ManipulateType } from 'dayjs';
import { DefaultBodyType, PathParams, rest } from 'msw';
import {
  fakeNodeTree,
  fakeProject,
  fakeNodeGraphData,
  fakeNodeGraphDataList,
  fakeMonthlyScore,
  fakeSummaryUtilityScores,
  fakeSummaryDailyUtilityScores,
  fakeSummaryDailyReduction,
} from './faker';
import {
  ListClientNodeDataResponse,
  ListClientNodesTreeResponse,
  ListClientProjectsResponse,
  ReadClientNodeDataResponse,
  ReadClientProjectResponse,
  ReadSummaryScoreResponse,
  ReadSummaryDetailResponse,
  ReadSummaryTotalResponse,
  CreateClientTokenResponseBody,
  ReadClientUserProfileResponse,
} from '@/api/types';
import { DataInterval, Project } from '@/types';

const parseInterval: Record<DataInterval, [number, ManipulateType]> = {
  '10min': [10, 'minute'],
  '30min': [30, 'minute'],
  '1hour': [1, 'hour'],
  '1day': [1, 'day'],
  '1month': [1, 'month'],
};

export const handlers = [
  // Handles a POST /login request
  rest.post('/auth', (_req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.delay(1000),
      ctx.json({
        id: faker.datatype.uuid(),
        token: faker.datatype.string(10),
      }),
    );
  }),
  // Handles a GET /user request
  rest.get('/user', (_req, res, ctx) => {
    // If authenticated, return a mocked user details
    return res(
      ctx.status(200),
      ctx.json({
        id: faker.datatype.uuid(),
        firstName: faker.name.firstName(),
        lastName: faker.name.lastName(),
        email: faker.internet.email(),
      }),
    );
  }),
  /* 
  
  Auth / クライアント認証
  CreateClientToken
  
  */
  rest.post<DefaultBodyType, PathParams<string>, CreateClientTokenResponseBody>(
    '/client/auth/token',
    async (_, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          access_token: faker.string.sample(),
          token_type: 'bearer',
          refresh_token: faker.string.sample(),
        }),
      );
    },
  ),
  /* 
  

  Profile / クライアントユーザプロフィール
  ReadClientUserProfile
  
  

  */
  rest.get<DefaultBodyType, PathParams<string>, ReadClientUserProfileResponse>(
    '/client/users/me',
    async (_, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.delay(500),
        ctx.json({
          clientUser: {
            createdAt: '2023-05-15T03:04:12.774Z',
            updatedAt: '2023-05-15T03:04:12.774Z',
            id: 0,
            username: faker.name.lastName() + '' + faker.name.firstName(),
            isAvailable: true,
            email: '<EMAIL>',
            name: faker.name.firstName() + ' ' + faker.name.lastName(),
            company: 'company',
            department: 'department',
            position: 'position',
          },
        }),
      );
    },
  ),
  /* 
  
  ListClientProjects
  
  */
  rest.get<DefaultBodyType, PathParams, ListClientProjectsResponse>(
    '/client/projects',
    (_, res, ctx) => {
      const mock = Array(50)
        .fill(undefined)
        .map<Project>((_, index) => fakeProject(index + 1));
      return res(ctx.status(200), ctx.json({ projects: mock }), ctx.delay(500));
    },
  ),
  /* 
  
  ReadClientProject
  
  */
  rest.get<DefaultBodyType, PathParams, ReadClientProjectResponse>(
    '/client/projects/:projectId',
    (req, res, ctx) => {
      const project = fakeProject(Number(req.params.projectId));
      return res(ctx.status(200), ctx.json({ project: project }), ctx.delay(500));
    },
  ),
  /* 
  
  ListClientNodesTree
  
  */
  rest.get<DefaultBodyType, PathParams, ListClientNodesTreeResponse>(
    '/client/projects/:projectId/nodes',
    (_req, res, ctx) => {
      return res(ctx.status(200), ctx.json({ nodesTree: fakeNodeTree() }), ctx.delay(500));
    },
  ),
  /* 
  
  ListClientNodeData
  
  */
  rest.get<DefaultBodyType, PathParams, ListClientNodeDataResponse>(
    '/client/nodes/data',
    async (req, res, ctx) => {
      const nodeIds = req.url.searchParams.getAll('nodeIds').map(Number);

      const startAtInQuery = req.url.searchParams.get('startAt');
      const startAt = startAtInQuery !== null ? new Date(startAtInQuery) : new Date();

      const endAtInQuery = req.url.searchParams.get('endAt');
      const endAt = endAtInQuery !== null ? new Date(endAtInQuery) : new Date();

      const intervalInQuery = req.url.searchParams.get('interval') as DataInterval | null;
      const [interval, intervalUnit] =
        intervalInQuery !== null ? parseInterval[intervalInQuery] : parseInterval['1hour'];

      return res(
        ctx.status(200),
        ctx.json({
          nodeData: fakeNodeGraphDataList({ nodeIds, startAt, endAt, interval, intervalUnit }),
        }),
        ctx.delay(500),
      );
    },
  ),
  /* 
  
  ReadClientNodeData
  
  */
  rest.all<DefaultBodyType, PathParams, ReadClientNodeDataResponse>(
    '/client/nodes/:nodeId/data',
    async (req, res, ctx) => {
      const nodeId = Number(req.params.nodeId);

      const startAtInQuery = req.url.searchParams.get('startAt');
      const startAt = startAtInQuery !== null ? new Date(startAtInQuery) : new Date();

      const endAtInQuery = req.url.searchParams.get('endAt');
      const endAt = endAtInQuery !== null ? new Date(endAtInQuery) : new Date();

      const intervalInQuery = req.url.searchParams.get('interval') as DataInterval | null;
      const [interval, intervalUnit] =
        intervalInQuery !== null ? parseInterval[intervalInQuery] : parseInterval['1hour'];

      return res(
        ctx.status(200),
        ctx.json({
          nodeData: fakeNodeGraphData({ nodeId, startAt, endAt, interval, intervalUnit }),
        }),
        ctx.delay(500),
      );
    },
  ),
  /* 
  
  ReadSummaryScore
  
  */
  rest.get<DefaultBodyType, PathParams, ReadSummaryScoreResponse>(
    '/client/summaries/score/:projectId',
    async (req, res, ctx) => {
      const projectId = Number(req.params.projectId);

      const date = new Date();

      const yearInQuery = req.url.searchParams.get('year');
      const year = yearInQuery !== null ? Number(yearInQuery) : date.getFullYear();

      const monthInQuery = req.url.searchParams.get('month');
      const month = monthInQuery !== null ? Number(monthInQuery) : date.getMonth();

      return res(
        ctx.status(200),
        ctx.json({
          summaryScore: {
            projectId,
            year,
            month,
            // latestDate: null,
            // totalReductionCost: { monthly: null, latest: null },
            // totalReductionTargetCost: { monthly: null, latest: null },
            latestDate: date.toString(),
            totalReductionCost: fakeMonthlyScore(),
            totalReductionTargetCost: fakeMonthlyScore(),
            utilities: fakeSummaryUtilityScores(),
          },
        }),
        ctx.delay(500),
      );
    },
  ),
  /* 
  
  ReadSummaryDetail
  
  */
  rest.get<DefaultBodyType, PathParams, ReadSummaryDetailResponse>(
    '/client/summaries/detail/:projectId',
    (req, res, ctx) => {
      const projectId = Number(req.params.projectId);

      const date = new Date();

      const yearInQuery = req.url.searchParams.get('year');
      const year = yearInQuery !== null ? Number(yearInQuery) : date.getFullYear();

      const monthInQuery = req.url.searchParams.get('month');
      const month = monthInQuery !== null ? Number(monthInQuery) : date.getMonth();

      return res(
        ctx.status(200),
        ctx.json({
          summaryDetail: {
            projectId,
            year,
            month,
            utilities: fakeSummaryDailyUtilityScores(year, month),
          },
        }),
        ctx.delay(500),
      );
    },
  ),
  /* 
  
  ReadSummaryTotal
  
  */
  rest.get<DefaultBodyType, PathParams, ReadSummaryTotalResponse>(
    '/client/summaries/total/:projectId',
    (req, res, ctx) => {
      const projectId = Number(req.params.projectId);

      const date = new Date();

      const yearInQuery = req.url.searchParams.get('year');
      const year = yearInQuery !== null ? Number(yearInQuery) : date.getFullYear();

      const monthInQuery = req.url.searchParams.get('month');
      const month = monthInQuery !== null ? Number(monthInQuery) : date.getMonth();

      return res(
        ctx.status(200),
        ctx.json({
          summaryTotal: {
            projectId,
            year,
            month,
            totalReductionCost: {
              daily: fakeSummaryDailyReduction(year, month),
            },
            totalReductionTargetCost: {
              daily: fakeSummaryDailyReduction(year, month),
            },
          },
        }),
        ctx.delay(500),
      );
    },
  ),
];
