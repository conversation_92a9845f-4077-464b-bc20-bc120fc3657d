import { faker } from '@faker-js/faker/locale/ja';
import { Project } from '@/types';

function fakeProject(projectId: number): Project {
  return {
    id: projectId,
    name: `プロジェクト${projectId}`,
    isAvailable: faker.datatype.boolean(),
    isEnableSummaryScore: faker.datatype.boolean(),
    isEnableSummaryDetail: faker.datatype.boolean(),
    isEnableSummaryTotal: faker.datatype.boolean(),
  };
}

export { fakeProject };
