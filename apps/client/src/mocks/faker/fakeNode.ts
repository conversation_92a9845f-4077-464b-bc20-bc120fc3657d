// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-nocheck
import { faker } from '@faker-js/faker/locale/ja';
import { Node, Color } from '@/types';

function fakeNode(): Node {
  const colors: Color[] = ['red', 'blue', 'green', 'yellow'];
  return {
    id: faker.datatype.number(),
    name: faker.datatype.string(),
    color: colors[faker.datatype.number({ min: 0, max: colors.length - 1 })],
    order: faker.datatype.number(),
    children: [],
  };
}

function addChildren(node: Node, depth: number) {
  if (depth === 0) {
    return;
  }

  const children = Array(faker.datatype.number({ min: 1, max: 3 }))
    .fill(0)
    .map(() => {
      return {
        ...fakeNode(),
      };
    });
  node.children = children;
  children.forEach((child) => {
    addChildren(child, depth - 1);
  });
}

function fakeNodeTree(): Node[] {
  return Array(5)
    .fill(0)
    .map(() => {
      const node = fakeNode();
      addChildren(node, faker.datatype.number({ min: 1, max: 3 }));
      return node;
    });
}

export { fakeNode, fakeNodeTree };
