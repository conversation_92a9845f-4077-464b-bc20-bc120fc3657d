import { faker } from '@faker-js/faker/locale/ja';
import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import { NodeGraphDataList, NodeData, DataInterval, NodeGraphData } from '@/types';

interface fakeNodeDataInput {
  nodeId: number;
  startAt: Date;
  endAt: Date;
  interval: number;
  intervalUnit: ManipulateType;
}

function fakeNodeData(props: fakeNodeDataInput): NodeData {
  const { nodeId, startAt, endAt, interval, intervalUnit } = props;

  const data = [];
  let currentAt: Dayjs = dayjs(startAt);

  while (currentAt.toDate() <= endAt) {
    data.push({
      date: currentAt.format('YYYY-MM-DDTHH:mm:ss+09:00'),
      value: faker.number.float({ min: 0.0, max: 10.0, precision: 0.001 }),
    });
    currentAt = dayjs(currentAt).add(interval, intervalUnit);
  }

  return {
    nodeId: nodeId,
    measuredData: data,
    standardData: data.map((d) => {
      return {
        date: d.date,
        value: d.value + faker.number.float({ min: -10.0, max: 10.0, precision: 0.1 }),
      };
    }),
    lastWeekData: data.map((d) => {
      return {
        date: d.date,
        value: d.value + faker.number.float({ min: -10.0, max: 10.0, precision: 0.1 }),
      };
    }),
    lastYearData: data.map((d) => {
      return {
        date: d.date,
        value: d.value + faker.number.float({ min: -10.0, max: 10.0, precision: 0.1 }),
      };
    }),
  };
}

interface FakeNodeGraphDataInput {
  nodeId: number;
  startAt: Date;
  endAt: Date;
  interval: number;
  intervalUnit: ManipulateType;
}

function fakeNodeGraphData(props: FakeNodeGraphDataInput): NodeGraphData {
  const { nodeId, startAt, endAt, interval, intervalUnit } = props;

  return {
    startAt: startAt,
    endAt: endAt,
    interval: (interval.toString() + intervalUnit) as DataInterval,
    threshold: 0,
    includeStandardData: true,
    includeLastWeekData: true,
    includeLastYearData: true,
    data: fakeNodeData({ nodeId, startAt, endAt, interval, intervalUnit }),
  };
}

interface FakeNodeGraphDataListInput {
  nodeIds: number[];
  startAt: Date;
  endAt: Date;
  interval: number;
  intervalUnit: ManipulateType;
}

function fakeNodeGraphDataList(props: FakeNodeGraphDataListInput): NodeGraphDataList {
  const { nodeIds, startAt, endAt, interval, intervalUnit } = props;
  const data = nodeIds.map((nodeId) => {
    return fakeNodeData({ nodeId, startAt, endAt, interval, intervalUnit });
  });

  return {
    startAt: startAt,
    endAt: endAt,
    interval: (interval.toString() + intervalUnit) as DataInterval,
    threshold: 0,
    includeStandardData: true,
    includeLastWeekData: true,
    includeLastYearData: true,
    data: data,
  };
}

export { fakeNodeGraphData, fakeNodeGraphDataList };
