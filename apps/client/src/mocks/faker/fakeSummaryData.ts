import { faker } from '@faker-js/faker/locale/ja';
import dayjs, { Dayjs } from 'dayjs';
import {
  MonthlyScore,
  UtilityInfo,
  Color,
  DailyScore,
  UtilityScore,
  DailyUtilityScore,
} from '@/types';

function get_from_and_to_date(year: number, month: number): { fromDate: Dayjs; toDate: Dayjs } {
  const fromDate = dayjs(new Date(year, month - 1, 1));
  let toDate: Dayjs;
  if (month === 12) {
    toDate = dayjs(new Date(year + 1, 0, 1)).subtract(1, 'day');
  } else {
    toDate = dayjs(new Date(year, month, 1)).subtract(1, 'day');
  }
  return { fromDate, toDate };
}

function fakeMonthlyScore(): MonthlyScore {
  return {
    monthly: faker.number.int({ min: 0, max: 10000 }),
    latest: faker.number.int({ min: 0, max: 10000 }),
  };
}

function fakeDailyScore(fromDate: Dayjs, toDate: Dayjs): DailyScore[] {
  const dailyScore: DailyScore[] = [];
  let currentDate = fromDate;
  while (currentDate <= toDate) {
    dailyScore.push({
      date: currentDate.toDate().toString(),
      value: faker.number.int({ min: 0, max: 10000 }),
    });
    currentDate = currentDate.add(1, 'day');
  }

  return dailyScore;
}

function fakeUtility(utilityId: number): UtilityInfo {
  const colors: Color[] = ['red', 'blue', 'green', 'yellow'];
  const utilityCategory = ['電気', 'ガス', '水道'];

  return {
    utilityId: utilityId,
    nodeId: utilityId,
    name:
      utilityCategory[faker.number.int({ min: 0, max: utilityCategory.length - 1 })] +
      String(utilityId),
    order: faker.number.int(),
    color: colors[faker.number.int({ min: 0, max: colors.length - 1 })],
    unitcost: faker.number.int({ min: 0, max: 100 }),
  };
}

function fakeSummaryUtilityScores(): UtilityScore[] {
  return Array(4)
    .fill(0)
    .map((_, index) => {
      return {
        ...fakeUtility(index + 1),
        measuredAmount: fakeMonthlyScore(),
        standardAmount: fakeMonthlyScore(),
        reductionAmount: fakeMonthlyScore(),
        reductionCost: fakeMonthlyScore(),
        reductionTargetAmount: fakeMonthlyScore(),
        reductionTargetCost: fakeMonthlyScore(),
      };
    });
}

function fakeUtilityDailyScore(index: number, fromDate: Dayjs, toDate: Dayjs): DailyUtilityScore {
  const dailyScore: DailyScore[] = [];
  let currentDate = fromDate;
  while (currentDate <= toDate) {
    dailyScore.push({
      date: currentDate.toDate().toString(),
      value: faker.number.int({ min: 0, max: 10000 }),
    });
    currentDate = currentDate.add(1, 'day');
  }

  return {
    ...fakeUtility(index + 1),
    measuredAmount: { daily: fakeDailyScore(fromDate, toDate) },
    standardAmount: { daily: fakeDailyScore(fromDate, toDate) },
    reductionAmount: { daily: fakeDailyScore(fromDate, toDate) },
    reductionCost: { daily: fakeDailyScore(fromDate, toDate) },
    reductionTargetAmount: { daily: fakeDailyScore(fromDate, toDate) },
    reductionTargetCost: { daily: fakeDailyScore(fromDate, toDate) },
  };
}

function fakeSummaryDailyUtilityScores(year: number, month: number): DailyUtilityScore[] {
  const { fromDate, toDate } = get_from_and_to_date(year, month);

  return Array(4)
    .fill(0)
    .map((index) => fakeUtilityDailyScore(index, fromDate, toDate));
}

function fakeSummaryDailyReduction(year: number, month: number): DailyScore[] {
  const { fromDate, toDate } = get_from_and_to_date(year, month);

  return fakeDailyScore(fromDate, toDate);
}

export {
  fakeMonthlyScore,
  fakeSummaryUtilityScores,
  fakeSummaryDailyUtilityScores,
  fakeSummaryDailyReduction,
};
