import Calendar from '@mui/icons-material/Event';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import {
  Box,
  Autocomplete,
  TextField,
  Grid,
  IconButton,
  InputAdornment,
  Popover,
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { DateField } from '@mui/x-date-pickers/DateField';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateValidationError, DateView } from '@mui/x-date-pickers/models';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/ja';
import { useState, useEffect, useMemo, MouseEvent, SyntheticEvent } from 'react';
import { useEffectOnce } from 'react-use';
import { useListClientProjects } from '@/api';
import { AccumulatedSummary } from '@/components/Summary/AccumulatedSummary/AccumulatedSummary';
import { AggregatedSummary } from '@/components/Summary/AggregatedSummary/AggregatedSummary';
import { ProjectUnlinkedErrorDialog } from '@/components/Summary/Components/ProjectUnlinkedErrorDialog';
import { MonthlySummary } from '@/components/Summary/ScoreSummary';
import { Project } from '@/types';
export const SummaryScreen = () => {
  const [selectedMonth, setSelectedMonth] = useState<Dayjs>(dayjs().startOf('month'));
  const [selectedProjectId, setSelectedProjectId] = useState<Project['id'] | null>(null);
  const [candidateProjects, setCandidateProjects] = useState<Project[]>([]);
  const [candidateProjectMap, setCandidateProjectMap] = useState<Record<number, Project>>({});
  const [datePickError, setDatePickError] = useState<DateValidationError | null>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [currentView, setCurrentView] = useState<DateView>('year');
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);

  const { data, trigger, isLoading } = useListClientProjects();

  useEffectOnce(() => {
    trigger();
  });

  useEffect(() => {
    if (!data) return;

    try {
      setCandidateProjects(data.projects);
      setCandidateProjectMap(
        data.projects.reduce((acc, project) => ({ ...acc, [project.id]: project }), {}),
      );
      setSelectedProjectId(data.projects[0].id);
    } catch (error) {
      console.error(error);
    }
  }, [data]);

  const minDate = dayjs('2000-01-01');
  const maxDate = dayjs('2249-12-31');

  const handleProjectChange = (_event: SyntheticEvent, value: Partial<Project> | null) => {
    setSelectedProjectId(value?.id ?? null);
  };

  const handleMonthChange = (newMonth: Dayjs | null) => {
    if (newMonth === null || newMonth.isBefore(minDate) || newMonth.isAfter(maxDate)) {
      return;
    }
    if (currentView === 'month') {
      setSelectedMonth(newMonth.startOf('month'));
      if (selectedMonth) {
        handleCloseCalendar();
        setCurrentView('year');
      }
    }
  };

  const handleViewChange = (newView: DateView) => {
    setCurrentView(newView);
  };

  const datePickErrorMsg = useMemo(() => {
    switch (datePickError) {
      case 'minDate': {
        return '2000/1以降の日付を指定して下さい';
      }
      case 'maxDate': {
        return '2249/12以前の日付を指定して下さい';
      }
      case 'invalidDate': {
        return '日付の形式が正しくありません。';
      }
      default: {
        return '';
      }
    }
  }, [datePickError]);

  const handlePrevMonth = () => {
    setSelectedMonth((newMonth) => newMonth.subtract(1, 'month').startOf('month'));
  };

  const handleNextMonth = () => {
    setSelectedMonth((newMonth) => newMonth.add(1, 'month').startOf('month'));
  };

  const handleOpenCalendar = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    setPopoverOpen(true);
  };

  const handleCloseCalendar = () => {
    setAnchorEl(null);
    setPopoverOpen(false);
  };

  const isDataUnlinked = !data || data.projects.length === 0;

  return (
    <Box>
      {/* {isLoading && <CircularProgress />} */}
      {!isLoading && isDataUnlinked && <ProjectUnlinkedErrorDialog />}
      {
        <>
          <Box display='flex' justifyContent='space-between' alignItems='center'>
            <Autocomplete
              value={
                selectedProjectId && typeof selectedProjectId === 'number'
                  ? { label: candidateProjectMap[selectedProjectId].name, id: selectedProjectId }
                  : null
              }
              onChange={handleProjectChange}
              options={candidateProjects.map((project) => {
                return { label: project.name, id: project.id };
              })}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              renderInput={(params) => <TextField {...params} label='案件を選択してください' />}
              style={{ minWidth: '40%' }}
              renderOption={(props, option) => (
                <li {...props} key={option.id}>
                  {option.label}
                </li>
              )}
            />
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale='ja'>
              <DateField
                readOnly
                format='YYYY/MM'
                value={selectedMonth}
                onError={(newError) => setDatePickError(newError)}
                onChange={handleMonthChange}
                slotProps={{
                  textField: {
                    helperText: datePickErrorMsg,
                    InputProps: {
                      startAdornment: (
                        <InputAdornment position='start'>
                          <IconButton onClick={handlePrevMonth}>
                            <KeyboardArrowLeftIcon />
                          </IconButton>
                          <IconButton onClick={handleOpenCalendar} sx={{ ml: 1.2 }}>
                            <Calendar />
                          </IconButton>
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position='end'>
                          <IconButton onClick={handleNextMonth}>
                            <KeyboardArrowRightIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                    inputProps: {
                      onClick: (event) => {
                        handleOpenCalendar(event as MouseEvent<HTMLButtonElement>);
                      },
                    },
                    sx: {
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      '& .MuiInputBase-input': {
                        maxWidth: 76,
                        pointerEvents: 'auto',
                        cursor: 'pointer',
                      },
                    },
                  },
                }}
              />
              <Popover
                id='simple-popover'
                open={popoverOpen}
                anchorEl={anchorEl}
                onClose={handleCloseCalendar}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: -10,
                  horizontal: 0,
                }}
              >
                <DateCalendar
                  views={['year', 'month']}
                  minDate={minDate}
                  maxDate={maxDate}
                  onChange={handleMonthChange}
                  onViewChange={handleViewChange}
                />
              </Popover>
            </LocalizationProvider>
          </Box>

          <MonthlySummary projectId={selectedProjectId} selectedMonth={selectedMonth} />

          <Box mb={5} pb={5}>
            <Grid container spacing={1}>
              <Grid item xs={6}>
                <AggregatedSummary projectId={selectedProjectId} selectedMonth={selectedMonth} />
              </Grid>
              <Grid item xs={6}>
                <AccumulatedSummary projectId={selectedProjectId} selectedMonth={selectedMonth} />
              </Grid>
            </Grid>
          </Box>
        </>
      }
    </Box>
  );
};
