import { Box, Tab, Tabs } from '@mui/material';
import { PropsWithChildren } from 'react';

import { GraphContextProvider, useGraphContext } from '../providers/GraphProvider';
import { GraphPanel } from '@/components/GraphPanel';
import { LayoutConstants } from '@/constants/LayoutConstants';

/**
 *
 * Graph Screen Structure
 *
 * GraphList -- Arrays of Panel
 * └─ Panel -- The Graph Tab
 *    ├─ GraphControl -- The Graph Setting
 *   └─ DetailedGraph -- The Graph
 *
 *
 *
 */

function Child({ children }: PropsWithChildren) {
  return <>{children}</>;
}

const Graph = () => {
  const {
    graphDisplayState: { incrementalId, graphIdList, graphIdToShow },
    setGraphIdList,
    setGraphIdToShow,
    setIncrementalId,
  } = useGraphContext();

  const graphIndexToShow = graphIdList.findIndex((id) => id === graphIdToShow);

  const handleGraphChange = (_event: React.SyntheticEvent, newGraphIndex: number) => {
    const idToShow = graphIdList[newGraphIndex];
    setGraphIdToShow(idToShow);
  };

  const handleAddGraphClick = (_event: React.SyntheticEvent) => {
    const nextGraphId = incrementalId + 1;
    setGraphIdList([...graphIdList, nextGraphId]);
    setIncrementalId(nextGraphId);
    setGraphIdToShow(graphIdToShow);
  };

  const tabsHeight = LayoutConstants.GRAPH_TAB_BAR_HEIGHT;

  return (
    <Box
      sx={{
        height: '100%',
        display: 'grid',
        gridTemplateColumns: '1fr',
        gridTemplateRows: `${tabsHeight} calc(100% - ${tabsHeight})`,
      }}
    >
      <Tabs
        value={graphIndexToShow}
        onChange={handleGraphChange}
        sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
      >
        {graphIdList.map((graphId) => {
          return <Tab key={graphId} label={`グラフ${graphId}`} />;
        })}
        <Tab label='+ 新規グラフ' onClick={handleAddGraphClick} />
      </Tabs>
      {graphIdList.map((graphId) => {
        return <GraphPanel graphId={graphId} key={graphId} />;
      })}
    </Box>
  );
};

export const GraphScreen = () => {
  return (
    <GraphContextProvider>
      <Child>
        <Graph />
      </Child>
    </GraphContextProvider>
  );
};
