import { ErrorAlertProvider } from 'lib/ErrorAlert';
import { SnackbarProvider } from 'notistack';
import { RouterProvider } from 'react-router-dom';
import { router } from '../routes/Router';
import { AuthProvider } from './AuthProvider';
import { MuiThemeProvider } from './MuiThemeProvider';
import { SWRConfigProvider } from './SWRConfigProvider';

export const AppProviders = ({ children }: { children?: React.ReactNode }) => {
  return (
    <>
      <MuiThemeProvider>
        <ErrorAlertProvider>
          <SWRConfigProvider>
            <AuthProvider>
              <SnackbarProvider>
                <RouterProvider router={router} />
                {children}
              </SnackbarProvider>
            </AuthProvider>
          </SWRConfigProvider>
        </ErrorAlertProvider>
      </MuiThemeProvider>
    </>
  );
};
