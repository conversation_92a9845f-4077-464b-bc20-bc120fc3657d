import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useState } from 'react';

interface GraphDisplayState {
  incrementalId: number;
  graphIdList: number[];
  graphIdToShow: number;
}

export type GraphContext = {
  graphDisplayState: GraphDisplayState;

  setIncrementalId: Dispatch<SetStateAction<number>>;
  setGraphIdList: Dispatch<SetStateAction<number[]>>;
  setGraphIdToShow: Dispatch<SetStateAction<number>>;
  removeGraph: (graphId: number) => void;
};

const GraphContext = createContext<GraphContext | null>(null);

const GraphContextProvider = ({ children }: { children: ReactNode }) => {
  const [graphIdToShow, setGraphIdToShow] = useState<number>(1);
  const [graphIdList, setGraphIdList] = useState<number[]>([1]);
  const [incrementalId, setIncrementalId] = useState<number>(1);

  const removeGraph = (graphIdToRemove: number) => {
    if (graphIdList.length === 1) return;

    const graphIndexToRemove = graphIdList.findIndex((id) => id === graphIdToRemove);
    const newIGraphIdList = graphIdList.filter((id) => id !== graphIdToRemove);

    setGraphIdList(newIGraphIdList);
    // 表示するグラフを削除したグラフの左隣りのグラフにする
    setGraphIdToShow(newIGraphIdList[Math.max(0, graphIndexToRemove - 1)]);
  };

  const value: GraphContext = {
    graphDisplayState: {
      incrementalId,
      graphIdList,
      graphIdToShow,
    },
    setIncrementalId,
    setGraphIdList,
    setGraphIdToShow,
    removeGraph,
  };

  return <GraphContext.Provider value={value}>{children}</GraphContext.Provider>;
};

function useGraphContext(): GraphContext {
  const context = useContext(GraphContext);

  if (!context) {
    throw new Error('useGraphContext must be used within a GraphContextProvider');
  }
  return context;
}

export { GraphContextProvider, useGraphContext };
