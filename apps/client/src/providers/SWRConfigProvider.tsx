import { swrCommonOnErrorHand<PERSON>, useErrorAlertContext } from 'lib/ErrorAlert';
import { PropsWithChildren } from 'react';
import { SWRConfig } from 'swr';

function SWRConfigProvider({ children }: PropsWithChildren) {
  const { addError } = useErrorAlertContext();

  return (
    <SWRConfig
      value={{
        onError: async (err) => {
          swrCommonOnErrorHandler({
            err,
            addError,
          });
        },
        revalidateOnFocus: false,
      }}
    >
      {children}
    </SWRConfig>
  );
}

export { SWRConfigProvider };
