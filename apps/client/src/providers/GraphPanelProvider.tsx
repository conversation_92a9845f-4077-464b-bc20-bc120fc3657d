import dayjs, { Dayjs } from 'dayjs';
import {
  createContext,
  Dispatch,
  MutableRefObject,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useListClientNodeData, useListClientNodesTree } from '@/api';
import { generateDownloadFilename, downloadChart } from '@/components/Chart/chartUtils';
import { FileTypes } from '@/components/Chart/types';
import { cashNodesToNodes } from '@/components/GraphPanel/utils';
import { useShowSnackbar } from '@/components/SnackBarNotification';
import { selectDownloadNotifyMsg } from '@/components/SnackBarNotification/messages';
import {
  CashedNode,
  DataInterval,
  HighchartsReactRef,
  Node,
  NodeGraphDataList,
  NodeWithProject,
  Project,
  UtilityWithProject,
} from '@/types';
import { GraphType } from '@/types/Graph';
// TODO: ステートの更新をuseReducerに変更する
export type GraphPanelContext = {
  // ProjectNodes
  nodesTree: Node[];
  fetchNodes: (projectId: number | null) => void;
  requestNodes: (projectId: number) => Promise<Node[] | undefined>;

  // Not categorized
  nodes: UtilityWithProject[];
  setNodes: Dispatch<SetStateAction<UtilityWithProject[]>>;
  nodeMap: Map<number, UtilityWithProject>;
  setNodeMap: Dispatch<Map<number, UtilityWithProject>>;
  graphType: GraphType;
  displayPeriod: {
    fromDate: Dayjs;
    toDate: Dayjs;
  };

  // ChartComponentRef
  chartComponentRef: MutableRefObject<HighchartsReactRef | undefined>;
  setChartComponentRef: (ref: MutableRefObject<HighchartsReactRef | undefined>) => void;
  getChartComponentRef: () => MutableRefObject<HighchartsReactRef | undefined>;

  // GraphSetting
  optionalData: {
    includeStandardData: boolean;
    includeLastWeekData: boolean;
    includeLastYearData: boolean;
  };

  // Datapoint loading threshold
  DATAPOINT_LOADING_THRESHOLD: number;

  dataInterval: DataInterval;
  columnHeight: number;
  setColumnHeight: Dispatch<SetStateAction<number>>;
  projectSelectionHeight: number;
  setProjectSelectionHeight: Dispatch<SetStateAction<number>>;
  setGraphType: Dispatch<SetStateAction<GraphType>>;
  setDataInterval: Dispatch<SetStateAction<DataInterval>>;
  setFromDate: Dispatch<SetStateAction<Dayjs>>;
  setToDate: Dispatch<SetStateAction<Dayjs>>;
  setIncludeStandardData: Dispatch<SetStateAction<boolean>>;
  setIncludeLastWeekData: Dispatch<SetStateAction<boolean>>;
  setIncludeLastYearData: Dispatch<SetStateAction<boolean>>;

  // CurrentProjectId
  currentProjectId: Project['id'] | null;
  updateCurrentProjectId: (projectId: Project['id'] | null) => void;

  // PickedNodeIds
  pickedNodeIds: Node['id'][];
  appendPickedNodeId: (nodeId: Node['id']) => void;
  removePickedNodeId: (nodeId: Node['id']) => void;
  clearPickedNodeIds: () => void;
  getPickedNodes: () => NodeWithProject[];

  // ClientNodeData
  clientNodeData: NodeGraphDataList | undefined;

  // Loading State
  isLoading: {
    all: boolean;
    isLoadingProjectNodes: boolean;
    isLoadingNodeData: boolean;
  };

  // getCurrentChart
  getCurrentChart: () => Highcharts.Chart | undefined;

  // downloadGraph
  downloadGraph: (options: { fileType: FileTypes; filename?: string }[]) => void;
};

const GraphPanelContext = createContext<GraphPanelContext | null>(null);

function GraphPanelContextProvider({ children }: { children: ReactNode }) {
  const [nodes, setNodes] = useState<UtilityWithProject[]>([]);
  const [nodeMap, setNodeMap] = useState<Map<number, UtilityWithProject>>(new Map());
  const [graphType, setGraphType] = useState<GraphType>(GraphType.Line);
  const [fromDate, setFromDate] = useState<Dayjs>(dayjs().startOf('day'));
  const [toDate, setToDate] = useState<Dayjs>(dayjs());
  const [dataInterval, setDataInterval] = useState<DataInterval>('1day');
  const [includeStandardData, setIncludeStandardData] = useState<boolean>(false);
  const [includeLastWeekData, setIncludeLastWeekData] = useState<boolean>(false);
  const [includeLastYearData, setIncludeLastYearData] = useState<boolean>(false);
  const [columnHeight, setColumnHeight] = useState<number>(500);
  const [projectSelectionHeight, setProjectSelectionHeight] = useState<number>(500);
  const showSnackbar = useShowSnackbar();
  const DATAPOINT_LOADING_THRESHOLD = 2500;

  /**
   *
   *
   * ProjectNodes
   * プロジェクトノードの管理
   *
   */
  const [nodesTree, setNodesTree] = useState<Node[]>([]);
  const [projectNodeCache, setProjectNodeCache] = useState<CashedNode[]>([]);
  const controllRef = useRef<AbortController>();

  const { trigger: fetchClientProjectNodes, isLoading: isLoadingClientProjectNodes } =
    useListClientNodesTree();

  async function requestNodes(projectId: Project['id']) {
    if (controllRef.current) {
      controllRef.current.abort();
    }
    controllRef.current = new AbortController();
    const signal = controllRef.current.signal;

    const res = await fetchClientProjectNodes({
      urlParameter: {
        projectId: projectId,
      },
      signal,
    });

    if (res?.nodesTree) {
      return res.nodesTree;
    }
  }

  async function fetchNodes(
    projectId: Project['id'] | null,
  ): Promise<'return cache' | 'fetch nodes' | 'something wrong'> {
    // projectIdがnullの場合は、nodesTreeを空にする
    if (projectId === null) {
      setNodesTree([]);
      return 'return cache';
    }

    // EvaluateCache
    const cacheHaveItem = projectNodeCache?.some((node) => node.projectId === projectId);
    const cacheIsEmpty = projectNodeCache.length === 0;
    const cacheDoesNotHaveItem = !cacheIsEmpty || !cacheHaveItem;

    //
    // Cache has projectId Items
    //
    if (cacheHaveItem && !cacheIsEmpty) {
      // Find item and Return cache
      const cache = projectNodeCache.find((node) => node.projectId === projectId);
      if (cache) {
        setNodesTree(cache.nodes);
        // return result
        return 'return cache';
      }
    }
    //
    // if cache doesn't have projectId Items or cache is empty, fetch nodes
    //
    if (cacheDoesNotHaveItem || cacheIsEmpty) {
      // Request nodes
      const requestedProjectNodes = await requestNodes(projectId);

      if (requestedProjectNodes !== undefined) {
        // Assign to nodesTree
        setNodesTree(requestedProjectNodes);

        // Assign to cache
        const newCache = { projectId: projectId, nodes: requestedProjectNodes };
        setProjectNodeCache((prev) => {
          if (prev === undefined) return [];
          return [...prev, newCache];
        });

        // return result
        return 'fetch nodes';
      }
    }

    return 'something wrong';
  }

  /**
   *
   *  PickedNode
   *  選択したノードの管理
   *
   */
  const [pickedNodeIds, setPickedNodeIds] = useState<Node['id'][]>([]);

  function appendPickedNodeId(nodeId: Node['id']) {
    setPickedNodeIds([...pickedNodeIds, nodeId]);
  }

  function removePickedNodeId(nodeId: Node['id']) {
    setPickedNodeIds(pickedNodeIds.filter((id) => id !== nodeId));
  }

  function clearPickedNodeIds() {
    setPickedNodeIds([]);
  }

  // 再帰的にノードを検索し取得する
  // 木構造のnodesTreeから、pickedNodeIdsに含まれるノードを再帰的に検索し取得する
  const getPickedNodes: () => NodeWithProject[] = () => {
    // CacheからProjectNodesを取得する
    const nodesTree = cashNodesToNodes(projectNodeCache);
    // BUG ここで、projectNodes取得しているが、ProjectNodesはSELECTの度に再取得しているので、PickedNodeも再取得することになる
    const pickedNodes = nodesTree.filter((node) => pickedNodeIds.includes(node.id));
    return pickedNodes;
  };

  /**
   *
   * ProjectIdの管理
   *
   */

  // 現在選択中のプロジェクト
  const [currentProjectId, setCurrentProjectId] = useState<Project['id'] | null>(null);

  // プロジェクトIDの更新
  async function updateCurrentProjectId(projectId: Project['id'] | null) {
    setCurrentProjectId(projectId);
    await fetchNodes(projectId);
  }

  /**
   *
   *  ChartComponentのRefの管理
   *
   *
   */
  const chartComponentRef = useRef<HighchartsReactRef>();

  function setChartComponentRef(ref: MutableRefObject<HighchartsReactRef | undefined>) {
    chartComponentRef.current = ref.current;
  }

  function getChartComponentRef() {
    return chartComponentRef;
  }

  /**
   *
   *
   * ClientNodeDataの管理
   *
   */
  const {
    data: ListClientNodeDataResponse,
    trigger: fetchListClientNodeData,
    isLoading: isLoadingClientNodeData,
  } = useListClientNodeData();
  // clientNodeData

  const clientNodeData = useMemo(() => {
    return ListClientNodeDataResponse?.nodeData ?? undefined;
  }, [ListClientNodeDataResponse]);

  const fetchNodeData = () => {
    if (pickedNodeIds.length === 0) return;
    getClientNodeData({
      nodeIds: pickedNodeIds,
      startAt: fromDate.toDate(),
      endAt: toDate.toDate(),
      interval: dataInterval,
      includeLastWeekData,
      includeLastYearData,
    });
  };

  useEffect(() => {
    fetchNodeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pickedNodeIds, fromDate, toDate, dataInterval, includeLastWeekData, includeLastYearData]);

  // fetchClientNodeData
  function getClientNodeData({
    nodeIds,
    startAt,
    endAt,
    interval,
    includeLastWeekData,
    includeLastYearData,
  }: {
    nodeIds: Node['id'][];
    startAt: Date;
    endAt: Date;
    interval: DataInterval;
    includeLastWeekData: boolean;
    includeLastYearData: boolean;
  }) {
    // ノードが未選択の場合はリクエストしない
    if (nodesTree.length === 0) return;

    const queryParameter = {
      nodeIds: nodeIds,
      startAt: startAt,
      endAt: endAt,
      interval: interval,
      includeLastWeekData,
      includeLastYearData,
    };

    fetchListClientNodeData({
      queryParameter,
    });
  }
  function getCurrentChart() {
    return getChartComponentRef()?.current?.chart;
  }

  function downloadGraph(options: { fileType: FileTypes; filename?: string }[]) {
    const chart = getCurrentChart();

    // チャートがない場合はコンソールにエラーを出す
    if (!chart) {
      console.error('チャートが存在しません');
      return;
    }

    // ダウンロードの種類ごとにダウンロードする
    options.forEach((option) => {
      const { fileType, filename } = option;
      try {
        downloadChart(chart, {
          fileType,
          filename: filename ?? generateDownloadFilename({ fileType }),
        });
        showSnackbar(selectDownloadNotifyMsg({ fileType, isSuccess: true }), 'success');
      } catch (error) {
        showSnackbar(selectDownloadNotifyMsg({ fileType, isSuccess: false }), 'error');
      }
    });
  }

  /**
   *
   *  Context Objectへアサイン
   *
   */
  const graphPanelContext: GraphPanelContext = {
    requestNodes,
    currentProjectId,
    updateCurrentProjectId,
    nodesTree,
    fetchNodes,
    nodes,
    setNodes,
    nodeMap,
    setNodeMap,
    graphType,
    setGraphType,
    displayPeriod: {
      fromDate,
      toDate,
    },
    setFromDate,
    setToDate,
    chartComponentRef,
    setChartComponentRef,
    getChartComponentRef,
    DATAPOINT_LOADING_THRESHOLD,
    dataInterval,
    setDataInterval,
    optionalData: {
      includeStandardData,
      includeLastWeekData,
      includeLastYearData,
    },
    setIncludeStandardData,
    setIncludeLastWeekData,
    setIncludeLastYearData,
    columnHeight,
    setColumnHeight,
    projectSelectionHeight,
    setProjectSelectionHeight,
    pickedNodeIds,
    appendPickedNodeId,
    removePickedNodeId,
    clearPickedNodeIds,
    clientNodeData,
    getPickedNodes,
    isLoading: {
      all: isLoadingClientProjectNodes || isLoadingClientNodeData,
      isLoadingProjectNodes: isLoadingClientProjectNodes,
      isLoadingNodeData: isLoadingClientNodeData,
    },
    getCurrentChart,
    downloadGraph,
  };

  return (
    <GraphPanelContext.Provider value={graphPanelContext}>{children}</GraphPanelContext.Provider>
  );
}

function useGraphPanelContext(): GraphPanelContext {
  const context = useContext(GraphPanelContext);

  if (!context) {
    throw new Error('useGraphPanelContext must be used within a GraphPanelContextProvider');
  }
  return context;
}

export { GraphPanelContextProvider, useGraphPanelContext };
