import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListClientNodesTreeUrlParameter, ListClientNodesTreeResponse } from '@/api/types';

function useListClientNodesTree() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListClientNodesTreeResponse,
    { urlParameter: ListClientNodesTreeUrlParameter; signal?: AbortSignal }
  >(Endpoints.ListClientNodesTree, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListClientNodesTree };
