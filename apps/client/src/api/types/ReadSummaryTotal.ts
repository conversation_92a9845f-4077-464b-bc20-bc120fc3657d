import { DailyScore, Project } from '@/types';

type ReadSummaryTotalQueryParameter = {
  projectId: Project['id'];
  year?: number;
  month?: number;
};

type ReadSummaryTotalResponse = {
  summaryTotal: {
    projectId: number;
    year: number;
    month: number;
    totalReductionCost: {
      daily: DailyScore[];
    };
    totalReductionTargetCost: {
      daily: DailyScore[];
    };
  };
};

export type { ReadSummaryTotalQueryParameter, ReadSummaryTotalResponse };
