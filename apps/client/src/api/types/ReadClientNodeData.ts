import { DataInterval, NodeGraphData } from '@/types';

type ReadClientNodeDataQueryParameter = {
  startAt?: Date;
  endAt?: Date;
  interval?: DataInterval;
  threshold?: number;
  includeStandardData?: boolean;
  includeLastWeekData?: boolean;
  includeLastYearData?: boolean;
};
type ReadClientNodeDataURLParameter = {
  nodeId: number;
};
type ReadClientNodeDataResponse = {
  nodeData: NodeGraphData;
};

export type {
  ReadClientNodeDataQueryParameter,
  ReadClientNodeDataURLParameter,
  ReadClientNodeDataResponse,
};
