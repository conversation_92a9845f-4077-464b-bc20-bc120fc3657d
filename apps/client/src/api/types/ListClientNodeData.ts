import { DataInterval, Node, NodeGraphDataList } from '@/types';

type ListClientNodeDataQueryParameter = {
  nodeIds: Node['id'][];
  startAt?: Date;
  endAt?: Date;
  interval?: DataInterval;
  threshold?: number;
  includeStandardData?: boolean;
  includeLastWeekData?: boolean;
  includeLastYearData?: boolean;
};

type ListClientNodeDataResponse = {
  nodeData: NodeGraphDataList;
};

export type { ListClientNodeDataQueryParameter, ListClientNodeDataResponse };
