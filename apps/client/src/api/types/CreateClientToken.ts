type CreateClientTokenRequestBody = {
  username: string;
  password: string;
};

type CreateClientTokenResponseBody = {
  access_token: string;
  token_type: string;
  refresh_token: string;
};
type CreateClientTokenUnprocessableEntity = {
  detail: {
    errorCode: string;
    errorMsg: string;
  };
};

export type {
  CreateClientTokenRequestBody,
  CreateClientTokenResponseBody,
  CreateClientTokenUnprocessableEntity,
};
