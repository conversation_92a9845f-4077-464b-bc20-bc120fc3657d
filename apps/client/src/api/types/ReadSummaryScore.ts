import { MonthlyScore, Project, UtilityScore } from '@/types';

type ReadSummaryScoreQueryParameter = {
  projectId?: Project['id'];
  year?: number;
  month?: number;
};

type ReadSummaryScoreResponse = {
  summaryScore: {
    projectId: number;
    year: number;
    month: number;
    latestDate: string | null;
    totalReductionCost: MonthlyScore;
    totalReductionTargetCost: MonthlyScore;
    utilities: UtilityScore[];
  };
};

export type { ReadSummaryScoreQueryParameter, ReadSummaryScoreResponse };
