import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ReadSummaryScoreQueryParameter, ReadSummaryScoreResponse } from '@/api/types';

function useReadSummaryScore() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadSummaryScoreResponse,
    {
      queryParameter: ReadSummaryScoreQueryParameter;
    }
  >(Endpoints.ReadSummaryScore, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadSummaryScore };
