import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ReadClientProjectURLParameter, ReadClientProjectResponse } from '@/api/types';

function useReadClientProject() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadClientProjectResponse,
    { urlParameter: ReadClientProjectURLParameter }
  >(Endpoints.ReadClientProject, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadClientProject };
