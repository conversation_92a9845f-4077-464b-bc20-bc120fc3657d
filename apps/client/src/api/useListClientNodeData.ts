import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ListClientNodeDataQueryParameter, ListClientNodeDataResponse } from '@/api/types';

function useListClientNodeData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ListClientNodeDataResponse,
    { queryParameter: ListClientNodeDataQueryParameter }
  >(Endpoints.ListClientNodeData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useListClientNodeData };
