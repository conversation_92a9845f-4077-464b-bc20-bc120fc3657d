import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ReadSummaryDetailQueryParameter, ReadSummaryDetailResponse } from '@/api/types';

function useReadSummaryDetail() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadSummaryDetailResponse,
    {
      queryParameter: ReadSummaryDetailQueryParameter;
    }
  >(Endpoints.ReadSummaryDetail, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadSummaryDetail };
