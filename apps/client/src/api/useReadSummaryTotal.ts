import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import { ReadSummaryTotalQueryParameter, ReadSummaryTotalResponse } from '@/api/types';

function useReadSummaryTotal() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadSummaryTotalResponse,
    {
      queryParameter: ReadSummaryTotalQueryParameter;
    }
  >(Endpoints.ReadSummaryTotal, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadSummaryTotal };
