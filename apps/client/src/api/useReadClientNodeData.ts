import { useEcoisSWRMutation, abstractFetcher, Endpoints } from 'lib/api';
import {
  ReadClientNodeDataQueryParameter,
  ReadClientNodeDataURLParameter,
  ReadClientNodeDataResponse,
} from '@/api/types';

function useReadClientNodeData() {
  const { data, error, isMutating, trigger, ...props } = useEcoisSWRMutation<
    ReadClientNodeDataResponse,
    {
      queryParameter: ReadClientNodeDataQueryParameter;
      urlParameter: ReadClientNodeDataURLParameter;
    }
  >(Endpoints.ReadClientNodeData, abstractFetcher);

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadClientNodeData };
