import { Endpoints, abstractFetcher, useEcoisSWRMutation } from 'lib/api';
import { ReadClientUserProfileResponse } from './types';

function useReadClientUserProfile() {
  const { data, error, isMutating, trigger, ...props } =
    useEcoisSWRMutation<ReadClientUserProfileResponse>(
      Endpoints.ReadClientUserProfile,
      abstractFetcher,
    );

  return {
    data,
    error,
    isLoading: isMutating,
    trigger,
    ...props,
  };
}

export { useReadClientUserProfile };
