import { LayoutConstants } from '@/constants/LayoutConstants';
import { Box } from '@mui/material';
import { PropsWithChildren } from 'react';

const GLOBAL_MENU_BAR_HEIGHT = LayoutConstants.GLOBAL_NAVIGATION_HEIGHT;
const staticHeight = `calc(100vh - ${GLOBAL_MENU_BAR_HEIGHT})`;

function FullScreenLayout({ children }: PropsWithChildren) {
  return (
    <Box
      sx={{
        width: '100%',
        maxHeight: staticHeight,
        height: staticHeight,
        marginTop: GLOBAL_MENU_BAR_HEIGHT,
      }}
    >
      {children}
    </Box>
  );
}

export { FullScreenLayout };
