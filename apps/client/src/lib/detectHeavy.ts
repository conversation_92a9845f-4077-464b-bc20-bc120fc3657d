import dayjs from 'dayjs';
import { dataIntervalList } from '@/components/GraphPanel/GraphSetting/Panel/components/DataIntervalSelection';
import { DataInterval } from '@/types';

function detectHeavy({
  startAt,
  endAt,
  interval,
  includeLastWeekData,
  includeLastYearData,
  nodeCount,
  thredshold,
}: {
  startAt: Date;
  endAt: Date;
  interval: DataInterval;
  includeLastWeekData: boolean;
  includeLastYearData: boolean;
  nodeCount: number;
  thredshold: number;
}) {
  const durationDay = dayjs(endAt).diff(dayjs(startAt), 'day') + 1;

  const datapointCountPerDay =
    dataIntervalList.find((i) => i.value === interval)?.datapointCountPerDay || 1;

  const multiplierWeekData = includeLastWeekData ? 2 : 1;
  const multiplierYearData = includeLastYearData ? 2 : 1;

  const dataCount =
    durationDay * datapointCountPerDay * multiplierWeekData * multiplierYearData * nodeCount;

  const isHeavy = dataCount > thredshold;

  return isHeavy;
}

export { detectHeavy };
