import { Project } from './Project';

type Color = 'red' | 'blue' | 'green' | 'yellow' | 'purple';

type EnergyType = {
  id: number;
  name: string;
  unit: string;
  color: Color;
};

type Node = {
  id: number | string; // 基準値の場合はstring
  name: string;
  order: number;
  children: Node[];
  energyType: EnergyType;
};

type NodeWithProject = Node & { projectId: Project['id']; children: NodeWithProject[] };

type CashedNode = { projectId: Project['id']; nodes: Node[] };

type Utility = Omit<Node, 'children'>;

type UtilityInfo = {
  utilityId: number;
  nodeId: number;
  name: string;
  order: number;
  color: Color;
  unitcost: number;
};

type UtilityWithProject = Utility & { project: { id: number; name: string } };

type DataInterval = '10min' | '30min' | '1hour' | '1day' | '1month';

type Data = {
  date: string;
  value: number;
};

type NodeData = {
  nodeId: number;
  measuredData: Data[];
  standardData: Data[];
  lastWeekData: Data[];
  lastYearData: Data[];
};

type NodeGraphData = {
  startAt: Date;
  endAt: Date;
  interval: DataInterval;
  threshold: number;
  includeStandardData: boolean;
  includeLastWeekData: boolean;
  includeLastYearData: boolean;
  data: NodeData;
};

type NodeGraphDataList = {
  startAt: Date;
  endAt: Date;
  interval: DataInterval;
  threshold: number;
  includeStandardData: boolean;
  includeLastWeekData: boolean;
  includeLastYearData: boolean;
  data: NodeData[];
};

export const colorMap: Record<Color | 'default', string> = {
  red: '#f44336',
  blue: '#2196f3',
  green: '#4caf50',
  yellow: '#ffeb3b',
  purple: '#9c27b0',
  default: '#9e9e9e',
};

export const reductionCostColorMap: Record<string, string> = {
  target: colorMap.green,
  amount: colorMap.red,
};

export type {
  Color,
  DataInterval,
  Node,
  NodeWithProject,
  CashedNode,
  NodeData,
  NodeGraphData,
  NodeGraphDataList,
  Utility,
  UtilityInfo,
  UtilityWithProject,
};
