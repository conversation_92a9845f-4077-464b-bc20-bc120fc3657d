import { UtilityInfo } from '@/types';

type MonthlyScore = {
  monthly: number | null;
  latest: number | null;
};

type DailyScore = {
  date: string;
  value: number;
};

interface UtilityScore extends UtilityInfo {
  measuredAmount: MonthlyScore;
  standardAmount: MonthlyScore;
  reductionAmount: MonthlyScore;
  reductionCost: MonthlyScore;
  reductionTargetAmount: MonthlyScore;
  reductionTargetCost: MonthlyScore;
}

interface DailyUtilityScore extends UtilityInfo {
  measuredAmount: { daily: DailyScore[] };
  standardAmount: { daily: DailyScore[] };
  reductionAmount: { daily: DailyScore[] };
  reductionCost: { daily: DailyScore[] };
  reductionTargetAmount: { daily: DailyScore[] };
  reductionTargetCost: { daily: DailyScore[] };
}

export type { UtilityScore, DailyScore, MonthlyScore, DailyUtilityScore };
