{"name": "client", "version": "1.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite --port 8081", "dev:local": "vite --mode localapi --port 8081", "dev:staging": "vite --mode staging --port 8081", "build": "vite build", "build:local": "vite build --mode localapi", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.12.2", "@mui/x-date-pickers": "^6.3.1", "@mui/x-tree-view": "^6.17.0", "@sentry/react": "^7.75.1", "@sentry/vite-plugin": "^2.9.0", "@types/node": "^18.14.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "dayjs": "^1.11.13", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "highcharts": "^11.0.0", "highcharts-react-official": "^3.2.2", "lib": "*", "notistack": "^3.0.2", "prettier": "^2.8.4", "react": "^18.2.0", "react-arborist": "^3.4.3", "react-csv-downloader": "^2.9.1", "react-dom": "^18.2.0", "react-use": "^17.6.0", "rsuite": "^5.83.1", "turbo": "^1.8.3"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "tsconfig": "*", "typescript": "^4.9.5", "vite": "^4.1.4", "viteconfig": "*"}, "msw": {"workerDirectory": "public"}}