#################
## build env   ##
#################
FROM arm64v8/node:22.13.1-alpine AS builder
WORKDIR /usr/src/app

# turborepoの実行に必要なパッケージをインストール
RUN apk add --no-cache libc6-compat

# App用の変数をセット
ARG app
ARG env

# Sentry用の変数をセット
ARG sentry_auth_token
ARG sentry_dsn
ARG sentry_organization
ARG sentry_project
ENV SENTRY_AUTH_TOKEN=${sentry_auth_token}
ENV SENTRY_DSN=${sentry_dsn}
ENV SENTRY_ORG=${sentry_organization}
ENV SENTRY_PROJECT=${sentry_project}

# ローカルマシン上のすべてのファイルをコピー
COPY . .

# 依存関係のあるパッケージをインストール
RUN yarn install --network-timeout 1200000
RUN yarn build:${app}:${env}


#################
## nginx image ##
#################
FROM arm64v8/nginx:1.27.3-alpine

ARG app
COPY ./conf/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/apps/${app}/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"] 
