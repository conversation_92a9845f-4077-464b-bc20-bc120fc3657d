services:
  admin:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app=admin
        - env=local
    image: ecois.web.admin
    container_name: ecois.web.admin
    hostname: ecois.web.admin
    ports:
      - '3000:80'

  client:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app=client
        - env=local
    image: ecois.web.client
    container_name: ecois.web.client
    hostname: ecois.web.client
    ports:
      - '3001:80'
