# Picoada Ecois V2 Web Repository

## Overview

Ecois V2 の Web アプリケーション

- Admin
- Client

### URLs

|        | Production              | Staging                         | Preview                         |
| :----- | :---------------------- | :------------------------------ | :------------------------------ |
| Admin  | https://admin.ecois.jp  | https://admin.staging.ecois.jp  | https://admin.preview.ecois.jp  |
| Client | https://client.ecois.jp | https://client.staging.ecois.jp | https://client.preview.ecois.jp |

### CI/CD

Github Actions 経由でビルド及びデプロイをおこないます。

- Tag: Production へデプロイ(Tag Format: `2024.01.r1` )
- Main Branch: Staging へデプロイ
- Pull Request: Preview へデプロイ(バックエンドは Staging 環境)

## Environment Setup

- 高速な開発のために開発はローカルでビルドを行う必要があります。
- デプロイとローカルでの確認用に Docker 環境も用意されています。

### For Development

フロントエンド開発者向けの環境(ローカルでの開発環境構築)

#### nodejs version

```
$ node -v
22.0.0

# 22系以上を推奨
```

#### 開発環境の構築手順

##### 1. 依存関係のインストール

```sh
$ yarn install
```

##### 2. 開発サーバーを起動

```sh
$ yarn dev:{appName}:{environment}

## Samples
yarn dev:admin:local # adminがlocal環境で起動
yarn dev:local  # 双方がlocal環境で起動
yarn dev:client # ClientがDev環境で起動
```

開発用サーバーポート

- admin 8080
- client 8081

##### 3. ビルドを行う場合

```sh
$ yarn build:{appName}:{environment}

## ルールはdevコマンドと同様
```

apps/admin or apps/client 配下の dist ディレクトリにビルド結果が格納されます。

##### 4. ビルド結果を確認する

```sh
$ yarn preview:{appName}
```

apps/{appName}/dist ディレクトリ内がホスティングされます。

---

### For Preview

Docker イメージをビルドして確認を行えます。

##### 1. Docker イメージのビルド

```sh
$ make build/{appName}

## Samples
$ make build/admin
$ make build/client
$ make build  # 双方がlocal環境で起動
```

##### 2. Docker コンテナの起動

```sh
$ make up/{appName}/{environment}

## Samples
$ make up/admin
$ make up/client
$ make up  # 双方がlocal環境で起動
```

##### 3. Docker コンテナの停止

```sh
$ make down

## すべてのコンテナを停止
```

---

### For Deploy

DockerImage によるデプロイを前提としています。

[Dockerfile の ARG](https://docs.docker.com/engine/reference/builder/#arg) を利用することで、1 つの Dockerfile でそれぞれのアプリと環境の組み合わせに対応できるようにしています。

- arg1: app
  - admin
  - client
- arg2: env
  - staging
  - production

```sh
#dockerビルドコマンドのサンプル
docker build --build-arg app=admin env=production .

```

## Environments

| Environment | backendAPI     | env file         |
| ----------- | -------------- | ---------------- |
| development | msw(mock)      | .env.development |
| local       | local docker   | .env.localapi    |
| staging     | staging server | .env.staging     |
| production  | prod server    | .env.production  |

## Deployment

Github Actions 経由でのデプロイを構築しています。

1. Github Actions 上で Build
2. ECR へ Push
3. ECS Task Definition を更新
4. ECS へ Deploy

## Frontend architecture docs

フロントエンドアーキテクチャに関してはこちらを参照してください。

[Notion/フロントエンドアーキテクチャ](https://www.notion.so/picoada/Frontend-architecture-14b37dc8205d4dedad47f81a24341e0d?pvs=4)
