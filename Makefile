build:  # Build containers.
	docker compose build --no-cache

build/admin:  # Build Admin container.
	docker compose build --no-cache admin

build/client:  # Build Client container.
	docker compose build --no-cache client

up:  # Start containers.
	docker compose up -d

up/admin:  # Start Admin container.
	docker compose up -d admin

up/client:  # Start Client container.
	docker compose up -d client

down:  # Stop containers.
	docker compose down --remove-orphans

ls:  # Show containers.
	docker container ls

ps:  # Show containers for compose.
	docker compose ps

logs:  # Tail container logs.
	docker compose logs -f --tail=500

start: up ls logs  # Start containers and tail their logs.

restart: down up ls logs  # Restart containers and tail their logs.
