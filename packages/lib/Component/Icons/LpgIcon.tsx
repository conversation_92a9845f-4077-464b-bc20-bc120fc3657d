import { SvgIcon, SvgIconProps } from '@mui/material';

function LpgIcon(props: SvgIconProps) {
  return (
    <SvgIcon
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        d='M11.5333 3V5.45962C11.5333 6.18314 11.761 6.79001 12.2163 7.28025C12.6716 7.77049 13.2295 8.01561 13.89 8.01561C14.1759 8.01561 14.4485 7.95665 14.7076 7.83873C14.9666 7.72082 15.2045 7.54709 15.4211 7.31757L15.832 6.86362C16.6325 7.42468 17.294 8.1383 17.8167 9.00449C17.2725 9.02572 16.755 9.12277 16.2764 9.31046C16.1645 9.16421 16.0457 9.02227 15.92 8.88464C15.6089 9.10616 15.2822 9.2723 14.94 9.38306C14.6292 9.48364 14.312 9.53855 13.9884 9.54779C13.4525 9.25184 12.8115 9.12008 12.1322 9.08802C11.8642 8.93201 11.6111 8.73642 11.3728 8.50124C10.665 7.80261 10.2597 6.93553 10.1567 5.90002C9.55001 6.45586 9.01334 7.03624 8.54668 7.64115C8.08001 8.24607 7.68723 8.86274 7.36834 9.49117C7.2412 9.7418 7.12642 9.99393 7.02401 10.2476V9.08H5.96036C6.18748 8.5916 6.4549 8.10832 6.76261 7.63016C7.93768 5.80434 9.52792 4.26095 11.5333 3Z'
        fill='currentColor'
      />
      <path
        d='M5.77537 16.92C6.09596 17.5846 6.51596 18.2014 7.03538 18.7704C8.39229 20.2568 10.0472 21 12 21C13.9529 21 15.6077 20.2568 16.9646 18.7704C17.4633 18.2242 17.8703 17.6339 18.1857 16.9995C18.1592 16.9998 18.1326 17 18.106 17C17.5741 17 17.0658 16.9342 16.592 16.7893C16.186 17.4137 15.6795 17.9358 15.0728 18.3556C14.7072 18.7475 12.5133 19.5 12 19.5C11.4867 19.5 9.2928 18.7475 8.92724 18.3556C8.36332 17.9654 7.88607 17.4869 7.4955 16.92H5.77537Z'
        fill='currentColor'
      />
      <path d='M4.64801 16V10.16H5.67201V15.112H8.77601V16H4.64801Z' fill='currentColor' />
      <path
        d='M9.71051 16V10.16H11.4305C12.3052 10.16 12.9532 10.3227 13.3745 10.648C13.8012 10.968 14.0145 11.464 14.0145 12.136C14.0145 12.792 13.8118 13.2853 13.4065 13.616C13.0012 13.9413 12.3905 14.104 11.5745 14.104H10.2065V13.224H11.4545C11.9932 13.224 12.3852 13.1387 12.6305 12.968C12.8758 12.792 12.9985 12.5147 12.9985 12.136C12.9985 11.752 12.8758 11.4747 12.6305 11.304C12.3852 11.128 11.9932 11.04 11.4545 11.04H10.7105V16H9.71051Z'
        fill='currentColor'
      />
      <path
        d='M17.754 16.08C17.0927 16.08 16.5247 15.96 16.05 15.72C15.5753 15.48 15.21 15.136 14.954 14.688C14.7033 14.24 14.578 13.704 14.578 13.08C14.578 12.456 14.7033 11.92 14.954 11.472C15.2047 11.024 15.562 10.68 16.026 10.44C16.4953 10.2 17.0527 10.08 17.698 10.08C18.0553 10.08 18.394 10.12 18.714 10.2C19.034 10.2747 19.3193 10.3893 19.57 10.544L19.338 11.384C19.114 11.2507 18.858 11.1493 18.57 11.08C18.282 11.0053 17.9913 10.968 17.698 10.968C17.266 10.968 16.8953 11.0507 16.586 11.216C16.282 11.3813 16.0473 11.6213 15.882 11.936C15.722 12.2507 15.642 12.632 15.642 13.08C15.642 13.528 15.722 13.9093 15.882 14.224C16.0473 14.5387 16.2873 14.7787 16.602 14.944C16.922 15.1093 17.306 15.192 17.754 15.192C17.9887 15.192 18.2153 15.1707 18.434 15.128C18.6527 15.08 18.85 15.0107 19.026 14.92L18.89 15.24V13.44H17.034V12.6H19.882V15.624C19.5993 15.768 19.2687 15.88 18.89 15.96C18.5167 16.04 18.138 16.08 17.754 16.08Z'
        fill='currentColor'
      />
    </SvgIcon>
  );
}

export { LpgIcon };
