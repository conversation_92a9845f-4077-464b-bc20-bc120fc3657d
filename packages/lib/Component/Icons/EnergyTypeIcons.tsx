import { SxProps } from '@mui/material';
import {
  ElectricityIcon,
  GasIcon,
  HeavyOilIcon,
  KeroseneOilIcon,
  SteamIcon,
  TemperatureIcon,
  LpgIcon,
  WaterIcon,
  WaterLitterIcon,
  ElectricityColorIcon,
  GasColorIcon,
  HeavyOilColorIcon,
  KeroseneOilColorIcon,
  LpgColorIcon,
  SteamColorIcon,
  TemperatureColorIcon,
  WaterColorIcon,
  WaterLitterColorIcon,
  HotSpringColorIcon,
  HotSpringIcon,
} from 'lib/Component/Icons';

interface EnergyTypeIconsProps {
  energyTypeId: number;
  sx?: SxProps;
}
function EnergyTypeColorIcons({ energyTypeId }: EnergyTypeIconsProps) {
  switch (energyTypeId) {
    case 1:
      return <WaterColorIcon />;
    case 2:
      return <GasColorIcon />;
    case 3:
      return <LpgColorIcon />;
    case 4:
      return <HeavyOilColorIcon />;
    case 5:
      return <KeroseneOilColorIcon />;
    case 6:
      return <ElectricityColorIcon />;
    case 7:
      return <SteamColorIcon />;
    case 8:
      return <WaterLitterColorIcon />;
    case 9:
      return <TemperatureColorIcon />;
    case 10:
      return <HotSpringColorIcon />;
    default:
      return null;
  }
}

function EnergyTypeIcons(props: EnergyTypeIconsProps) {
  const { energyTypeId } = props;
  switch (energyTypeId) {
    case 1:
      return <WaterIcon {...props} />;
    case 2:
      return <GasIcon {...props} />;
    case 3:
      return <LpgIcon {...props} />;
    case 4:
      return <HeavyOilIcon {...props} />;
    case 5:
      return <KeroseneOilIcon {...props} />;
    case 6:
      return <ElectricityIcon {...props} />;
    case 7:
      return <SteamIcon {...props} />;
    case 8:
      return <WaterLitterIcon {...props} />;
    case 9:
      return <TemperatureIcon {...props} />;
    case 10:
      return <HotSpringIcon {...props} />;
    default:
      return null;
  }
}

export { EnergyTypeColorIcons, EnergyTypeIcons };
