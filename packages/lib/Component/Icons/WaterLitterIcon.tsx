import { SvgIcon, SvgIconProps } from '@mui/material';

function WaterLitterIcon(props: SvgIconProps) {
  return (
    <SvgIcon
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.5846 2.51445C17.5011 6.84052 20 10.6179 20 13.9382C20 18.6403 16.3582 22 12 22C7.64182 22 4 18.6403 4 13.9382C4 10.6179 6.49894 6.84052 11.4154 2.51445L12 2L12.5846 2.51445ZM5.77778 13.9382C5.77778 17.6139 8.59032 20.2085 12 20.2085C15.4097 20.2085 18.2222 17.6139 18.2222 13.9382C18.2222 11.386 16.1719 8.17535 12 4.38644C7.8281 8.17535 5.77778 11.386 5.77778 13.9382Z'
        fill='currentColor'
      />
      <path
        d='M9.71429 17.5556V8.66667H11.6016V16.0061H15.4286V17.5556H9.71429Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.25714 18V8.22222H12.0588V15.5616H15.8857V18H9.25714ZM11.6016 16.0061V8.66667H9.71429V17.5556H15.4286V16.0061H11.6016Z'
        fill='white'
      />
    </SvgIcon>
  );
}

export { WaterLitterIcon };
