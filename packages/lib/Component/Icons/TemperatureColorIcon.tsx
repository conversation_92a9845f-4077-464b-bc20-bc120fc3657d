import { SvgIcon, SvgIconProps } from '@mui/material';

function TemperatureColorIcon(props: SvgIconProps) {
  return (
    <SvgIcon
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        d='M19.6728 18.5168C18.6686 19.0869 17.5322 19.3861 16.3755 19.3833C14.6052 19.3833 12.9069 18.6801 11.6551 17.4283C10.4033 16.1765 9.70001 14.4787 9.70001 12.7083C9.70001 10.938 10.4033 9.2402 11.6551 7.98839C12.9069 6.73659 14.6047 6.03333 16.375 6.03333C17.6975 6.03333 18.9373 6.41755 19.9696 7.08336L19.0706 9.32291C18.3076 8.71341 17.3569 8.38013 16.3746 8.38333C13.9727 8.38354 12.05 10.3063 12.05 12.7083C12.05 15.1105 13.9729 17.0333 16.375 17.0333C17.2706 17.0333 18.0963 16.7655 18.7866 16.3012L19.6728 18.5168Z'
        fill='#5C946A'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.75001 4.2C7.42631 4.2 8.07492 4.46866 8.55314 4.94688C9.03135 5.42509 9.30001 6.0737 9.30001 6.75C9.30001 7.4263 9.03135 8.0749 8.55314 8.55312C8.07492 9.03134 7.42631 9.3 6.75001 9.3C6.07371 9.3 5.42511 9.03134 4.94689 8.55312C4.46867 8.0749 4.20001 7.4263 4.20001 6.75C4.20001 6.0737 4.46867 5.42509 4.94689 4.94688C5.42511 4.46866 6.07371 4.2 6.75001 4.2ZM6.75001 5.63333C6.45385 5.63333 6.16983 5.75098 5.96041 5.9604C5.75099 6.16981 5.63335 6.45384 5.63335 6.75C5.63335 7.04616 5.75099 7.33019 5.96041 7.5396C6.16983 7.74902 6.45385 7.86667 6.75001 7.86667C7.04617 7.86667 7.3302 7.74902 7.53961 7.5396C7.74903 7.33019 7.86668 7.04616 7.86668 6.75C7.86668 6.45384 7.74903 6.16981 7.53961 5.9604C7.3302 5.75098 7.04617 5.63333 6.75001 5.63333Z'
        fill='#5C946A'
      />
    </SvgIcon>
  );
}

export { TemperatureColorIcon };
