import { SvgIcon, SvgIconProps } from '@mui/material';

function HotSpringIcon(props: SvgIconProps) {
  return (
    <SvgIcon
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        xmlns='http://www.w3.org/2000/svg'
        d='M6.47168 12.7588C6.44955 12.7906 6.42715 12.8221 6.40137 12.8506C6.01767 13.2751 5.73773 13.8422 5.7207 14.46C5.70206 15.1365 6.19792 15.9679 7.35547 16.6914C8.50042 17.407 10.141 17.8799 12 17.8799C13.8577 17.8799 15.4978 17.404 16.6426 16.6855C17.7992 15.9596 18.2994 15.1257 18.2793 14.4492C18.2611 13.8362 17.9782 13.2765 17.6006 12.8516L18.5811 11.2334C18.6261 11.2587 18.67 11.2878 18.7109 11.3223L18.7773 11.3828C19.479 12.1518 19.9126 13.1981 19.9961 14.3193C19.9986 14.3531 20 14.3722 20 14.4062C20 16.1928 18.8589 17.5797 17.4561 18.46C16.0156 19.3637 14.086 19.8926 12 19.8926C9.91648 19.8926 7.98623 19.3683 6.54688 18.4688C5.14278 17.5911 4.0001 16.2079 4 14.4199C4 14.3875 4.00156 14.3691 4.00391 14.3369C4.08629 13.21 4.51773 12.1551 5.22363 11.3828C5.28264 11.3183 5.34961 11.2657 5.4209 11.2246L6.47168 12.7588ZM12.6133 5.22461C12.579 5.33883 12.5286 5.44527 12.4619 5.53809C12.078 6.0724 11.982 6.60352 12.0332 7.21289C12.0898 7.88527 12.3189 8.60573 12.6064 9.4873C12.614 9.5104 12.6185 9.52378 12.626 9.54688C12.8896 10.3563 13.204 11.3173 13.2852 12.2852C13.3737 13.3415 13.1898 14.4371 12.4619 15.4531C12.4252 15.5044 12.3834 15.5503 12.3389 15.5918L11.0615 14.2979C11.0863 14.2482 11.1148 14.2005 11.1465 14.1562C11.5291 13.6222 11.6245 13.0907 11.5732 12.4814C11.5168 11.8103 11.2887 11.089 11.001 10.207C10.9933 10.1835 10.9891 10.17 10.9814 10.1465C10.7179 9.33707 10.4034 8.37733 10.3223 7.4082C10.2338 6.35187 10.4174 5.25671 11.1465 4.24219C11.2186 4.1418 11.306 4.05983 11.4033 4L12.6133 5.22461ZM9.2168 6.64551C9.19305 6.69216 9.16668 6.73752 9.13672 6.7793C8.70496 7.38154 8.67447 7.7639 8.70508 8.02832C8.74662 8.3867 8.91783 8.7498 9.21582 9.33105C9.22779 9.3544 9.2351 9.36733 9.24707 9.39062C9.5038 9.88999 9.86203 10.589 9.95605 11.3984C10.0648 12.3352 9.81621 13.2677 9.13672 14.2139C9.07739 14.2965 9.00666 14.3652 8.92969 14.4209L7.68945 13.165C7.72229 13.0763 7.76725 12.9934 7.82129 12.918C8.25353 12.3154 8.28359 11.9334 8.25293 11.6689C8.21134 11.3107 8.04007 10.9482 7.74219 10.3672C7.7301 10.3436 7.72306 10.3302 7.71094 10.3066C7.45422 9.8073 7.09591 9.10785 7.00195 8.2998C6.89304 7.36289 7.14172 6.42873 7.82129 5.48242C7.8561 5.43396 7.89381 5.38842 7.93555 5.34863L9.2168 6.64551ZM16.0576 6.61621L16.042 6.6543C15.7283 7.25661 15.679 7.69815 15.71 8.06445C15.7454 8.48187 15.885 8.88095 16.0947 9.44727C16.1017 9.4662 16.1062 9.47717 16.1133 9.49609C16.3035 10.0075 16.5509 10.6759 16.6143 11.4355C16.6838 12.2692 16.5326 13.1434 16.041 14.0898C15.9824 14.2026 15.9054 14.3009 15.8145 14.3789C15.8004 14.3909 15.7851 14.4011 15.7705 14.4121L14.5234 13.1504C14.5376 13.1138 14.5543 13.0784 14.5723 13.0439C14.8861 12.4413 14.9353 11.9982 14.9043 11.6318C14.8689 11.2143 14.7293 10.8151 14.5195 10.25C14.5126 10.2312 14.5079 10.2209 14.501 10.2021C14.3107 9.68936 14.0639 9.02142 14 8.26172C13.9299 7.42814 14.0787 6.55229 14.5732 5.60645C14.6299 5.49826 14.7039 5.40556 14.79 5.33203L16.0576 6.61621Z'
        fill='#currentColor'
      />
    </SvgIcon>
  );
}

export { HotSpringIcon };
