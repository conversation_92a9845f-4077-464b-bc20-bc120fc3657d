import { useEffect } from 'react';
function useTrack(...args: any[]) {
  let comment: any, param: any;

  if (args.length === 1) {
    param = args[0];
  } else if (args.length >= 2) {
    comment = args[0];
    param = args[1];
  }

  useEffect(() => {
    if (comment) {
      console.log(comment, param);
    } else {
      console.log(param);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param]);
}

export { useTrack };
