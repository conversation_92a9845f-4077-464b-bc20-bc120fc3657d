import { useCallback, useRef } from 'react';

type Debounce = (fn: () => void) => void;

function useDebounce(timeout: number): Debounce {
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const debounce: Debounce = useCallback(
    (fn: () => void) => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
      timer.current = setTimeout(() => {
        fn();
      }, timeout);
    },
    [timeout],
  );
  return debounce;
}

export { useDebounce };
