import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react-swc';
import { UserConfigExport } from 'vite';
import svgr from 'vite-plugin-svgr';

export const baseConfig: UserConfigExport = {
  ...(process.env.SENTRY_DSN
    ? {
        define: {
          'import.meta.env.SENTRY_DSN': JSON.stringify(process.env.SENTRY_DSN),
        },
      }
    : {}),
  plugins: [
    // EnvironmentPlugin({ SENTRY_DSN: process.env.SENTRY_DSN }, { defineOn: 'import.meta.env' }),
    svgr(),
    react({
      jsxImportSource: '@emotion/react',
    }),
    sentryVitePlugin({
      disable: process.env.NODE_ENV === 'development',
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: process.env.SENTRY_ORG,
      project: process.env.SENTRY_PROJECT,
    }),
  ],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './vitest.setup.ts',
  },
};
