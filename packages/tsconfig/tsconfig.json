{"compilerOptions": {"types": ["vite-plugin-svgr/client", "vite/client", "vitest/globals"], "target": "ESNext", "module": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "@emotion/react", "incremental": true, "baseUrl": "."}, "exclude": ["node_modules"]}