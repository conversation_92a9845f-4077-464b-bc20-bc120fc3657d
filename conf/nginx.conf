server {
    listen 80;
    root /usr/share/nginx/html;

    # gzip all possible mime types, per default only the ones bigger than  20 bytes will be gzipped
    gzip on;
    gzip_types text/plain text/javascript text/css application/json;

    # Add header for Sentry
    add_header Document-Policy "js-profiling";

    # Cache-Control, ETag, and Last-Modified headers to ensure browser gets the latest resources
    location / {
        # try to resolve static files (like css or images) if not found (like the url path) return the index.html
        try_files $uri $uri/ /index.html;
    }

    # キャッシュ無効化の設定をHTMLファイルにのみ適用
    location ~* \.html$ {
        # Cache-Controlヘッダーを設定
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";

        # ETagを有効にする
        etag on;

        # Last-Modifiedヘッダーを設定
        if_modified_since exact;
        add_header Last-Modified $date_gmt;
    }
}