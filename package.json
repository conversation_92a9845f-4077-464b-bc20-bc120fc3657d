{"name": "picoada-ecois-v2-web", "private": true, "version": "0.0.0", "engines": {"node": ">=22.0.0"}, "scripts": {"dev": "yarn turbo run dev --parallel --no-cache", "dev:local": "yarn turbo run dev:local --parallel --no-cache", "dev:admin": "yarn turbo run dev --parallel --no-cache --scope=admin", "dev:admin:local": "yarn turbo run dev:local --parallel --no-cache --scope=admin", "dev:admin:staging": "yarn turbo run dev:staging --parallel --no-cache --scope=admin", "dev:client": "yarn turbo run dev --parallel --no-cache --scope=client", "dev:client:local": "yarn turbo run dev:local --parallel --no-cache --scope=client", "dev:client:staging": "yarn turbo run dev:staging --parallel --no-cache --scope=client", "build": "yarn turbo run build", "build:admin:production": "yarn turbo run build:production --scope=admin", "build:admin:staging": "yarn turbo run build:staging --scope=admin", "build:admin:local": "yarn turbo run build:local --scope=admin", "build:client:production": "yarn turbo run build:production --scope=client", "build:client:staging": "yarn turbo run build:staging --scope=client", "build:client:local": "yarn turbo run build:local --scope=client", "preview": "yarn turbo run preview", "preview:admin": "yarn turbo run preview --scope=admin", "preview:client": "yarn turbo run preview --scope=client", "test": "yarn turbo run test", "test:admin": "yarn turbo run test --scope=admin", "test:client": "yarn turbo run test --scope=client", "test:watch": "yarn turbo run test:watch", "test:watch:admin": "yarn turbo run test:watch --scope=admin", "test:watch:client": "yarn turbo run test:watch --scope=client", "lint": "yarn eslint apps", "lint:fix": "yarn lint -- --fix", "prettier": "yarn prettier apps --check", "prettier:fix": "yarn prettier -- --write", "format": "yarn prettier:fix && yarn lint:fix"}, "workspaces": ["packages/*", "apps/client", "apps/admin"], "devDependencies": {"@emotion/eslint-plugin": "^11.12.0", "@types/node": "^18.14.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "prettier": "^2.8.4", "turbo": "^1.8.3", "typescript": "^4.9.5", "vite-tsconfig-paths": "^4.2.0"}, "resolutions": {"react": "^18.2.0", "react-dom": "^18.2.0"}}