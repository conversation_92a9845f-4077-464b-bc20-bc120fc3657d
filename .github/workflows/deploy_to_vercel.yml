name: Deploy to Vercel
run-name: Deploy ${{ inputs.application }}:${{ inputs.app_environment }} to Vercel

permissions:
  id-token: write
  contents: read
  pull-requests: write

on:
  workflow_call:
    inputs:
      application:
        description: 'Application Name'
        required: true
        type: string
      app_environment:
        description: 'App Environment Name'
        required: true
        type: string
    secrets:
      vercel_token:
        required: true
      vercel_org_id:
        required: true
      vercel_project_id:
        required: true
    outputs:
      deployment_url:
        description: 'Vercel Deployment URL'
        value: ${{ jobs.deploy_to_vercel.outputs.deployment_url }}

jobs:
  deploy_to_vercel:
    name: Deploy to Vercel
    runs-on: ubuntu-latest

    env:
      VERCEL_ORG_ID: ${{ secrets.vercel_org_id }}
      VERCEL_PROJECT_ID: ${{ secrets.vercel_project_id }}

    outputs:
      deployment_url: ${{ steps.vercel_deploy.outputs.deployment_url }}

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'

      - name: Install Vercel CLI
        run: npm install -g vercel

      - name: Setup Vercel local config
        run: |
          echo "{
            \"buildCommand\": \"yarn build:${{ inputs.application }}:${{ inputs.app_environment }}\",
            \"outputDirectory\": \"apps/${{ inputs.application }}/dist\",
            \"rewrites\": [{\"source\": \"/(.*)\", \"destination\": \"/\"}]
          }" > vercel.json

      - name: Pull Vercel Environment Info
        run: |
          vercel pull --yes \
          --token=${{ secrets.vercel_token }} \
          --local-config vercel.json \
          --environment=production

      - name: Build Project Artifacts for Vercel
        run: |
          vercel build --yes \
          --prod \
          --token=${{ secrets.vercel_token }} \
          --local-config vercel.json

      - name: Deploy Project Artifacts to Vercel
        id: vercel_deploy
        run: |
          DEPLOYMENT_URL=$(vercel deploy --yes \
          --prebuilt \
          --prod \
          --token=${{ secrets.vercel_token }} \
          --local-config vercel.json)
          echo "deployment_url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT

      - name: Comment Deployment URL
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh pr comment ${{ github.event.pull_request.number }} \
            --body "🚀 Deployed to Vercel(${{ inputs.application }} on ${{ inputs.app_environment }}): ${{ steps.vercel_deploy.outputs.deployment_url }}"
