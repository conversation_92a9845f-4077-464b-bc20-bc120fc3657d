name: Check Build and Deploy on Preview

on:
  workflow_dispatch:
  pull_request:

jobs:
  set_env:
    name: Set Environment
    runs-on: ubuntu-latest
    env:
      environment: preview # as a infra
      app_environment: staging # as a application
    outputs:
      environment: ${{ env.environment }}
      app_environment: ${{ env.app_environment }}
      matrix: ${{ steps.set_matrix.outputs.matrix }}
    steps:
      - name: Set Dynamic Matrix
        id: set_matrix
        run: |
          object=$(jq -c . <<EOF
          {
            "application": [
              {
                "name": "admin",
                "aws_ecr_repository": "AWS_ECR_REPOSITORY_ADMIN_NAME",
                "vercel_project_id": "VERCEL_PROJECT_ID_ADMIN"
              },
              {
                "name": "client",
                "aws_ecr_repository": "AWS_ECR_REPOSITORY_CLIENT_NAME",
                "vercel_project_id": "VERCEL_PROJECT_ID_CLIENT"
              }
            ]
          }
          EOF
          )
          echo "$object"
          echo "matrix=$object" >> $GITHUB_OUTPUT


  # build_check:
  #   needs: set_env
  #   strategy:
  #     matrix: ${{ fromJson(needs.set_env.outputs.matrix) }}
  #   uses: ./.github/workflows/build_and_push.yml
  #   with:
  #     application: ${{ matrix.application.name }}
  #     environment: ${{ needs.set_env.outputs.environment }}
  #     app_environment: ${{ needs.set_env.outputs.app_environment }}
  #     push: false
  #   secrets:
  #     role_to_assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
  #     aws_region: ${{ secrets.AWS_REGION }}
  #     aws_ecr_repository: ${{ secrets[matrix.application.aws_ecr_repository] }}
  #     sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
  #     sentry_dsn: ${{ secrets.SENTRY_DSN }}
  #     sentry_organization: ${{ secrets.SENTRY_ORG }}
  #     sentry_project: ${{ secrets.SENTRY_PROJECT }}


  deploy_to_vercel:
    needs: set_env
    strategy:
      matrix: ${{ fromJson(needs.set_env.outputs.matrix) }}
    uses: ./.github/workflows/deploy_to_vercel.yml
    with:
      application: ${{ matrix.application.name }}
      app_environment: ${{ needs.set_env.outputs.app_environment }}
    secrets:
      vercel_token: ${{ secrets.VERCEL_TOKEN }}
      vercel_org_id: ${{ secrets.VERCEL_TEAM_ID }}
      vercel_project_id: ${{ secrets[matrix.application.vercel_project_id] }}
