name: Deploy to ECS

permissions:
  id-token: write
  contents: read

on:
  workflow_call:
    inputs:
      environment:
        description: "Environment Name"
        required: true
        type: string
      aws_ecs_task_definition:
        description: "ECS Task Difinition Name"
        required: true
        type: string
      aws_ecs_service:
        description: "App ECS Service Name"
        required: true
        type: string
      aws_ecs_cluster:
        description: "App ECS Cluster Name"
        required: true
        type: string
      container:
        description: "Docker Container Name"
        required: true
        type: string

    secrets:
      role_to_assume:
        required: true
      aws_region:
        required: true
      aws_ecr_repository:
        required: true

jobs:
  deploy_to_ecs:
    name: Deploy to ECS
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ secrets.role_to_assume }}
        aws-region: ${{ secrets.aws_region }}
        role-session-name: GitHubActions-${{ github.run_id }}

    - name: Login to Amazon ECR
      id: login_ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Download previous task definition
      run: |
        aws ecs describe-task-definition --task-definition ${{ inputs.aws_ecs_task_definition }} --include TAGS --query taskDefinition > ${{ inputs.aws_ecs_task_definition }}.json

    - name: Remove unnecessary properties from task definition
      run: |
        jq 'del(.taskDefinitionArn, .compatibilities, .requiresAttributes, .revision, .status, .registeredAt, .registeredBy)' ${{ inputs.aws_ecs_task_definition }}.json > new_ecs_task_definition.json

    - name: Render ECS task definition
      id: render_app_container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: new_ecs_task_definition.json
        container-name: ${{ inputs.container }}
        image: ${{ steps.login_ecr.outputs.registry }}/${{ secrets.aws_ecr_repository }}:${{ inputs.environment }}

    - name: Deploy ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.render_app_container.outputs.task-definition }}
        service: ${{ inputs.aws_ecs_service }}
        cluster: ${{ inputs.aws_ecs_cluster }}
        wait-for-service-stability: true
