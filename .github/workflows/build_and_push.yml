name: Build and Push

permissions:
  id-token: write
  contents: read

on:
  workflow_call:
    inputs:
      application:
        description: "Application Name"
        required: true
        type: string
      environment:
        description: "Environment Name"
        required: true
        type: string
      app_environment:
        description: "App Environment Name"
        required: true
        type: string
      context:
        description: "Path Context"
        default: .
        required: false
        type: string
      dockerfile:
        description: "Dockerfile path"
        default: Dockerfile
        required: false
        type: string
      platforms:
        description: "Docker Platforms"
        default: linux/amd64,linux/arm64
        required: false
        type: string
      push:
        description: "Push to ECR"
        default: true
        required: false
        type: boolean

    secrets:
      role_to_assume:
        required: true
      aws_region:
        required: true
      aws_ecr_repository:
        required: true
      sentry_auth_token:
        required: true
      sentry_dsn:
        required: true
      sentry_organization:
        required: true
      sentry_project:
        required: true

    outputs:
      imageid:
        description: "Docker Image ID"
        value: ${{ jobs.build_and_push.outputs.imageid }}
      digest:
        description: "Docker Image Digest"
        value: ${{ jobs.build_and_push.outputs.digest }}

jobs:
  build_and_push:
    name: Build and Push
    runs-on: ubuntu-latest

    outputs:
      imageid: ${{ steps.build_and_push.outputs.imageid }}
      digest: ${{ steps.build_and_push.outputs.digest }}

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ secrets.role_to_assume }}
        aws-region: ${{ secrets.aws_region }}
        role-session-name: GitHubActions-${{ github.run_id }}

    - name: Login to Amazon ECR
      id: login_ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Docker meta
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ steps.login_ecr.outputs.registry }}/${{ secrets.aws_ecr_repository }}
        tags: |
          type=sha,format=long
          type=raw,value=latest
          type=raw,value=${{ inputs.environment }}
          type=ref,event=tag

    - name: Build and Push to ECR
      id: build_and_push
      uses: docker/build-push-action@v6
      env:
        cache_ref: ${{ steps.login_ecr.outputs.registry }}/${{ secrets.aws_ecr_repository }}:cache
      with:
        context: ${{ inputs.context }}
        file: ${{ inputs.dockerfile }}
        cache-from: type=registry,ref=${{ env.cache_ref }}
        cache-to: type=registry,ref=${{ env.cache_ref }},mode=max,image-manifest=true,oci-mediatypes=true
        platforms: ${{ inputs.platforms }}
        push: ${{ inputs.push }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          app=${{ inputs.application }}
          env=${{ inputs.app_environment }}
          sentry_auth_token=${{ secrets.sentry_auth_token }}
          sentry_dsn=${{ secrets.sentry_dsn }}
          sentry_organization: ${{ secrets.sentry_organization }}
          sentry_project: ${{ secrets.sentry_project }}
