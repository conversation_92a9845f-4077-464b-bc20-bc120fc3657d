name: Release

permissions:
  id-token: write
  contents: write

on:
  workflow_call:
    inputs:
      environment:
        description: "Environment Name"
        required: true
        type: string

    secrets:
      sentry_integration_token:
        required: true
      sentry_organization:
        required: true
      sentry_project:
        required: true

jobs:
  release_to_git:
    name: Create New Release to Git
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Create release by Tag
      uses: softprops/action-gh-release@v2
      if: ${{ startsWith( github.ref, 'refs/tags/' ) }}
      with:
        draft: false
        prerelease: false
        generate_release_notes: true

  release_to_sentry:
    name: Create New Release to Sentry
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Create Sentry release
      uses: getsentry/action-release@v1
      if: ${{ !startsWith( github.ref, 'refs/tags/' ) }}
      env:
        SENTRY_AUTH_TOKEN: ${{ secrets.sentry_integration_token }}
        SENTRY_ORG: ${{ secrets.sentry_organization }}
        SENTRY_PROJECT: ${{ secrets.sentry_project }}
      with:
        environment: ${{ inputs.environment }}
        version: ${{ github.sha }}

    - name: Create Sentry release by Tag
      uses: getsentry/action-release@v1
      if: ${{ startsWith( github.ref, 'refs/tags/' ) }}
      env:
        SENTRY_AUTH_TOKEN: ${{ secrets.sentry_integration_token }}
        SENTRY_ORG: ${{ secrets.sentry_organization }}
        SENTRY_PROJECT: ${{ secrets.sentry_project }}
      with:
        environment: ${{ inputs.environment }}
        version: ${{ github.ref }}
