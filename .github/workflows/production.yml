name: Deploy on Production

on:
  push:
    tags:
    - "[0-9]+.[0-9]+.r[0-9]+"  # e.g. 2024.01.r1

jobs:
  set_env:
    name: Set Environment
    runs-on: ubuntu-latest
    env:
      environment: production      # as a infra
      app_environment: production  # as a application
    outputs:
      environment: ${{ env.environment }}
      app_environment: ${{ env.app_environment }}
      aws_ecs_cluster: "core-${{ env.environment }}-web"
      matrix: ${{ steps.set_matrix.outputs.matrix }}
    steps:
      - name: Set Dynamic Matrix
        id: set_matrix
        run: |
          object=$(jq -c . <<EOF
          {
            "application": [
              {
                "name": "admin",
                "aws_ecr_repository": "AWS_ECR_REPOSITORY_ADMIN_NAME",
                "aws_ecs_task_definition": "core-${{ env.environment }}-web-admin"
              },
              {
                "name": "client",
                "aws_ecr_repository": "AWS_ECR_REPOSITORY_CLIENT_NAME",
                "aws_ecs_task_definition": "core-${{ env.environment }}-web-client"
              }
            ]
          }
          EOF
          )
          echo "$object"
          echo "matrix=$object" >> $GITHUB_OUTPUT

  build:
    needs: set_env
    strategy:
      matrix: ${{ fromJson(needs.set_env.outputs.matrix) }}
    uses: ./.github/workflows/build_and_push.yml
    with:
      application: ${{ matrix.application.name }}
      environment: ${{ needs.set_env.outputs.environment }}
      app_environment: ${{ needs.set_env.outputs.app_environment }}
      platforms: linux/arm64
    secrets:
      role_to_assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
      aws_region: ${{ secrets.AWS_REGION }}
      aws_ecr_repository: ${{ secrets[matrix.application.aws_ecr_repository] }}
      sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
      sentry_dsn: ${{ secrets.SENTRY_DSN }}
      sentry_organization: ${{ secrets.SENTRY_ORG }}
      sentry_project: ${{ secrets.SENTRY_PROJECT }}

  release:
    uses: ./.github/workflows/release.yml
    needs: [set_env, build]
    with:
      environment: ${{ needs.set_env.outputs.environment }}
    secrets:
      sentry_integration_token: ${{ secrets.SENTRY_INTEGRATION_TOKEN }}
      sentry_organization: ${{ secrets.SENTRY_ORG }}
      sentry_project: ${{ secrets.SENTRY_PROJECT }}

  deploy:
    needs: [set_env, build, release]
    strategy:
      matrix: ${{ fromJson(needs.set_env.outputs.matrix) }}
    uses: ./.github/workflows/deploy_to_ecs.yml
    with:
      environment: ${{ needs.set_env.outputs.environment }}
      aws_ecs_task_definition: ${{ matrix.application.aws_ecs_task_definition }}
      aws_ecs_service: ${{ matrix.application.name }}
      aws_ecs_cluster: ${{ needs.set_env.outputs.aws_ecs_cluster }}
      container: app
    secrets:
      role_to_assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
      aws_region: ${{ secrets.AWS_REGION }}
      aws_ecr_repository: ${{ secrets[matrix.application.aws_ecr_repository] }}
